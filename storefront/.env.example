# Your Medusa backend, should be updated to where you are hosting your server. Remember to update CORS settings for your server. See – https://docs.medusajs.com/usage/configurations#admin_cors-and-store_cors
NEXT_PUBLIC_MEDUSA_BACKEND_URL=http://localhost:9000
# !!! UPDATE IMAGE REMOTE PATTERNS IN next.config.js !!!

# Your publishable key that can be attached to sales channels. See - https://docs.medusajs.com/development/publishable-api-keys
NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=

# Your store URL, should be updated to where you are hosting your storefront.
NEXT_PUBLIC_BASE_URL=http://localhost:8000
NEXT_PUBLIC_STORE_URL=http://localhost:8000

# Your Stripe public key. See – https://docs.medusajs.com/add-plugins/stripe
NEXT_PUBLIC_STRIPE_KEY=
NEXT_PUBLIC_LOGROCKET_KEY=

# Your Next.js revalidation secret. See – https://nextjs.org/docs/app/building-your-application/data-fetching/fetching-caching-and-revalidating#on-demand-revalidation
REVALIDATE_SECRET=supersecret

NEXT_PUBLIC_GA_ID=G-XYZ
NEXT_PUBLIC_GTM_ID=GTM-XYZ
NEXT_PUBLIC_ZIPY_API_KEY=123

