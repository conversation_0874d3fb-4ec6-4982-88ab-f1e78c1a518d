{"name": "medusa-next", "version": "1.0.3", "private": true, "author": "<PERSON><PERSON> <<EMAIL>> & <PERSON> <<EMAIL>> (https://www.medusajs.com)", "description": "Next.js Starter to be used with Medusa V2", "keywords": ["medusa-storefront"], "scripts": {"dev": "next dev --turbopack -p 8000", "build": "next build", "start": "next start -p 8000", "lint": "next lint", "analyze": "ANALYZE=true next build"}, "dependencies": {"@headlessui/react": "^2.2.0", "@hookform/resolvers": "^4.1.3", "@medusajs/js-sdk": "latest", "@medusajs/ui": "latest", "@next/third-parties": "^15.2.4", "@radix-ui/react-accordion": "^1.2.1", "@stripe/react-stripe-js": "^1.7.2", "@stripe/stripe-js": "^1.29.0", "@tailwindcss/postcss": "^4.0.0", "@tanstack/react-query": "^5.64.0", "@uidotdev/usehooks": "^2.4.1", "clsx": "^2.1.1", "dompurify": "^3.2.3", "embla-carousel": "^8.5.2", "embla-carousel-react": "^8.5.2", "lodash": "^4.17.21", "logrocket": "^10.0.0", "lucide-react": "^0.471.1", "lz-string": "^1.5.0", "next": "^15.2.4", "nuqs": "^2.3.0", "pg": "^8.11.3", "plaiceholder": "^3.0.0", "qs": "^6.12.1", "react": "19.0.0-rc-66855b96-20241106", "react-country-flag": "^3.1.0", "react-day-picker": "^9.5.1", "react-dom": "19.0.0-rc-66855b96-20241106", "react-hook-form": "^7.54.2", "react-intersection-observer": "^9.16.0", "server-only": "^0.0.1", "tailwindcss-radix": "^2.8.0", "webpack": "^5", "zipyai": "^1.7.41", "zod": "^3.24.2"}, "devDependencies": {"@babel/core": "^7.17.5", "@medusajs/types": "latest", "@medusajs/ui-preset": "latest", "@svgr/webpack": "^8.1.0", "@types/lodash": "^4.14.195", "@types/node": "17.0.21", "@types/pg": "^8.11.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-instantsearch-dom": "^6.12.3", "ansi-colors": "^4.1.3", "autoprefixer": "^10.4.2", "babel-loader": "^8.2.3", "eslint": "8.10.0", "eslint-config-next": "15.0.3", "postcss": "^8.5.1", "prettier": "^2.8.8", "tailwindcss": "^3.4.17", "typescript": "^5.3.2"}, "packageManager": "yarn@3.2.3", "resolutions": {"@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1"}, "overrides": {"react": "19.0.0-rc-66855b96-20241106", "react-dom": "19.0.0-rc-66855b96-20241106"}}