"use client"

import useCart from "hooks/useCart"
import useExchangeRate from "hooks/useExchangeRate"
import { createContext, useContext, useMemo } from "react"
import { ExchangeRate, ExtendedCart } from "types/cart"

type CartContextType = {
  cart: ExtendedCart | null | "fetching"
  cartId: string | null
  exchangeRate: ExchangeRate | null
  isLoading: boolean
  error: Error | null
  isCartFull: boolean
  clearCart: () => void
  refetch: () => void
  deactivateCart: () => void
  setCartId: (cartId: string) => void
}

const CartContext = createContext<CartContextType>({
  cart: "fetching",
  cartId: null,
  exchangeRate: null,
  isLoading: false,
  error: null,
  isCartFull: false,
  setCartId: () => {},
  refetch: () => {},
  clearCart: () => {},
  deactivateCart: () => {},
})

const CartContextProvider = ({ children }: { children: React.ReactNode }) => {
  const {
    cart,
    cartId,
    isLoading,
    error,
    setCartId,
    refetch,
    clearCart,
    isCartFull,
    deactivateCart,
  } = useCart() || {
    cart: "fetching",
    isLoading: false,
    isCartFull: false,
    error: null,
    setCartId: () => {},
    refetch: () => {},
    clearCart: () => {},
    deactivateCart: () => {},
  }

  let exchangeRate: ExchangeRate | null = null
  const { data: exchangeRateData } = useExchangeRate()

  if (exchangeRateData) {
    exchangeRate = exchangeRateData
  }

  const memoizedValues = useMemo(
    () => ({
      cart,
      cartId,
      exchangeRate,
      isLoading,
      error,
      refetch,
      clearCart,
      isCartFull,
      setCartId,
      deactivateCart,
    }),
    [
      cart,
      cartId,
      exchangeRate,
      isLoading,
      error,
      refetch,
      clearCart,
      isCartFull,
      refetch,
      setCartId,
      deactivateCart,
    ]
  )

  return (
    <CartContext.Provider value={memoizedValues}>
      {children}
    </CartContext.Provider>
  )
}

const useCartContext = () => {
  const context = useContext(CartContext)
  if (!context) {
    throw new Error("useCartContext must be used within a CartContextProvider")
  }
  return context
}

export { CartContextProvider, useCartContext }
