"use client"

import { createContext, useContext } from "react"
import useC<PERSON>Timer, { CartTimerState } from "hooks/useCartTimer"

const CartTimerContext = createContext<CartTimerState | null>(null)

export const CartTimerProvider = ({
  children,
}: {
  children: React.ReactNode
}) => {
  const cartTimer = useCartTimer()

  return (
    <CartTimerContext.Provider value={cartTimer}>
      {children}
    </CartTimerContext.Provider>
  )
}

export const useCartTimerContext = () => {
  const context = useContext(CartTimerContext)
  if (!context) {
    throw new Error(
      "useCartTimerContext must be used within a CartTimerProvider"
    )
  }
  return context
}

export default CartTimerContext
