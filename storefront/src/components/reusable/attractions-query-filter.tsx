// import { Input } from "@headlessui/react"
import { useAttractionsSearchContext } from "contexts/AttractionsSearchContext"
import React from "react"
import { X, Search, Loader2 } from "lucide-react"
import { Input } from "@headlessui/react"
import clsx from "clsx"

type Props = {}

const AttractionsQueryFilter = (props: Props) => {
  const { query, setQuery, isLoading, clearSearch, hasActiveSearch } =
    useAttractionsSearchContext()

  return (
    <div className="relative flex items-center gap-2 grow">
      <div className="relative flex items-center grow w-full">
        <Search className="absolute left-5 w-4 h-4 text-gray-50 pointer-events-none" />

        <Input
          type="text"
          placeholder="Wyszukaj atrakcję wg nazwy..."
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className={clsx(
            "w-full pl-11 h-12 pr-10  py-2 border-2 outline-none text-sm sm:text-base  rounded-xxl border-secondary",
            "placeholder:text-gray-50 ",
            "transition-all",
            "focus:outline-none focus:ring-2 focus:ring-secondary"
          )}
        />

        {query && query.length < 2 && (
          <div className="absolute right-5 top-1/2 -translate-y-1/2 text-gray-50 pointer-events-none text-sm sm:text-base">
            Wpisz co najmniej 3 znaki
          </div>
        )}

        {isLoading && (
          <Loader2 className="absolute right-8 w-4 h-4 text-gray-50 animate-spin" />
        )}

        {hasActiveSearch && !isLoading && (
          <button
            onClick={clearSearch}
            className="absolute right-3 p-1 hover:bg-gray-100 rounded-full transition-colors"
            aria-label="Wyczyść wyszukiwanie"
          >
            <X className="w-3 h-3 text-gray-50" />
          </button>
        )}
      </div>
    </div>
  )
}

export default AttractionsQueryFilter
