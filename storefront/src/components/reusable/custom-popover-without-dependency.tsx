import React, { useState, useRef, useEffect } from "react"
import clsx from "clsx"
import useMobile from "hooks/useMobile"

type Props = {
  children:
    | ((close: () => void) => React.ReactNode)
    | React.ReactElement
    | React.ReactNode
  popoverButtonContent: React.ReactNode
  variant?: "primary" | "transparent"
  fitWidthPanel?: boolean
  triggerAsDiv?: boolean
  headless?: boolean
  className?: string
  customWidth?: string
}

const CustomPopoverWithoutDependency = ({
  children,
  popoverButtonContent,
  variant,
  triggerAsDiv = false,
  fitWidthPanel,
  headless = false,
  className,
  customWidth,
}: Props) => {
  const [isOpen, setIsOpen] = useState(false)
  const [popoverStyle, setPopoverStyle] = useState<React.CSSProperties>({
    boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
  })
  const popoverRef = useRef<HTMLDivElement>(null)
  const buttonRef = useRef<HTMLDivElement | HTMLButtonElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const isMobile = useMobile()

  const togglePopover = () => {
    setIsOpen(!isOpen)
  }

  const closePopover = () => {
    setIsOpen(false)
  }

  // Position the popover to prevent overflow
  useEffect(() => {
    if (isOpen && popoverRef.current && buttonRef.current) {
      const updatePosition = () => {
        const buttonRect = buttonRef.current?.getBoundingClientRect()
        const popoverRect = popoverRef.current?.getBoundingClientRect()

        if (!buttonRect || !popoverRect) return

        const viewportHeight = window.innerHeight
        const viewportWidth = window.innerWidth
        const spaceBelow = viewportHeight - buttonRect.bottom
        const newStyle: React.CSSProperties = {
          boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
        }

        // Mobile positioning
        if (isMobile) {
          const margin = 16 // 1rem
          newStyle.width = `calc(100vw - ${margin * 2}px)`

          // Position at left edge of screen with margin
          newStyle.left =
            "calc(0px - " + buttonRect.left + "px + " + margin + "px)"
          newStyle.transform = "none" // Remove centering
        }

        setPopoverStyle(newStyle)
      }

      updatePosition()
      window.addEventListener("resize", updatePosition)

      return () => {
        window.removeEventListener("resize", updatePosition)
      }
    }
  }, [isOpen, isMobile, fitWidthPanel])

  // Close popover when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        popoverRef.current &&
        buttonRef.current &&
        !popoverRef.current.contains(event.target as Node) &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [isOpen])

  // Handle escape key press
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener("keydown", handleEscapeKey)
    }

    return () => {
      document.removeEventListener("keydown", handleEscapeKey)
    }
  }, [isOpen])

  const ButtonComponent = triggerAsDiv ? "div" : "button"

  return (
    <div className={clsx("relative", className)} ref={containerRef}>
      <ButtonComponent
        ref={buttonRef as any}
        onClick={togglePopover}
        className={
          headless
            ? ""
            : `py-2 border rounded-full w-full text-left px-6 text-lg font-semibold ${
                variant === "transparent"
                  ? "bg-transparent text-white"
                  : "bg-white text-primary"
              }`
        }
      >
        {popoverButtonContent}
      </ButtonComponent>

      {isOpen && (
        <div
          ref={popoverRef}
          style={popoverStyle}
          className={clsx(
            "z-[50] overflow-visible absolute top-full mt-4 flex flex-col gap-2 bg-white rounded-lg p-4 border animate-scaleIn",
            {
              "!w-fit ml-auto": fitWidthPanel,
              "w-full": !fitWidthPanel && !isMobile,
              "max-h-[unset]": headless,
            },
            customWidth
          )}
        >
          {typeof children === "function" ? children(closePopover) : children}
        </div>
      )}
    </div>
  )
}

export default CustomPopoverWithoutDependency
