import { Dialog, DialogBackdrop, DialogPanel } from "@headlessui/react"
import Link from "next/link"
import { ReactNode } from "react"
import Button from "./button"

type Props = {
  isOpen: boolean
  onClose: () => void
  timer?: string
  icon: ReactNode
  title: string
  description: ReactNode
  ctaText: string
  ctaHref: string
  ctaAs?: "button" | "link"
  className?: string
  children?: ReactNode
}

export const TimedStatusPopover = ({
  isOpen,
  onClose,
  timer,
  icon,
  title,
  description,
  ctaText,
  ctaHref,
  ctaAs = "link",
  className = "",
  children,
}: Props) => {
  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <DialogBackdrop
        className="fixed inset-0 bg-black/30"
        aria-hidden="true"
      />
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <DialogPanel
          className={`w-full max-w-2xl rounded-xxl bg-background p-6 sm:px-14 sm:py-10 text-center ${className}`}
        >
          {timer && <div className="text-xl font-medium mb-2">{timer}</div>}

          <div className="mx-auto w-fit mb-4">{icon}</div>

          <h2 className="text-xl font-bold mb-4">{title}</h2>

          <div className="text-sm text-gray-50 mb-6">{description}</div>

          <div className="flex items-center justify-around">
            {children}

            {ctaAs === "link" && (
              <Link
                href={ctaHref}
                className="inline-block mt-4 px-6 py-2 bg-background border-secondary border rounded-full text-sm font-medium transition-colors"
                onClick={onClose}
              >
                {ctaText}
              </Link>
            )}

            {ctaAs === "button" && (
              <Button
                variant="outline"
                className="mt-4 min-w-40"
                onClick={onClose}
              >
                {ctaText}
              </Button>
            )}
          </div>
        </DialogPanel>
      </div>
    </Dialog>
  )
}
