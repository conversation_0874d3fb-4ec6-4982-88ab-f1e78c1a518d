import { Dialog, DialogBackdrop, DialogPanel } from "@headlessui/react"
import { ReactNode } from "react"

type LoadingDialogProps = {
  isOpen: boolean
  onClose: () => void
  children: ReactNode
  className?: string
}

const LoadingDialog = ({
  isOpen,
  onClose,
  children,
  className = "",
}: LoadingDialogProps) => {
  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <DialogBackdrop
        className="fixed inset-0 bg-black/30"
        aria-hidden="true"
      />
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <DialogPanel
          className={`w-full max-w-2xl rounded-xxl bg-background p-6 sm:px-14 sm:py-10 flex items-center justify-center ${className}`}
        >
          {children}
        </DialogPanel>
      </div>
    </Dialog>
  )
}

export default LoadingDialog
