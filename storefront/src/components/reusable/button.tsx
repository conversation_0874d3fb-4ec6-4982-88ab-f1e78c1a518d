import clsx from "clsx"
import { Loader2 } from "lucide-react"
import React from "react"

type Props = {
  isLoading?: boolean
  children: React.ReactNode
  className?: string
  variant?: "primary" | "outline"
  onClick?: () => void
  cta?: boolean
  disabled?: boolean
}

const Button = ({
  isLoading,
  children,
  variant,
  className,
  onClick,
  cta,
  disabled,
}: Props) => {
  const defaultButtonClasses = "rounded-full px-4 py-2"

  const ctaClasses = `
    relative overflow-hidden  
    transition-all duration-500
    hover:shadow-lg hover:shadow-secondary/20
    ${isLoading ? "opacity-70" : ""}
    before:absolute before:inset-0
    before:bg-white/20 before:translate-x-[-100%]
    hover:before:animate-shine
    active:scale-[0.98] 
    disabled:cursor-not-allowed
    group
    `

  const buttonClasses = {
    primary: "bg-secondary text-primary",
    outline: "border border-secondary text-primary",
  }

  const resolvedVariant = buttonClasses[variant ?? "primary"]

  return (
    <button
      className={clsx(
        defaultButtonClasses,
        className,
        isLoading ? "opacity-50" : "",
        resolvedVariant,
        cta && ctaClasses
      )}
      onClick={onClick}
      disabled={disabled}
    >
      {isLoading ? (
        <div className="min-h-6 grid place-content-center">
          <Loader2 className="h-4 w-4 m-auto animate-spin" />
        </div>
      ) : (
        children
      )}
    </button>
  )
}

export default Button
