import React from "react"
import Image from "next/image"
import { Button } from "@medusajs/ui"
import CartIcon from "@icons/cart_black.svg"

interface AnimatedCartButtonProps {
  isLoading?: boolean
  isSuccess?: boolean
  disabled?: boolean
  buttonText?: string
  successText?: string
}

const AnimatedCartButton: React.FC<AnimatedCartButtonProps> = ({
  isLoading = false,
  isSuccess = false,
  disabled = false,
  buttonText = "Dodaj do koszyka",
  successText = "Dodano!",
}) => {
  return (
    <Button
      type="submit"
      disabled={isLoading || disabled}
      className={`
        relative overflow-hidden
        bg-secondary px-8 py-2 rounded-full
        flex items-center gap-2
        transition-all duration-500
        hover:shadow-lg hover:shadow-secondary/20
        ${isLoading ? "opacity-70" : ""}
        ${disabled ? "opacity-50 cursor-not-allowed" : ""}
        ${isSuccess ? "bg-green" : ""}
        before:absolute before:inset-0
        before:bg-white/20 before:translate-x-[-100%]
        hover:before:animate-shine
        active:scale-[0.98] 
        h-12
        disabled:cursor-not-allowed
        group
      `}
    >
      <div className="relative w-5 h-5">
        <div
          className={`
            absolute inset-0 
            transition-all duration-500
            ${
              isSuccess
                ? "opacity-0 rotate-[-90deg] scale-0"
                : "opacity-100 rotate-0 scale-100"
            }
          `}
        >
          <Image
            src={CartIcon}
            alt="cart"
            width={20}
            height={20}
            className="group-hover:animate-wiggle"
          />
        </div>
        <svg
          className={`
            absolute inset-0 w-5 h-5
            stroke-current
            transition-all duration-500
            ${
              isSuccess
                ? "opacity-100 rotate-0 scale-100"
                : "opacity-0 rotate-[90deg] scale-0"
            }
          `}
          viewBox="0 0 20 20"
          fill="none"
          strokeWidth="2"
        >
          <path
            d="M4 10l4 4L16 6"
            strokeLinecap="round"
            strokeLinejoin="round"
            className={isSuccess ? "animate-draw-check" : ""}
          />
        </svg>
      </div>
      <span
        className={`
          transition-all duration-500
          ${isSuccess ? "translate-y-0" : "translate-y-0"}
        `}
      >
        {isSuccess ? successText : buttonText}
      </span>
    </Button>
  )
}

export default AnimatedCartButton
