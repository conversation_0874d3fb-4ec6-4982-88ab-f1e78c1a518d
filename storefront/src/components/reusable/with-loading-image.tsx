"use client"

import clsx from "clsx"
import useLoadingImage from "hooks/useLoadingImage"
import Image from "next/image"
import React, { ComponentProps } from "react"

type Props = {
  imageSrc: string | ""
} & Omit<ComponentProps<typeof Image>, "src" | "onLoad" | "placeholder">

const WithLoadingImage = ({ imageSrc, ...props }: Props) => {
  const { imageLoaded, handleImageLoad } = useLoadingImage()

  const { className, ...rest } = props

  return (
    <>
      {imageSrc === "" ? null : (
        <Image
          src={imageSrc}
          blurDataURL={`/_next/image?url=${imageSrc}&w=16&q=1`}
          placeholder="blur"
          loading="lazy"
          className={clsx(
            className,
            imageLoaded ? "opacity-100 blur-none" : "opacity-80 blur-sm"
          )}
          onLoad={handleImageLoad}
          {...rest}
        />
      )}
    </>
  )
}

export default WithLoadingImage
