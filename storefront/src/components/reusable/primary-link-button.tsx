import Link from "next/link"
import React, { ComponentProps } from "react"
import Button from "./button"
import clsx from "clsx"

type Props = {
  href: string
  children: React.ReactNode
} & ComponentProps<typeof Link>

export const PrimaryLinkButton = ({ href, children, ...props }: Props) => {
  return (
    <Link
      href={href}
      className={clsx(
        "h-12 px-8 max-md:grow bg-secondary rounded-[40px] border-2 border-secondary text-primary grid place-content-center",
        props.className
      )}
      {...props}
    >
      {children}
    </Link>
  )
}

type PrimaryButtonProps = {
  children: React.ReactNode
  onClick?: () => void
} & ComponentProps<typeof Button>

export const PrimaryButton = ({
  children,
  className,
  ...props
}: PrimaryButtonProps) => {
  return (
    <Button
      className={clsx(
        "h-12 px-8 max-md:grow bg-secondary rounded-[40px] border-2 border-secondary text-primary grid place-content-center",
        className
      )}
      {...props}
    >
      {children}
    </Button>
  )
}
