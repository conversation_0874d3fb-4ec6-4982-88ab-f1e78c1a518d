import { SOCIAL_LINKS } from "@lib/constants/socialLinks"
import SocialMediaTile from "./social-media-tile"
import { Icons } from "./Icons"
import SocialLinks from "./social-links"
import { YoutubeColor } from "@icons/youtube_color"
import { FacebookColor } from "@icons/facebook_color"
import InstagramColor from "@icons/instagram_color.svg"
import InstagramGray from "@icons/instagram_gray.svg"
import TiktokColor from "@icons/tiktok_color.svg"
import TiktokGray from "@icons/tiktok_gray.svg"

interface IProps {
  isContact?: boolean
}

const OurSocialMedia = ({ isContact }: IProps) => {
  return (
    <section className="py-20 px-[50px] 2xl:px-0 flex flex-col gap-12 md:flex-row-reverse md:justify-between max-w-[1440px] mx-auto container">
      <div className="flex flex-col gap-12">
        <h2 className="text-heading_secondary font-bold">Nasze Social Media</h2>
        <div
          className={`flex flex-wrap gap-1 self-center md:w-full ${
            isContact
              ? "flex-wrap justify-start gap-4 w-[320px]"
              : "md:flex-wrap md:justify-start md:gap-4 md:w-[320px]"
          }`}
        >
          <SocialMediaTile
            href={SOCIAL_LINKS[1].href}
            name={SOCIAL_LINKS[1].name}
            Svg={FacebookColor}
            className={
              isContact
                ? "!min-w-[150px] !min-h-[150px]"
                : "md:min-w-[150px] md:min-h-[150px]"
            }
            iconClassName={
              isContact
                ? "!w-10 !h-10 group-hover:fill-[#1873EB]"
                : "md:w-10 md:h-10 group-hover:fill-[#1873EB]"
            }
            captionClassName={isContact ? "!text-lg" : "md:text-lg"}
          />
          <SocialMediaTile
            href={SOCIAL_LINKS[3].href}
            name={SOCIAL_LINKS[3].name}
            svgAsImg={InstagramColor}
            svgHover={InstagramGray}
            className={
              isContact
                ? "!min-w-[150px] !min-h-[150px]"
                : "md:min-w-[150px] md:min-h-[150px]"
            }
            iconClassName={isContact ? "!w-10 !h-10" : "md:w-10 md:h-10"}
            captionClassName={isContact ? "!text-lg" : "md:text-lg"}
          />
          <SocialMediaTile
            href={SOCIAL_LINKS[2].href}
            name={SOCIAL_LINKS[2].name}
            svgAsImg={TiktokColor}
            svgHover={TiktokGray}
            className={
              isContact
                ? "!min-w-[150px] !min-h-[150px]"
                : "md:min-w-[150px] md:min-h-[150px]"
            }
            iconClassName={isContact ? "!w-10 !h-10" : "md:w-10 md:h-10"}
            captionClassName={isContact ? "!text-lg" : "md:text-lg"}
          />
          <SocialMediaTile
            href={SOCIAL_LINKS[5].href}
            name={SOCIAL_LINKS[5].name}
            Svg={FacebookColor}
            className={
              isContact
                ? "!min-w-[150px] !min-h-[150px]"
                : "md:min-w-[150px] md:min-h-[150px]"
            }
            iconClassName={
              isContact
                ? "!w-10 !h-10 group-hover:fill-[#1873EB]"
                : "md:w-10 md:h-10 group-hover:fill-[#1873EB]"
            }
            captionClassName={isContact ? "!text-lg" : "md:text-lg"}
          />
          <SocialMediaTile
            href={SOCIAL_LINKS[0].href}
            name={SOCIAL_LINKS[0].name}
            Svg={YoutubeColor}
            className={
              isContact
                ? "!min-w-[150px] !min-h-[150px]"
                : "md:min-w-[150px] md:min-h-[150px]"
            }
            iconClassName={
              isContact
                ? "!w-10 !h-10 group-hover:fill-[#D41818]"
                : "md:w-10 md:h-10 group-hover:fill-[#D41818]"
            }
            captionClassName={isContact ? "!text-lg" : "md:text-lg"}
          />
        </div>
      </div>
      <div className="flex flex-col gap-12 justify-between">
        <div className="flex flex-col gap-6 md:gap-9">
          <div className="flex gap-3 items-center">
            <Icons.Mail className="w-4 h-4" />
            <a
              href="mailto:<EMAIL>?subject=Zapytanie&body=Cześć, chciałbym zapytać o..."
              className="text-base-semi md:text-xl"
            >
              <EMAIL>
            </a>
          </div>
          <p className="pl-9 text-xsmall-light max-w-[212px] text-gray-50 text-[11px] leading-5 md:pl-0 md:max-w-[434px] md:text-base">
            Jeśli chcesz się z nami skontaktować, kliknij poniższe ikony
            messengera lub instagrama.
          </p>
        </div>
        <SocialLinks
          iconsClassName="w-7 h-7 md:w-4 md:h-4"
          isShowFacebook={false}
        />
        <article className="self-end flex flex-col gap-1 md:self-start md:gap-3">
          <h4 className="text-base-semi text-black md:text-xl-semi">
            LWS Group S.R.O.
          </h4>
          <p className="text-gray-50 text-small-regular md:text-lg">
            Karpatské námestie 10A
            <br />
            831 06 Bratysława
            <br />
            Słowacja <br />
          </p>
        </article>
      </div>
    </section>
  )
}

export default OurSocialMedia
