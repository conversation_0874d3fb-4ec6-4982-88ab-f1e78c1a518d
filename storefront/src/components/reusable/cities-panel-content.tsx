import { X } from "lucide-react"
import { Popover } from "@headlessui/react"

interface IProps {
  selectedCities: string[] | null
  cities: Array<{ id: string; name: string }>
  onCityClick: (city: string) => void
  onClose: () => void
}

const CitiesPanelContent = ({
  cities,
  onCityClick,
  selectedCities,
  onClose,
}: IProps) => {
  const areAnyCitiesAvailable = cities.length > 0

  return (
    <>
      <div className="flex justify-between mb-4">
        <h3 className="text-lg text-gray-50">Wybierz miasto</h3>
        <button
          className="px-2 py-1 rounded-[40px] border text-gray-50"
          onClick={onClose}
        >
          <X className="w-4 h-4" />
        </button>
      </div>
      <div className="flex flex-wrap gap-4">
        {areAnyCitiesAvailable ? (
          cities?.map((city) => (
            <button
              key={city.id}
              className={`px-4 ring-0 outline-none py-1 shadow-none !border-secondary border transition-colors rounded-xxl ${
                selectedCities?.includes(city.name)
                  ? "bg-secondary text-white"
                  : "bg-white text-secondary"
              }`}
              onClick={(e) => {
                e.stopPropagation()
                onCityClick(city.name)
              }}
            >
              {city.name}
            </button>
          ))
        ) : (
          <p className="text-secondary">
            Nie znaleziono miast dla tej kategorii.
          </p>
        )}
      </div>
    </>
  )
}

export default CitiesPanelContent
