"use client"

import <PERSON>ie<PERSON>anne<PERSON> from "components/cookie-banner/cookie-banner"
import { RequestCookie } from "next/dist/compiled/@edge-runtime/cookies"
import { useState, useLayoutEffect } from "react"

type Props = {
  cookieValue: RequestCookie | undefined
}

const ForceBrowserDataVersioning = ({ cookieValue }: Props) => {
  const [ensured, setEnsured] = useState(false)

  function deleteAllCookies() {
    var cookies = document.cookie.split("; ")
    for (var c = 0; c < cookies.length; c++) {
      var d = window.location.hostname.split(".")
      while (d.length > 0) {
        var cookieBase =
          encodeURIComponent(cookies[c].split(";")[0].split("=")[0]) +
          "=; expires=Thu, 01-Jan-1970 00:00:01 GMT; domain=" +
          d.join(".") +
          " ;path="
        var p = location.pathname.split("/")
        document.cookie = cookieBase + "/"
        while (p.length > 0) {
          document.cookie = cookieBase + p.join("/")
          p.pop()
        }
        d.shift()
      }
    }
  }

  function clearBrowserDataIfNeeded() {
    const currentVersion = "2.0"
    const storedVersion = localStorage.getItem("appVersion")

    if (storedVersion !== currentVersion) {
      localStorage.clear()
      deleteAllCookies()

      localStorage.setItem("appVersion", currentVersion)
      console.log(
        "Updated to version " + currentVersion + ", cleared browser data"
      )
    }

    setEnsured(true)
  }

  useLayoutEffect(() => {
    clearBrowserDataIfNeeded()
  }, [])

  if (!ensured) return null

  return <CookieBanner cookieValue={cookieValue} />
}

export default ForceBrowserDataVersioning
