"use client"

import clsx from "clsx"
import { PropsWithChildren } from "react"
import WithLoadingImage from "./with-loading-image"

type Props = {
  imageSrc?: string
  title?: string
  className?: string
  imageClassName?: string
}

const SubpageHero = ({
  imageSrc,
  title,
  children,
  className,
  imageClassName,
}: PropsWithChildren<Props>) => {
  return (
    <div className="relative h-[30vh] lg:h-[40vh] min-h-[355px] w-full mb-20">
      <div className="absolute inset-0 overflow-clip">
        <WithLoadingImage
          imageSrc={imageSrc ?? ""}
          alt={title || "Hero background"}
          className={clsx(
            `w-full h-full z-0 object-cover rounded-b-xl md:rounded-b-[50px] lg:[object-position:0_30%] animate-fade-in-top`,
            imageClassName
          )}
          fill
          unoptimized
        />
        {title && (
          <div className="absolute inset-0 bg-black/30 rounded-b-xl md:rounded-b-[50px]" />
        )}
      </div>

      <div className="relative z-10 h-full flex flex-col items-center justify-center">
        {title && (
          <h1 className="max-md:text-center mb-14 text-white max-w-screen-lg text-hero_mobile px-4 md:text-hero font-bold z-10">
            {title}
          </h1>
        )}

        {/* Search Bar */}
        <div className="w-full absolute bottom-0 translate-y-1/2 mb-2 sm:px-4">
          <div className={clsx("inner-container-max-w", className)}>
            <div className="relative">{children}</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SubpageHero
