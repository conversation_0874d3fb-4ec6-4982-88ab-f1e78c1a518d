import Link from "next/link"
import Image from "next/image"

interface IProps {
  name: string
  href: string
  className?: string
  iconClassName?: string
  captionClassName?: string
  Svg?: React.ElementType
  svgAsImg?: string
  svgHover?: string
}

const SocialMediaTile = ({
  name,
  href,
  svgHover,
  svgAsImg,
  className = "",
  iconClassName = "",
  captionClassName = "",
  Svg,
}: IProps) => {
  return (
    <Link
      href={href}
      aria-label={name}
      className={`min-w-[89px] min-h-[89px] flex flex-col items-center justify-center gap-2 border-[0.45px] border-[#d4d4d4] rounded bg-gray-10 shadow-md hover:border-secondary group ${className}`}
    >
      {Svg && (
        <Svg
          width={28}
          height={28}
          className={`w-7 h-7 fill-gray-50 ${iconClassName}`}
        />
      )}
      {svgAsImg && (
        <Image
          src={svgAsImg}
          alt={name}
          className={`w-7 h-7 hidden group-hover:block ${iconClassName}`}
        />
      )}
      {svgHover && (
        <Image
          src={svgHover}
          alt={name}
          className={`w-7 h-7 group-hover:hidden ${iconClassName}`}
        />
      )}
      <h3
        className={`text-small-bold text-gray-50 group-hover:text-black ${captionClassName}`}
      >
        {name}
      </h3>
    </Link>
  )
}

export default SocialMediaTile
