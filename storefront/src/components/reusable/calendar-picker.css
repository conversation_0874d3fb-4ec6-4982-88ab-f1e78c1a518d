input[type="date"]::-webkit-inner-spin-button,
input[type="date"]::-webkit-calendar-picker-indicator {
  display: none;
  -webkit-appearance: none;
}

.rdp-root {
  --rdp-accent-color: var(--secondary); /* Change the accent color to indigo. */
  --rdp-accent-background-color: #f0f0f0; /* Change the accent background color. */
  --rdp-range_middle-background-color: transparent;
  --rdp-disabled-color: #a9a9a9;
  /* Add more CSS variables here. */
}

.rdp-selected .rdp-day_button {
  @apply rounded-md border shadow-sm  font-semibold;
}

.rdp-caption_label {
  @apply font-medium;
}

.rdp-button_previous,
.rdp-button_next {
  transition: all 0.2s ease-in-out;

  border: 1px solid #e8e8e8;
  @apply shadow-sm mx-1 rounded-md hover:bg-secondary;

  svg {
    transition: all 0.2s ease-in-out;
  }

  &:hover svg {
    fill: var(--rdp-accent-background-color);
  }
}

.rdp-selected .rdp-day_button {
  @apply ring-1 ring-gray-200;
}

.rdp-day {
  @apply max-xs:p-0 p-px sm:p-[2px] max-xs:text-sm;
}

.rdp-day.rdp-today.rdp-selected > button {
  @apply text-primary;
}

.rdp-month_grid {
  @apply min-h-[300px];
}

.rdp-day.rdp-today .rdp-day_button {
  @apply text-secondary;
}

.rdp-day_button:disabled {
  cursor: not-allowed;
  color: var(--rdp-disabled-color);
}

.rdp-day.rdp-disabled.rdp-today > button {
  color: var(--rdp-disabled-color);
}
