import React from "react"
import { AttractionShort, AttractionDetails } from "types/global"
import AttractionCard from "app/(atrakcje)/produkt/components/attraction-card"
import { AttractionNavigationSkeletonGrid } from "components/skeletons/attraction-navigation-skeleton"

interface SearchResultsProps {
  results: AttractionShort[] | null
  isLoading: boolean
  query: string
  resultsCount: number
}

const SearchResults: React.FC<SearchResultsProps> = ({
  results,
  isLoading,
  query,
  resultsCount,
}) => {
  if (isLoading) {
    return (
      <div className="container grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-4 max-w-screen-xl mx-auto mb-16 mobile-filter-results-margin">
        <AttractionNavigationSkeletonGrid count={6} />
      </div>
    )
  }

  if (!results || results.length === 0) {
    return (
      <div className="container max-w-screen-xl mx-auto mb-16 mobile-filter-results-margin">
        <div className="col-span-full text-center text-gray-50 min-h-[20vh] flex flex-col items-center justify-center">
          <h3 className="text-lg font-semibold mb-2">
            Brak wyników dla "{query}"
          </h3>
          <p className="text-sm">
            Spróbuj użyć innych słów kluczowych lub sprawdź pisownię.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="inner-container-max-w  mx-auto mb-16 mobile-filter-results-margin">
      <div className="mb-4 text-sm text-gray-50">
        Znaleziono {resultsCount}{" "}
        {resultsCount === 1
          ? "atrakcję"
          : resultsCount < 5
          ? "atrakcje"
          : "atrakcji"}{" "}
        dla "{query}"
      </div>

      <div className="grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-4 ">
        {results.map((attraction) => (
          <AttractionCard
            key={attraction.id}
            attraction={attraction as AttractionDetails}
          />
        ))}
      </div>
    </div>
  )
}

export default SearchResults
