import Messanger from "@icons/messanger.svg"
import Facebook from "@icons/facebook.svg"
import Instagram from "@icons/instagram.svg"
import Image from "next/image"
import { SOCIAL_LINKS } from "@lib/constants/socialLinks"
import Link from "next/link"

interface IProps {
  iconsClassName?: string
  className?: string
  isShowFacebook?: boolean
}

const SocialLinks = ({
  iconsClassName = "",
  className = "",
  isShowFacebook = true,
}: IProps) => {
  return (
    <div className={className}>
      <div className="max-w-[230px] h-[1px] bg-black" />
      <div className="flex gap-4 items-center pt-5">
        {isShowFacebook && (
          <Link href={SOCIAL_LINKS[1].href} aria-label={SOCIAL_LINKS[1].name}>
            <Image
              src={Facebook}
              alt={SOCIAL_LINKS[1].name}
              width={16}
              height={16}
              className={`w-4 h-4 lg:w-5 lg:h-5 ${iconsClassName}`}
            />
          </Link>
        )}
        <Link href={SOCIAL_LINKS[4].href} aria-label={SOCIAL_LINKS[4].name}>
          <Image
            src={Messanger}
            alt={SOCIAL_LINKS[4].name}
            width={16}
            height={16}
            className={`w-4 h-4 lg:w-5 lg:h-5 ${iconsClassName}`}
          />
        </Link>
        <Link href={SOCIAL_LINKS[3].href} aria-label={SOCIAL_LINKS[3].name}>
          <Image
            src={Instagram}
            alt={SOCIAL_LINKS[3].name}
            width={16}
            height={16}
            className={`w-4 h-4 lg:w-5 lg:h-5 ${iconsClassName}`}
          />
        </Link>
      </div>
    </div>
  )
}

export default SocialLinks
