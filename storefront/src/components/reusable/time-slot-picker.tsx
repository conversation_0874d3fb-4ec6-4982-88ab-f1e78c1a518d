import { useState, useEffect } from "react"
import clsx from "clsx"
import { Clock } from "lucide-react"
import CustomPopover from "./custom-popover"
import ErrorMessage from "./error-message"

export type TimeSlot = {
  time: string
  isDisabled: boolean
  disabledReason?: string
}

type TimeSlotPickerProps = {
  availableTimeSlots: TimeSlot[]
  selectedTime: string | undefined
  onChange: (time: string) => void
  onValidationChange?: (isValid: boolean) => void
  error?: string
  label?: string
  variant?: "primary" | "transparent"
  disabledMessage?: string
}

/**
 * A component for selecting from available time slots
 */
const TimeSlotPicker: React.FC<TimeSlotPickerProps> = ({
  availableTimeSlots,
  selectedTime,
  onChange,
  onValidationChange,
  error,
  label = "Wybie<PERSON> godzinę",
  variant = "transparent",
  disabledMessage = "niedostępne",
}) => {
  const [selected, setSelected] = useState<string | undefined>(selectedTime)

  useEffect(() => {
    setSelected(selectedTime)
  }, [selectedTime])

  const handleTimeSelect = (time: string, isDisabled: boolean) => {
    if (!isDisabled) {
      setSelected(time)
      onChange(time)
      if (onValidationChange) {
        onValidationChange(true)
      }
    }
  }

  const onlyAvailableTimeSlots = availableTimeSlots.filter(
    (timeSlot) => !timeSlot.isDisabled
  )

  return (
    <div className="w-full">
      <CustomPopover
        fitWidthPanel
        variant={variant}
        popoverButtonContent={
          <div className="flex items-center h-8 gap-2 justify-between">
            <div className="flex items-center gap-2">
              <span>{label}</span>
            </div>
            {selected && (
              <div className="px-4 py-1 text-sm border-secondary border animate-fade-in-right rounded-xl">
                {selected}
              </div>
            )}
            {error && <ErrorMessage error={error} />}
          </div>
        }
      >
        {(close) => (
          <div className="grid gap-2 p-2 max-h-[300px] overflow-y-auto">
            {onlyAvailableTimeSlots.length === 0 ? (
              <div className="text-center text-sm text-muted-foreground p-4">
                Brak dostępnych terminów
              </div>
            ) : (
              onlyAvailableTimeSlots.map((timeSlot) => (
                <button
                  type="button"
                  onClick={() => {
                    handleTimeSelect(timeSlot.time, timeSlot.isDisabled)
                    if (!timeSlot.isDisabled) {
                      close()
                    }
                  }}
                  key={timeSlot.time}
                  disabled={timeSlot.isDisabled}
                  className={clsx(
                    "border px-4 py-2 hover:shadow-sm hover:border hover:border-secondary rounded-md min-w-[200px]",
                    {
                      "opacity-50 cursor-not-allowed": timeSlot.isDisabled,
                      "cursor-pointer": !timeSlot.isDisabled,
                      "border-secondary": selected === timeSlot.time,
                      "border-gray-200": selected !== timeSlot.time,
                    }
                  )}
                >
                  <div className="flex items-center justify-between">
                    <span>{timeSlot.time}</span>
                  </div>
                </button>
              ))
            )}
          </div>
        )}
      </CustomPopover>
    </div>
  )
}

export default TimeSlotPicker
