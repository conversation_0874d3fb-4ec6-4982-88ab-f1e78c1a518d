import { Popover, PopoverButton, PopoverPanel } from "@headlessui/react"
import clsx from "clsx"
import useMobile from "hooks/useMobile"
import React from "react"

type Props = {
  children:
    | ((close: () => void) => React.ReactNode)
    | React.ReactElement
    | React.ReactNode
  popoverButtonContent: React.ReactNode
  variant?: "primary" | "transparent"
  fitWidthPanel?: boolean
  triggerAsDiv?: boolean
  headless?: boolean
  className?: string
}

const CustomPopover = ({
  children,
  popoverButtonContent,
  variant,
  triggerAsDiv = false,
  fitWidthPanel,
  headless = false,
  className,
}: Props) => {
  const isMobile = useMobile()

  return (
    <Popover className={clsx("relative", className)}>
      <PopoverButton
        as={triggerAsDiv ? "div" : "button"}
        className={
          headless
            ? ""
            : `py-2 border rounded-full w-full  text-left px-6 text-lg font-semibold ${
                variant === "transparent"
                  ? "bg-transparent text-white"
                  : "bg-white text-primary"
              }`
        }
      >
        {popoverButtonContent}
      </PopoverButton>
      <PopoverPanel
        transition
        {...(isMobile && { anchor: "bottom" })}
        className={clsx(
          "flex z-[50] max-h-[425px]  overflow-y-auto overflow-x-hidden inset-x-0 absolute duration-200 transition-all data-[open]:scale-100 data-[closed]:scale-95 data-[closed]:opacity-0 mt-4 flex-col gap-2 bg-white rounded-lg p-4 border",
          { "w-fit": fitWidthPanel },
          { "transition-none relative": isMobile },
          { "max-h-[unset]": headless }
        )}
      >
        {({ close }) => (
          <>{typeof children === "function" ? children(close) : children}</>
        )}
      </PopoverPanel>
    </Popover>
  )
}

export default CustomPopover
