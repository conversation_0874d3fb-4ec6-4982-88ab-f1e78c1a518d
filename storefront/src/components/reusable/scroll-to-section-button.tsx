"use client"

import React from "react"
import clsx from "clsx"
import { useScrollToCenter } from "hooks/useScrollToCenter"

interface ScrollToSectionButtonProps {
  targetId: string
  children: React.ReactNode
  className?: string
  navbarHeight?: number
}

export const ScrollToSectionButton: React.FC<ScrollToSectionButtonProps> = ({
  targetId,
  children,
  className,
  navbarHeight = 200,
}) => {
  const { isTargetVisible, scrollToCenter, setTargetRef } = useScrollToCenter({
    targetId,
    navbarHeight,
  })

  // Set the ref on the target element when component mounts
  React.useEffect(() => {
    const targetElement = document.getElementById(targetId)
    if (targetElement) {
      setTargetRef(targetElement)
    }
  }, [targetId, setTargetRef])

  return (
    <button
      onClick={scrollToCenter}
      className={clsx(
        "h-12 px-8 bg-secondary rounded-[40px] border-2 border-secondary text-primary grid place-content-center transition-all duration-300 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-opacity-50",
        isTargetVisible
          ? "opacity-0 pointer-events-none transform scale-95"
          : "opacity-100 pointer-events-auto transform scale-100",
        className
      )}
    >
      {children}
    </button>
  )
}
