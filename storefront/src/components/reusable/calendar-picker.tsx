import CalendarIcon from "@icons/calendar.svg"
import { dateUtils } from "@lib/util/date-format"
import { pl } from "react-day-picker/locale"

import Image from "next/image"
import { DateRange, DayPicker } from "react-day-picker"

import "react-day-picker/style.css"
import "./calendar-picker.css"
import { AttractionBlockedDatesByMonth } from "types/global"
import { AttractionBlockedDates } from "types/global"
import { X } from "lucide-react"
import CustomPopoverWithoutDependency from "./custom-popover-without-dependency"
import useStoreSettings from "hooks/useStoreSettings"

type BaseProps = {
  variant?: "primary" | "transparent"
  blockedDates: AttractionBlockedDates[]
  blockedDatesByMonth: AttractionBlockedDatesByMonth[]
  footerSlot?: React.ReactNode | ((close: () => void) => React.ReactNode)
  customTriggerComponent?: React.ReactNode
  className?: string
}

type SingleProps = BaseProps & {
  range?: false
  value: Date | undefined
  onChange: (date: Date | undefined) => void
}

type RangeProps = BaseProps & {
  range: true
  value: DateRange | undefined
  onChange: (date: DateRange) => void
}

type Props = SingleProps | RangeProps

const CalendarPicker = (props: Props) => {
  const { storeSettings } = useStoreSettings()

  const { variant, blockedDates, blockedDatesByMonth } = props

  // Calculate the date before which booking is blocked, considering time and handicap
  const calculateBlockedPastDates = (): Date => {
    const now = new Date()
    const currentHour = now.getHours()
    const currentMinute = now.getMinutes()

    // Determine the base earliest allowed *day* based on the time rule
    let earliestAllowedDayStart = new Date(now)

    const latestBookingHour = storeSettings?.latest_booking_hour ?? 19
    const latestBookingMinute = storeSettings?.latest_booking_minute ?? 0

    // Compare current time with latest booking time
    const currentTimeInMinutes = currentHour * 60 + currentMinute
    const latestBookingTimeInMinutes =
      latestBookingHour * 60 + latestBookingMinute

    if (currentTimeInMinutes >= latestBookingTimeInMinutes) {
      // After configured latest booking time: Earliest booking is the day after tomorrow. Block dates before that day.
      earliestAllowedDayStart.setDate(now.getDate() + 2)
    } else {
      // Before configured latest booking time: Earliest booking is tomorrow. Block dates before that day.
      earliestAllowedDayStart.setDate(now.getDate() + 1)
    }
    // Set to the very beginning of that day for comparison
    earliestAllowedDayStart.setHours(0, 0, 0, 0)

    // Consider the global handicap setting
    const globalHandicapHours =
      storeSettings?.global_amount_of_hours_calendar_handicap
    if (globalHandicapHours) {
      const handicapCutoffTime = new Date()
      handicapCutoffTime.setHours(
        handicapCutoffTime.getHours() + globalHandicapHours
      )

      // If the handicap cutoff time is *later* than the start of the earliest allowed day (calculated based on the 9 PM rule),
      // then the handicap imposes a stricter restriction. Use the handicap cutoff time directly.
      // The `before` matcher will block all dates/times strictly *before* this exact time.
      if (handicapCutoffTime > earliestAllowedDayStart) {
        return handicapCutoffTime
      }
    }

    // If no handicap applies or the handicap is earlier than the time-based rule,
    // use the start of the earliest allowed day calculated from the 9 PM rule.
    // The `before` matcher will block all dates strictly *before* the start of this day.
    return earliestAllowedDayStart
  }

  const blockedPastDates = calculateBlockedPastDates()

  const disabledDatesMatcher: DateRange[] = blockedDates?.map((date) => ({
    from: new Date(date.date_from),
    to: new Date(date.date_to),
  }))

  const disabledDatesByMonth = (date: Date): boolean => {
    return (
      blockedDatesByMonth?.some((blocked) => {
        if (date.getMonth() !== blocked.month - 1) return false
        const blockedDays = blocked.days
          .split(",")
          .map((day) => parseInt(day.trim(), 10))
        return blockedDays.includes(date.getDate())
      }) ?? false
    )
  }

  /**
   * Checks if a date falls within the yearly disabled periods:
   * - January 1st to March 31st
   * - April 1st
   * - November 1st to December 31st
   */
  const isInYearlyDisabledPeriod = (date: Date): boolean => {
    const month = date.getMonth() // 0-indexed (0 = January, 11 = December)
    const day = date.getDate() // Day of the month (1-31)

    // January (0) to March (2) OR November (10) to December (11)
    // OR April (3) and day is 1 (April 1st)
    return (
      (month >= 0 && month <= 2) ||
      (month >= 10 && month <= 11) ||
      (month === 3 && day === 1)
    )
  }

  const onDateChange = (date: Date | DateRange) => {
    if (props.range) {
      props.onChange(date as DateRange)
    } else {
      props.onChange(date as Date)
    }
  }

  const todayDateInstance = new Date()
  const currentYear = todayDateInstance.getFullYear()
  const fromMonth = new Date(currentYear, 0)
  const toMonth = new Date(currentYear, 11)

  return (
    <CustomPopoverWithoutDependency
      triggerAsDiv
      fitWidthPanel
      headless={Boolean(props.customTriggerComponent)}
      variant={variant}
      className={props.className}
      popoverButtonContent={
        props.customTriggerComponent ? (
          props.customTriggerComponent
        ) : (
          <div className="flex items-center gap-2 min-h-8">
            <Image src={CalendarIcon} alt="calendar" width={20} height={20} />
            {props.value ? (
              props.range ? (
                <div className="flex items-center gap-2">
                  <span className="text-sm">
                    {props.value.from ? (
                      <div className="flex items-center gap-2">
                        {dateUtils.formatDateToString(props.value.from)}
                      </div>
                    ) : (
                      "Wybierz datę początkową"
                    )}
                  </span>
                  <span className="text-sm">-</span>
                  <span className="text-sm">
                    {props.value.to ? (
                      <div className="flex items-center gap-2">
                        {dateUtils.formatDateToString(props.value.to)}
                      </div>
                    ) : (
                      "Wybierz datę końcową"
                    )}
                  </span>
                  {props.value.from || props.value.to ? (
                    <button
                      className="p-1 rounded-full bg-white/10"
                      onClick={(e) => {
                        e.stopPropagation()
                        props.onChange({
                          from: undefined,
                          to: undefined,
                        })
                      }}
                    >
                      <X className="size-4" />
                    </button>
                  ) : null}
                </div>
              ) : (
                dateUtils.formatDateToString(props.value)
              )
            ) : (
              "Wybierz datę"
            )}
          </div>
        )
      }
    >
      {(close) => (
        <>
          {props.range ? (
            <DayPicker
              mode="range"
              required
              startMonth={fromMonth}
              endMonth={toMonth}
              selected={props.value}
              defaultMonth={props.value?.from}
              onSelect={(date) => {
                onDateChange(date)
              }}
              disabled={[
                { before: blockedPastDates },
                ...disabledDatesMatcher,
                disabledDatesByMonth,
                isInYearlyDisabledPeriod,
              ]}
              locale={pl}
            />
          ) : (
            <DayPicker
              mode="single"
              required
              startMonth={fromMonth}
              endMonth={toMonth}
              selected={props.value}
              defaultMonth={props.value}
              onSelect={(date) => {
                props.onChange(date)
                close()
              }}
              disabled={[
                { before: blockedPastDates },
                ...disabledDatesMatcher,
                disabledDatesByMonth,
                isInYearlyDisabledPeriod,
              ]}
              locale={pl}
            />
          )}
          {props.footerSlot && typeof props.footerSlot === "function" ? (
            <div>{props.footerSlot(close)}</div>
          ) : (
            props.footerSlot
          )}
        </>
      )}
    </CustomPopoverWithoutDependency>
  )
}

export default CalendarPicker
