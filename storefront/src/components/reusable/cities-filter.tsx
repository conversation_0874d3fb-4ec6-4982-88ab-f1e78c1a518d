import Image from "next/image"
import ArrowR<PERSON> from "@icons/arrow_right.svg"
import { Calendar, X } from "lucide-react"
import CitiesPanelContent from "./cities-panel-content"
import Button from "./button"
import CalendarPicker from "./calendar-picker"
import { DateRange } from "react-day-picker"
import { dateUtils } from "@lib/util/date-format"
import Link from "next/link"
import { usePathname } from "next/navigation"
import CustomPopoverWithoutDependency from "./custom-popover-without-dependency"
import AttractionsQueryFilter from "./attractions-query-filter"

type CitiesFilterProps = {
  selectedCities: string[] | null
  cities: Array<{ id: string; name: string }>
  onCityClick: (city: string) => void
  handleClearCities: () => void
  dateRange: DateRange
  updateDateRangeQuery: (dateRange: DateRange) => void
  handleClearAllQueries: () => void
  handleClearDateRange: () => void
}

const AttractionsFilter = ({
  selectedCities,
  cities,
  onCityClick,
  handleClearCities,
  dateRange,
  updateDateRangeQuery,
  handleClearAllQueries,
  handleClearDateRange,
}: CitiesFilterProps) => {
  const areCitiesSelected =
    Array.isArray(selectedCities) && selectedCities.length > 0

  const getQueryParams = () => {
    const params = new URLSearchParams()
    if (selectedCities) {
      selectedCities.forEach((city) => params.append("c", city))
    }
    if (dateRange.from) {
      params.append("f", dateUtils.formatDateToUrl(dateRange.from))
    }
    if (dateRange.to) {
      params.append("t", dateUtils.formatDateToUrl(dateRange.to))
    }
    return params.toString()
  }

  const isOnPasujaceAtrakcjePage = usePathname() === "/pasujace-atrakcje"

  return (
    <div className="flex gap-2 w-full flex-col lg:flex-row lg:items-center">
      <div className="flex gap-2 lg:basis-[800px]  items-center mt-4 max-md:grid max-md:mt-2">
        <div className="items-center grow justify-between gap-4 max-2xs:gap-2 flex py-2 px-6 rounded-[40px] border-2 border-secondary relative bg-white text-lg placeholder:text-gray-50 focus:outline-none ">
          <CustomPopoverWithoutDependency
            headless
            triggerAsDiv
            className="grow"
            customWidth="md:left-0 md:-right-[100%] md:w-[unset] md:max-w-[500px] md:min-w-[400px]"
            popoverButtonContent={
              <>
                {areCitiesSelected ? (
                  <div className="flex gap-2 items-center justify-between grow">
                    <div className="flex gap-2 items-center flex-wrap">
                      {selectedCities?.map((city) => (
                        <button
                          key={city}
                          className="px-4 animate-fade-in-right rounded-[40px] border text-gray-50 max-sm:text-sm"
                        >
                          {city}
                        </button>
                      ))}
                    </div>
                    <button
                      className="px-2 py-1 rounded-[40px]  border text-gray-50"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleClearCities()
                      }}
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                ) : (
                  <button className="flex justify-between gap-2 grow items-center w-full">
                    <span className="text-gray-50 max-sm:text-sm inline-block mt-[0.125rem]">
                      Wybierz miasto
                    </span>
                    <Image
                      src={ArrowRight}
                      alt="arrow-right"
                      width={24}
                      height={24}
                    />
                  </button>
                )}
              </>
            }
          >
            {(close) => (
              <CitiesPanelContent
                onClose={close}
                cities={cities}
                selectedCities={selectedCities}
                onCityClick={(city) => {
                  onCityClick(city)
                  close()
                }}
              />
            )}
          </CustomPopoverWithoutDependency>

          <div className="w-px h-7 bg-gray-50 my-auto" />

          <CalendarPicker
            className=""
            blockedDates={[]}
            blockedDatesByMonth={[]}
            value={dateRange}
            range={true}
            onChange={updateDateRangeQuery}
            variant="primary"
            customTriggerComponent={
              <div className="flex items-center gap-2">
                <Calendar className="size-6 md:size-7 max-2xs:hidden  mr-2 stroke-[px] text-gray-50 inline-block" />

                {dateRange.from && dateRange.to ? (
                  <div className="flex justify-between gap-2 items-center w-full">
                    <div className="flex gap-2 max-2xs:gap-0 text-sm text-gray-50 max-sm:text-xs">
                      <div className="flex items-center">
                        {dateUtils.formatDateToString(dateRange.from)}
                      </div>
                      <div>-</div>
                      <div>{dateUtils.formatDateToString(dateRange.to)}</div>
                    </div>

                    <button
                      className="px-2 py-1 rounded-[40px]  border text-gray-50"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleClearDateRange()
                      }}
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                ) : (
                  <button className="flex justify-between gap-2 items-center w-full">
                    <span className="text-gray-50 max-sm:text-sm inline-block mt-[0.125rem]">
                      Wybierz termin
                    </span>

                    <Image
                      src={ArrowRight}
                      alt="arrow-right"
                      width={24}
                      height={24}
                    />
                  </button>
                )}
              </div>
            }
            footerSlot={(close) => (
              <div className="flex gap-4 items-center">
                <div className="flex gap-2 items-center text-sm">
                  <div>od:</div>
                  <div className="bg-gray-200 p-2 rounded-md">
                    {dateRange.from
                      ? dateUtils.formatDateToString(dateRange.from)
                      : "-"}
                  </div>
                </div>

                <div className="flex gap-2 items-center text-sm">
                  <div>do:</div>
                  <div className="bg-gray-200 p-2  rounded-md">
                    {dateRange.to
                      ? dateUtils.formatDateToString(dateRange.to)
                      : "-"}
                  </div>
                </div>

                <Button
                  variant="primary"
                  className="px-2 py-1 rounded-[40px] text-sm  ml-auto border text-gray-50"
                  onClick={close}
                >
                  Potwierdź
                </Button>
              </div>
            )}
          />
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            className="bg-white h-12 aspect-video"
            onClick={handleClearAllQueries}
          >
            Usuń
          </Button>

          {!isOnPasujaceAtrakcjePage && (
            <Link
              href={`/pasujace-atrakcje?${getQueryParams()}`}
              className="h-12 px-8 max-md:grow bg-secondary rounded-[40px] border-2 border-secondary text-primary grid place-content-center"
            >
              Wyszukaj
            </Link>
          )}
        </div>
      </div>
      <div className="grow lg:mt-4">
        <AttractionsQueryFilter />
      </div>
    </div>
  )
}

export default AttractionsFilter
