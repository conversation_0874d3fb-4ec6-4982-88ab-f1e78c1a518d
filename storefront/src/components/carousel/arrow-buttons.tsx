import React, {
  ComponentPropsWithRef,
  useCallback,
  useEffect,
  useState,
} from "react"
import { EmblaCarouselType } from "embla-carousel"

type UsePrevNextButtonsType = {
  prevBtnDisabled: boolean
  nextBtnDisabled: boolean
  onPrevButtonClick: () => void
  onNextButtonClick: () => void
}

export const usePrevNextButtons = (
  emblaApi: EmblaCarouselType | undefined
): UsePrevNextButtonsType => {
  const [prevBtnDisabled, setPrevBtnDisabled] = useState(true)
  const [nextBtnDisabled, setNextBtnDisabled] = useState(true)

  const onPrevButtonClick = useCallback(() => {
    if (!emblaApi) return
    emblaApi.scrollPrev()
  }, [emblaApi])

  const onNextButtonClick = useCallback(() => {
    if (!emblaApi) return
    emblaApi.scrollNext()
  }, [emblaApi])

  const onSelect = useCallback((emblaApi: EmblaCarouselType) => {
    setPrevBtnDisabled(!emblaApi.canScrollPrev())
    setNextBtnDisabled(!emblaApi.canScrollNext())
  }, [])

  useEffect(() => {
    if (!emblaApi) return

    onSelect(emblaApi)
    emblaApi.on("reInit", onSelect).on("select", onSelect)
  }, [emblaApi, onSelect])

  return {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  }
}

type PropType = ComponentPropsWithRef<"button">

export const PrevButton: React.FC<PropType> = (props) => {
  const { children, ...restProps } = props

  return (
    <button
      className="embla__button embla__button--prev"
      type="button"
      {...restProps}
    >
      <svg
        width="23"
        height="23"
        viewBox="0 0 23 23"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M19.673 16.8521C17.9439 19.8411 14.7122 21.8521 11.0108 21.8521C5.48789 21.8521 1.01074 17.3749 1.01074 11.8521C1.01074 6.3292 5.48789 1.85205 11.0108 1.85205C14.7122 1.85205 17.9439 3.86304 19.673 6.85205M11.0108 7.85205L7.01081 11.8521M7.01081 11.8521L11.0108 15.8521M7.01081 11.8521H21.0108"
          stroke="currentColor"
        />
      </svg>

      {children}
    </button>
  )
}

export const NextButton: React.FC<PropType> = (props) => {
  const { children, ...restProps } = props

  return (
    <button
      className="embla__button embla__button--next"
      type="button"
      {...restProps}
    >
      <svg
        width="23"
        height="23"
        viewBox="0 0 23 23"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M2.34863 6.85205C4.07768 3.86304 7.3094 1.85205 11.0108 1.85205C16.5336 1.85205 21.0108 6.3292 21.0108 11.8521C21.0108 17.3749 16.5336 21.8521 11.0108 21.8521C7.3094 21.8521 4.07768 19.8411 2.34863 16.8521M11.0107 15.8521L15.0107 11.8521M15.0107 11.8521L11.0107 7.85205M15.0107 11.8521H1.01074"
          stroke="currentColor"
        />
      </svg>

      {children}
    </button>
  )
}
