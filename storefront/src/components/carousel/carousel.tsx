"use client"

import React, { Children } from "react"
import { EmblaOptionsType } from "embla-carousel"
import { useDotButton } from "./dot-button"
import { PrevButton, NextButton, usePrevNextButtons } from "./arrow-buttons"
import useEmblaCarousel from "embla-carousel-react"
import "./embla.css"
import clsx from "clsx"

type PropType = {
  options?: EmblaOptionsType
  children: React.ReactNode
  className?: string
  asFooterCarousel?: boolean
  applyLeftPadding?: boolean
  slideSize?: number
  overflowHidden?: boolean
}

const Carousel: React.FC<PropType> = (props) => {
  const {
    options,
    children,
    className,
    asFooterCarousel = true,
    slideSize,
    overflowHidden = false,
  } = props
  const [emblaRef, emblaApi] = useEmblaCarousel(options)

  const { selectedIndex, scrollSnaps } = useDotButton(emblaApi)

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  } = usePrevNextButtons(emblaApi)

  // calculate progress of the carousel
  const progress = ((selectedIndex + 1) / scrollSnaps.length) * 100

  return (
    <section className={clsx("embla", className)}>
      <div
        className={clsx("embla__viewport", overflowHidden && "overflow-hidden")}
        ref={emblaRef}
      >
        <div className={clsx("embla__container", asFooterCarousel && "pr-10")}>
          {Children.map(children, (child, index) => (
            <div
              className={clsx(
                "!w-full sm:w-full grow shrink-0  no-selection",
                child?.props?.className,
                asFooterCarousel &&
                  "embla__slide max-sm:!basis-[100%] max-md:!basis-[80%] max-xl:!basis-[40%] !basis-[400px]"
              )}
              key={index}
            >
              {child}
            </div>
          ))}
        </div>
      </div>

      {Children.count(children) > 1 && (
        <div
          className={clsx(
            "w-full max-sm:hidden inner-container-max-w",
            asFooterCarousel && "inner-container",
            !asFooterCarousel && "mt-10"
          )}
        >
          {/* progress of the carousel */}
          <div className="w-full h-1 bg-gray-20 rounded-full">
            <div
              className="h-1 bg-secondary transition-all duration-300 rounded-full"
              style={{ width: `${progress}%` }}
            ></div>
          </div>

          {/* buttons */}
          <div className="flex justify-center mt-4 gap-5">
            <PrevButton
              onClick={onPrevButtonClick}
              disabled={prevBtnDisabled}
              className={clsx(
                "stroke-[2px]",
                { "text-white": !prevBtnDisabled && !asFooterCarousel },
                { "text-gray-50": prevBtnDisabled && asFooterCarousel }
              )}
            />
            <NextButton
              onClick={onNextButtonClick}
              disabled={nextBtnDisabled}
              className={clsx(
                "stroke-[2px]",
                { "text-gray-50": nextBtnDisabled && asFooterCarousel },
                { "text-white": !nextBtnDisabled && !asFooterCarousel }
              )}
            />
          </div>
        </div>
      )}
    </section>
  )
}

export default Carousel
