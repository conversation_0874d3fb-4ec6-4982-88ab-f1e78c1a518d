import Link from "next/link"
import React from "react"
import Image from "next/image"
import Logo from "@images/logo.svg"
import HOFU from "@images/HOFU.png"
import SocialLinks from "components/reusable/social-links"
import { NAV_LINKS } from "@lib/constants/navLinks"

type Props = {}

const Footer = (props: Props) => {
  return (
    <div
      id="footer"
      className="min-h-40 bg-black border-b-[10px] border-secondary"
    >
      <div className="pt-[60px] pb-8 sm:py-[60px]  container md:mx-auto 2xl:px-0">
        <div className="flex flex-col justify-between h-full gap-16">
          <div className="flex justify-between w-full flex-wrap gap-2">
            <Link href="/" className="text-2xl w-40 sm:w-60" aria-label="Logo">
              <Image
                src={Logo}
                alt="logo"
                placeholder="empty"
                className="filter brightness-0 invert"
              />
            </Link>
            <SocialLinks className="filter brightness-0 invert min-w-[159px]" />
          </div>
          <div className="grid grid-cols-2 text-small-regular justify-center gap-2 pr-4 md:gap-4 max-w-[326px] lg:max-w-fit sm:px-0 lg:text-large-regular">
            <Link href="/chorwacja-atrakcje" className="text-white">
              Atrakcje
            </Link>
            <Link href="/kontakt" className="text-white">
              Kontakt
            </Link>
            <Link href="/o-nas" className="text-white">
              O nas
            </Link>
            <Link href="/polityka-prywatnosci" className="text-white">
              Polityka prywatności
            </Link>
            <Link href="/poradniki" className="text-white">
              Poradniki
            </Link>
            <Link href="/koszyk" className="text-white">
              Koszyk
            </Link>
            <Link href="/informacje-prawne" className="text-white">
              Informacje prawne
            </Link>
            <Link href="/regulamin" className="text-white">
              Regulamin
            </Link>
            <Link href="/bezpieczenstwo" className="text-white">
              Bezpieczeństwo
            </Link>
            <Link
              href="/informacje-zgodne-z-aktem-o-uslugach-cyfrowych"
              className="text-white"
            >
              Informacje o usługach cyfrowych
            </Link>
          </div>
          <div className="flex justify-between w-full items-center">
            <span className="text-white">©LWS Group</span>
            <Link href={"https://hofumarketing.pl"}>
              <Image
                src={HOFU}
                alt="logo"
                placeholder="empty"
                className="pr-8"
              />
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Footer
