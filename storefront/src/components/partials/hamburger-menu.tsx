import Link from "next/link"
import CartIcon from "@icons/cart_black.svg"
import QueryProvider from "components/reusable/query-provider"
import Image from "next/image"
import CartItemsCounter from "app/(checkout)/koszyk/components/cart-items-counter"
import { Icons } from "components/reusable/Icons"
import { useEffect, useRef } from "react"
import { NAV_LINKS } from "@lib/constants/navLinks"
import SocialLinks from "components/reusable/social-links"

interface IProps {
  isOpenMenu: boolean
  setIsOpenMenu: React.Dispatch<React.SetStateAction<boolean>>
}

const HamburgerMenu = ({ isOpenMenu, setIsOpenMenu }: IProps) => {
  const menuRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const isMobile = window.innerWidth < 1024

    if (isOpenMenu && isMobile) {
      document.body.style.overflow = "hidden"
    } else {
      document.body.style.overflow = "auto"
    }

    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpenMenu(false)
      }
    }

    if (isOpenMenu) {
      document.body.addEventListener("click", handleClickOutside)
    }

    return () => {
      document.body.removeEventListener("click", handleClickOutside)
      document.body.style.overflow = "auto"
    }
  }, [isOpenMenu, setIsOpenMenu])

  const handleCloseMenu = (e: React.MouseEvent) => {
    setIsOpenMenu(false)
  }

  return (
    <div
      className="h-screen w-screen absolute top-0 left-0 bg-secondary text-black z-[999] lg:w-[476px] lg:right-0 lg:left-auto 2xl:right-auto 2xl:left-[calc(50%_+_295px)] 2xl:w-full"
      ref={menuRef}
    >
      <div className="flex gap-8 items-center justify-end w-full py-[41px] px-9 lg:px-[50px] 2xl:w-[476px]">
        <div className="relative">
          <Link href="/koszyk">
            <Image src={CartIcon} alt="cart" width={20} height={20} />
          </Link>
          <QueryProvider>
            <CartItemsCounter />
          </QueryProvider>
        </div>
        <button onClick={handleCloseMenu} aria-label="menu">
          <Icons.X />
        </button>
      </div>
      <div className="pl-[43px] flex flex-col h-[calc(100%_-_236px)] justify-between bg-secondary">
        <nav>
          <ul className="text-black text-[20.8px] font-semibold flex flex-col gap-[30px] lg:text-xl-semi lg:gap-5">
            {NAV_LINKS.map((link) => (
              <li key={link.name} className="first:font-bold">
                <Link href={link.href} onClick={handleCloseMenu}>
                  {link.name}
                </Link>
              </li>
            ))}
          </ul>
        </nav>
        <SocialLinks />
      </div>
    </div>
  )
}

export default HamburgerMenu
