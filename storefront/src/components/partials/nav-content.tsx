"use client"

import { Icons } from "components/reusable/Icons"
import Link from "next/link"
import React from "react"
import Logo from "@images/logo.svg"
import Image from "next/image"
import CartIcon from "@icons/cart_black.svg"
import CartItemsCounter from "app/(checkout)/koszyk/components/cart-items-counter"
import QueryProvider from "components/reusable/query-provider"
import HamburgerMenu from "./hamburger-menu"

type Props = {
  isOpenMenu: boolean
  setIsOpenMenu: React.Dispatch<React.SetStateAction<boolean>>
}

const NavContent = (props: Props) => {
  return (
    <>
      <div className="flex justify-between items-center py-6 container 2xl:px-0 max-w-[1440px] mx-auto">
        <Link href="/" className="text-2xl w-36 sm:w-fit" aria-label="Logo">
          <Image
            src={Logo}
            priority
            alt="logo"
            width={200}
            height={200}
            placeholder="empty"
          />
        </Link>

        <div className="flex gap-8 items-center shrink-0">
          <div className="relative">
            <Link href="/koszyk">
              <Image src={CartIcon} alt="cart" width={20} height={20} />
            </Link>
            <QueryProvider>
              <CartItemsCounter />
            </QueryProvider>
          </div>
          <button onClick={() => props.setIsOpenMenu(true)} aria-label="menu">
            <Icons.Menu />
          </button>
        </div>
      </div>

      {props.isOpenMenu && (
        <HamburgerMenu
          isOpenMenu={props.isOpenMenu}
          setIsOpenMenu={props.setIsOpenMenu}
        />
      )}
    </>
  )
}

export default NavContent
