"use client"

import React, { useState } from "react"
import NavContent from "./nav-content"

type Props = {}

const Navbar = (props: Props) => {
  const [isOpenMenu, setIsOpenMenu] = useState(false)

  return (
    <div
      className={`min-h-14 py-4 flex items-center justify-between fixed bg-secondary text-primary top-0 left-0 z-[99] w-full`}
    >
      <NavContent isOpenMenu={isOpenMenu} setIsOpenMenu={setIsOpenMenu} />
    </div>
  )
}

export default Navbar
