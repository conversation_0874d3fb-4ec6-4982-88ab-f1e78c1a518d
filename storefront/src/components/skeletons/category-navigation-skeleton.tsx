import { Icons } from "components/reusable/Icons"
import React from "react"

/**
 * Skeleton loader for CategoryNavigationCard to reduce layout shift while fetching
 * Maintains the same dimensions and styling as the actual card
 */
const CategoryNavigationCardSkeleton = () => {
  return (
    <div className="relative aspect-video min-[420px]:aspect-square sm:aspect-video rounded-xl overflow-hidden">
      <div className="absolute inset-0 bg-gray-100 "></div>
      <div className="absolute inset-0 bg-gray-300 animate-pulse"></div>
    </div>
  )
}

/**
 * Grid of skeleton loaders to display while categories are loading
 * Matches the grid layout of the actual categories display
 */
const CategoryNavigationSkeletonGrid = ({ count = 6 }: { count?: number }) => {
  return (
    <>
      {Array(count)
        .fill(0)
        .map((_, index) => (
          <CategoryNavigationCardSkeleton key={index} />
        ))}
    </>
  )
}

export { CategoryNavigationCardSkeleton, CategoryNavigationSkeletonGrid }
