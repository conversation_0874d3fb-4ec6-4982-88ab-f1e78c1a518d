import AttractionAttribute from "app/(atrakcje)/produkt/components/attraction-conditional-attributes"
import { ClockIcon } from "lucide-react"
import { CalendarIcon } from "lucide-react"
import { Icons } from "components/reusable/Icons"
import React from "react"
import clsx from "clsx"

/**
 * Skeleton loader for CategoryNavigationCard to reduce layout shift while fetching
 * Maintains the same dimensions and styling as the actual card
 */
const GuideNavigationCardSkeleton = () => {
  return (
    <div className="grow h-full">
      <div className="relative w-full mb-2 overflow-hidden rounded-xl min-h-[125px] sm:min-h-[350px] lg:min-h-[296px] aspect-square">
        <div className="absolute inset-0 bg-gray-200"></div>
        <div className="absolute inset-0 bg-gray-100 animate-pulse"></div>
      </div>

      <div className="relative overflow-hidden rounded-md mb-2 grow max-sm:min-h-40 min-h-16">
        <div className="absolute inset-0 bg-gray-200"></div>
        <div className="absolute inset-0 bg-gray-100 animate-pulse"></div>
      </div>
      <div className="relative overflow-hidden rounded-md mb-2 grow min-h-12">
        <div className="absolute inset-0 bg-gray-200"></div>
        <div className="absolute inset-0 bg-gray-100 animate-pulse"></div>
      </div>

      <div className="relative overflow-hidden rounded-md mb-2 grow min-h-4 w-1/3">
        <div className="absolute inset-0 bg-gray-200"></div>
        <div className="absolute inset-0 bg-gray-100 animate-pulse"></div>
      </div>
    </div>
  )
}

/**
 * Grid of skeleton loaders to display while categories are loading
 * Matches the grid layout of the actual categories display
 */
const GuideNavigationSkeletonGrid = ({ count = 6 }: { count?: number }) => {
  return (
    <>
      {Array(count)
        .fill(0)
        .map((_, index) => (
          <GuideNavigationCardSkeleton key={index} />
        ))}
    </>
  )
}

export { GuideNavigationCardSkeleton, GuideNavigationSkeletonGrid }
