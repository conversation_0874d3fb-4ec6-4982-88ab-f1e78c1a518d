import AttractionAttribute from "app/(atrakcje)/produkt/components/attraction-conditional-attributes"
import { ClockIcon } from "lucide-react"
import { CalendarIcon } from "lucide-react"
import { Icons } from "components/reusable/Icons"
import React from "react"

/**
 * Skeleton loader for CategoryNavigationCard to reduce layout shift while fetching
 * Maintains the same dimensions and styling as the actual card
 */
const AttractionNavigationCardSkeleton = () => {
  return (
    <div className="overflow-hidden p-2 sm:p-4 border-2 rounded-2xl flex flex-col h-full text-ellipsis">
      <div className="relative min-h-48 sm:min-h-64 overflow-hidden rounded-xl">
        <div className="absolute inset-0 bg-gray-200"></div>
        <div className="absolute inset-0 bg-gray-100 animate-pulse"></div>
      </div>

      <div className="grid grid-cols-2 gap-3 mt-3 text-xs text-gray-50"></div>

      <h2 className="sm:text-xl font-semibold grow min-h-12 my-3">{}</h2>

      <div className="text-gray-50 line-clamp-2 text-xs min-h-8"></div>
    </div>
  )
}

/**
 * Grid of skeleton loaders to display while categories are loading
 * Matches the grid layout of the actual categories display
 */
const AttractionNavigationSkeletonGrid = ({
  count = 6,
}: {
  count?: number
}) => {
  return (
    <>
      {Array(count)
        .fill(0)
        .map((_, index) => (
          <AttractionNavigationCardSkeleton key={index} />
        ))}
    </>
  )
}

export { AttractionNavigationCardSkeleton, AttractionNavigationSkeletonGrid }
