"use client"

import clsx from "clsx"
import Image from "next/image"
import Link from "next/link"
import { useRouter } from "next/navigation"
import React from "react"
import { cs } from "react-day-picker/locale"

type Props = {
  image: string
  title: string
  description?: string
  link: string
  imageClassName?: string
  className?: string
}

const NavigationCard = ({
  image,
  title,
  description,
  link,
  imageClassName = "",
  className = "",
}: Props) => {
  const router = useRouter()

  const handleClick = () => {
    router.push(link)
  }

  return (
    <button
      className={clsx(
        "rounded-[20px] text-black flex flex-col group text-left gap-2 p-4 sm:max-w-sm backdrop-blur-2xl bg-white/50 w-full lg:min-w-[276px] lg:max-w-[394px] lg:p-5 lg:h-full",
        className
      )}
      onClick={handleClick}
    >
      <div
        className={`relative shrink-0 rounded-lg overflow-hidden w-full grow aspect-square lg:aspect-[12/4]  ${imageClassName}`}
      >
        <Image
          src={image}
          alt={title}
          fill
          className={clsx("object-cover h-full w-full", imageClassName)}
          priority
          quality={80}
        />
      </div>
      <div className="flex w-full justify-between items-end flex-wrap gap-4">
        <div className="shrink-0">
          <h2 className="lg:font-bold lg:text-[clamp(1rem,1rem+0.35vw,3rem)] mt-4 text-xl-estrabold">
            {title}
          </h2>
          {description && (
            <p className="mt-4 lg:text-large-light text-gray-50 max-w-[182px]">
              {description}
            </p>
          )}
        </div>
        <Link
          href={link}
          className="rounded-full px-4 min-[420px]-px-[30px] py-2 hover:bg-secondary text-black hover:text-primary text-base-semi border-2 border-black hover:border-transparent group-hover:bg-secondary group-hover:text-primary group-hover:border-transparent"
        >
          Sprawdź
        </Link>
      </div>
    </button>
  )
}

export default NavigationCard
