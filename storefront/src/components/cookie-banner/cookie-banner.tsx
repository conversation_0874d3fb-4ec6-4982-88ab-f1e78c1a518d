"use client"

import { setCookieConsent } from "@lib/util/set-cookie-consent"
import Button from "components/reusable/button"
import { X } from "lucide-react"
import { RequestCookie } from "next/dist/compiled/@edge-runtime/cookies"
import { useEffect, useRef, useState } from "react"

interface IProps {
  cookieValue: RequestCookie | undefined
}

const CookieBanner = ({ cookieValue }: IProps) => {
  const [showBanner, setShowBanner] = useState(false)
  const itemRef = useRef<HTMLDivElement | null>(null)

  useEffect(() => {
    const localConsent = localStorage.getItem("cookie-consent")

    // Show banner ONLY if localStorage consent is missing
    if (!localConsent) {
      setShowBanner(true)
      setTimeout(() => {
        itemRef.current?.classList.add("animation-slide-up")
      }, 50)
    }
    // Intentionally ignore the potentially stale cookieValue prop for the initial show decision
  }, []) // Run only once after mount, crucially AFTER ForceBrowserDataVersioning's useLayoutEffect

  const handleAccept = () => {
    setShowBanner(false)
    setCookieConsent({ value: "accepted" })
    // Also store in localStorage for cross-tab persistence
    localStorage.setItem("cookie-consent", "accepted")
    //place for the scripts
  }

  const handleReject = () => {
    setShowBanner(false)
  }

  if (!showBanner) return null

  return (
    <div
      className={
        "fixed -bottom-full duration-700 transition-all ease-in-out left-0 bg-white w-full min-h-[140px] border-b-[10px] border-secondary rounded-s-md lg:min-h-[90px]"
      }
      ref={itemRef}
    >
      <div className="flex flex-col items-center p-6 gap-4 lg:gap-10 justify-between max-w-[1440px] mx-auto lg:flex-row">
        <p className="text-xsmall-regular text-gray-50 text-center w-3/4 lg:w-full lg:text-base-regular">
          Ta strona korzysta z ciasteczek aby świadczyć usługi na najwyższym
          poziomie. Dalsze korzystanie ze strony oznacza, że zgadzasz się na ich
          użycie.
        </p>
        <div className="flex items-center gap-10">
          <Button
            variant="outline"
            className="text-xsmall-regular font-semibold lg:text-base-regular lg:!px-5 lg:font-bold"
            onClick={handleAccept}
          >
            Zgoda
          </Button>
          <button
            onClick={handleReject}
            className="absolute right-3 top-3 lg:relative lg:right-auto lg:top-auto"
          >
            <X className="w-4 h-4 text-gray-50 lg:w-6 lg:h-6" />
          </button>
        </div>
      </div>
    </div>
  )
}

export default CookieBanner
