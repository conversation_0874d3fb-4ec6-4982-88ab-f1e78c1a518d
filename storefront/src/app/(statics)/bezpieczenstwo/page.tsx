import { Metadata } from "next"
import { getBaseURL } from "@lib/util/env"

export const metadata: Metadata = {
  title: "Bezpieczeństwo",
  description:
    "Bezpieczeństwo Polityka ujawniania luk Bezpieczeństwo informacji naszych klientów jest dla nas niezwykle ważne. Nieustannie dążymy do jak najwyższego bezpieczeństwa i jakości. Pomimo naszych nieustannych wysiłków problemy mogą pozostać. Dlatego też, jeśli znalazłeś lukę w zabe<PERSON>ch, zdecydowanie chcielibyśmy dowiedzieć się o niej więcej i jak najszybciej rozwiązać problem. Jak zgłosić lukę? W celu odpowiedzialnego ujawnienia wyślij [...]Czytaj dalej…",
  openGraph: {
    locale: "pl_PL",
    type: "article",
    title: "Bezpieczeństwo",
    description:
      "Bezpieczeństwo Polityka ujawniania luk Bezpieczeństwo informacji naszych klientów jest dla nas niezwykle ważne. Nieustannie dążymy do jak najwyższego bezpieczeństwa i jakości. Pomimo naszych nieustannych wysiłków problemy mogą pozostać. Dlatego też, jeśli znalazłeś lukę w zabezpieczeniach, zdecydowanie chcielibyśmy dowiedzieć się o niej więcej i jak najszybciej rozwiązać problem. Jak zgłosić lukę? W celu odpowiedzialnego ujawnienia wyślij [...]Czytaj dalej…",
  },
  twitter: {
    card: "summary_large_image",
  },
  metadataBase: new URL(getBaseURL()),
}

const Security = () => {
  return (
    <article>
      <h1>Bezpieczeństwo</h1>
      <p>
        Polityka ujawniania luk Bezpieczeństwo informacji naszych klientów jest
        dla nas niezwykle ważne. Nieustannie dążymy do jak najwyższego
        bezpieczeństwa i jakości. Pomimo naszych nieustannych wysiłków problemy
        mogą pozostać. Dlatego też, jeśli znalazłeś lukę w zabezpieczeniach,
        zdecydowanie chcielibyśmy dowiedzieć się o niej więcej i jak najszybciej
        rozwiązać problem.
      </p>
      <h2>Jak zgłosić lukę?</h2>
      <p>
        W celu odpowiedzialnego ujawnienia wyślij wiadomość e-mail z opisem
        problemu <NAME_EMAIL>, opisując krok po
        kroku, jak rozwiązać problem. W takim przypadku niezwłocznie
        skontaktujemy się z Tobą.
      </p>
      <p>
        W międzyczasie nie ujawniaj luk innym osobom i nie wykorzystuj luk dalej
        niż jest to konieczne do udowodnienia ich istnienia. Nie wolno zmieniać
        ani niszczyć danych pochodzących od prawowitych użytkowników serwisu.
        Będziemy współpracować z Tobą, aby rozwiązać ten problem.
      </p>
      <p>
        O ile nie wymaga tego prawo, nie podejmiemy działań prawnych przeciwko
        Tobie, osobie, która zgłosiła lukę. Będziemy jednak ścigać każdego, kto
        nadużywa jakichkolwiek luk w zabezpieczeniach systemu. Domyślnie nie
        wypłacamy nagród. Możemy zdecydować się na zawarcie umowy konsultingowej
        i wypłatę nagrody w przypadku wykrycia poważnego problemu.
      </p>
    </article>
  )
}

export default Security
