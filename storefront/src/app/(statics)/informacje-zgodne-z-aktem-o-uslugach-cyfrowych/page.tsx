import { getBaseURL } from "@lib/util/env"
import { Metadata } from "next"
import React from "react"

export const metadata: Metadata = {
  title: "Informacje zgodne z aktem o usługach cyfrowych",
  description:
    "Liczba aktywnych miesięcznie odbiorców usługi wakacyjnepomysly na terenie Unii Europejskiej zgodnie z art. 24 ust. 2 RODO średnio w okresie od 1 sierpnia 2022 r. do 31 stycznia 2023 r. wyniosła poniżej 10 mln. Wakacyjnepomysly będzie regularnie aktualizować te informacje zgodnie z art. 24 ust. 2 RODO. [...]Czytaj dalej…",
  openGraph: {
    locale: "pl_PL",
    type: "article",
    title: "Informacje zgodne z aktem o usługach cyfrowych",
    description:
      "Liczba aktywnych miesięcznie odbiorców usługi wakacyjnepomysly na terenie Unii Europejskiej zgodnie z art. 24 ust. 2 RODO średnio w okresie od 1 sierpnia 2022 r. do 31 stycznia 2023 r. wyniosła poniżej 10 mln. Wakacyjnepomysly będzie regularnie aktualizować te informacje zgodnie z art. 24 ust. 2 RODO. [...]Czytaj dalej…",
    siteName: "wakacyjnepomysly.pl",
  },
  twitter: {
    card: "summary_large_image",
  },
  metadataBase: new URL(getBaseURL()),
}

const InformationPage = () => {
  return (
    <article>
      <h1>Informacje prawne</h1>
      <p>
        Liczba aktywnych miesięcznie odbiorców usługi wakacyjnepomysly na
        terenie Unii Europejskiej zgodnie z art. 24 ust. 2 RODO średnio w
        okresie od 1 sierpnia 2022 r. do 31 stycznia 2023 r. wyniosła poniżej 10
        mln. Wakacyjnepomysly będzie regularnie aktualizować te informacje
        zgodnie z art. 24 ust. 2 RODO.
      </p>
    </article>
  )
}

export default InformationPage
