import { CartItem, ExtendedCart, FoodOption } from "types/cart"
import { useCartContext } from "context/cart-context"
import { ProductPricingVariant } from "types/global"
import { parseDateString } from "lib/utils"

// Constants
const HIGH_SEASON = {
  from: "07-01",
  to: "08-31",
}

export interface CartCalculations {
  // Cart metadata
  isSelectedPlace: boolean
  isSelectedDate: boolean
  isSelectedTime: boolean
  isDynamicGroup: boolean
  isBoatRental: boolean
  isRegularVariant: boolean

  // Pricing
  calculatedAdvance: number
  remainingAmount: number
  selectedFoodOptions: FoodOption[]
  totalAmount: number

  // Currency conversion
  exchangeRate: number
  exchangeRateUpdatedAt: string | null
  getPriceInPLN: (price: number) => number

  // Formatted dates
  formattedDate: string | null
  formattedDateRange: string | null
}

export const useCartCalculations = (cart: ExtendedCart): CartCalculations => {
  const { exchangeRate: exchangeRateContext } = useCartContext() ?? {
    exchangeRate: null,
  }
  const exchangeRate = exchangeRateContext?.rate ?? 1
  const exchangeRateUpdatedAt = exchangeRateContext?.updated_at || null

  const item = cart?.items?.[0]

  // Check if required data is selected
  const isSelectedPlace = Boolean(cart.metadata?.start_place)
  const isSelectedDate = Boolean(cart.metadata?.selected_date)
  const isSelectedTime = Boolean(cart.metadata?.start_time)

  // Determine product type
  const isDynamicGroup =
    item?.product.tour.product_pricing_variant ===
    ProductPricingVariant.DYNAMIC_AGE_GROUPS
  const isBoatRental =
    item?.product.tour.product_pricing_variant ===
    ProductPricingVariant.BOAT_RENTAL
  const isRegularVariant =
    item?.product.tour.product_pricing_variant ===
    ProductPricingVariant.REGULAR_VARIANTS

  // Format dates
  const formattedDate = isSelectedDate
    ? parseDateString(cart.metadata?.selected_date as string)
    : null

  const formattedDateRange = isBoatRental
    ? `${parseDateString(
        cart.metadata?.date_from as string
      )} - ${parseDateString(cart.metadata?.date_to as string)}`
    : null

  const fallbackStartDate = cart.metadata?.date_from
    ? cart.metadata.date_from
    : cart.metadata?.selected_date
    ? cart.metadata.selected_date
    : null

  const selectedFoodOptions = cart.metadata?.selected_food_options ?? []

  // Calculate advance payment
  const calculateAdvance = (
    item: CartItem,
    startDate: string | null
  ): number => {
    return (
      ((item.product.tour.pricing.prepaid_percentage ?? 100) * cart.total) / 100
    )

    // const selectedDate = new Date(startDate)

    // // Check if the date is valid
    // if (isNaN(selectedDate.getTime())) {
    //   return 0
    // }

    // const currentYear = selectedDate.getFullYear()

    // const highSeasonStart = new Date(`${currentYear}-${HIGH_SEASON.from}`)
    // const highSeasonEnd = new Date(`${currentYear}-${HIGH_SEASON.to}`)

    // const isSelectedDateWithinHighSeason =
    //   selectedDate >= highSeasonStart && selectedDate <= highSeasonEnd

    // const advancePercentage = isSelectedDateWithinHighSeason
    //   ? item.product.tour.pricing.prepaid_percentage_high_season
    //   : item.product.tour.pricing.prepaid_percentage ?? 0

    // const advance = (advancePercentage / 100) * cart.total
    // return Math.round(advance * 100) / 100 // Round to 2 decimal places
  }

  const calculatedAdvance = item ? calculateAdvance(item, fallbackStartDate) : 0
  const remainingAmount = cart.total - calculatedAdvance
  const totalAmount = cart.total

  // Currency conversion
  const getPriceInPLN = (price: number): number => {
    return Math.round(price * exchangeRate * 100) / 100
  }

  return {
    // Cart metadata
    isSelectedPlace,
    isSelectedDate,
    isSelectedTime,
    isDynamicGroup,
    isBoatRental,
    isRegularVariant,
    // Pricing
    calculatedAdvance,
    selectedFoodOptions,
    remainingAmount,
    totalAmount,

    // Currency conversion
    exchangeRate,
    exchangeRateUpdatedAt,
    getPriceInPLN,

    // Formatted dates
    formattedDate,
    formattedDateRange,
  }
}
