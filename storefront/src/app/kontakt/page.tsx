import { getBaseURL } from "@lib/util/env"
import OurSocialMedia from "components/reusable/our-social-media"
import SubpageHero from "components/reusable/subpage-hero"
import { Metadata } from "next"

export const metadata: Metadata = {
  title: "Kontakt - wakacyjnepomysly.pl",
  description: "Skontaktuj się z nami",
  openGraph: {
    locale: "pl_PL",
    type: "article",
    title: "Kontakt",
    description: "Skontaktuj się z nami",
    siteName: "wakacyjnepomysly.pl",
    url: `${getBaseURL()}/kontakt`,
  },
  twitter: {
    card: "summary_large_image",
  },
  metadataBase: new URL(getBaseURL()),
}

const ContactPage = () => {
  return (
    <>
      <SubpageHero imageSrc={"/images/contact_hero.webp"} title="Kontakt" />
      <OurSocialMedia isContact />
    </>
  )
}

export default ContactPage
