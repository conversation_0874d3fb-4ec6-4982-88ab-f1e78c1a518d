import SubpageHero from "components/reusable/subpage-hero"
import AboutUsMainSection from "./components/about-us-main-section"
import OurSocialMedia from "components/reusable/our-social-media"
import { Metadata } from "next"
import { getBaseURL } from "@lib/util/env"

export const metadata: Metadata = {
  title: "O nas - wakacyjnepomysly.pl",
  description:
    "Natalia i Artur. Jesteśmy dwojgiem kochających się ludzi, którzy posiadają wspólna zajawkę na odkrywanie świata! Z potrzeby dzielenia się pasją, w czerwcu 2019 roku, powstał nasz instagram @wakacyjnepomysly.pl (dawniej @lifewillshowpl), gdzie relacjonujemy nasze podróże, nasze życiei dzielimy się przydatnymi wskazówkami.",
  openGraph: {
    locale: "pl_PL",
    type: "article",
    title: "O nas",
    description:
      "Natalia i Artur. Jesteśmy dwojgiem kochających się ludzi, którzy posiadają wspólna zajawkę na odkrywanie świata! Z potrzeby dzielenia się pasją, w czerwcu 2019 roku, powstał nasz instagram @wakacyjnepomysly.pl (dawniej @lifewillshowpl), gdzie relacjonujemy nasze podróże, nasze życiei dzielimy się przydatnymi wskazówkami.",
    siteName: "wakacyjnepomysly.pl",
    url: `${getBaseURL()}/o-nas`,
  },
  twitter: {
    card: "summary_large_image",
  },
  metadataBase: new URL(getBaseURL()),
}
const AboutUsPage = () => {
  return (
    <>
      <SubpageHero imageSrc={"/images/about_us_hero.webp"} title="O nas" />
      <AboutUsMainSection />
      <OurSocialMedia />
    </>
  )
}

export default AboutUsPage
