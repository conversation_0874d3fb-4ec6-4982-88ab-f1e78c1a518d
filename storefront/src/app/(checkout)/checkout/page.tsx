"use client"

import React, { useEffect, useState } from "react"
import { But<PERSON> } from "@medusajs/ui"
import CheckoutForm from "./components/checkout-form"
import PolicyConsentCard from "./components/policy-consent-card"
import CheckoutSummary from "./components/checkout-summary"
import { useCheckoutForm } from "./hooks/useCheckoutForm"
import { useCartContext } from "context/cart-context"
import CheckoutFooter from "./components/checkout-footer"
import { useRouter } from "next/navigation"
import SubpageHero from "components/reusable/subpage-hero"
import CheckoutSkeleton from "./components/checkout-skeleton"
import { KEYS } from "hooks/useFlagSessionStorage"
import useFlagSessionStorage from "hooks/useFlagSessionStorage"
import useStoreSettings from "hooks/useStoreSettings"

const CheckoutPage = () => {
  const form = useCheckoutForm()
  const router = useRouter()
  const { cart, isLoading } = useCartContext()
  const { clearFlag, flag } = useFlagSessionStorage(
    KEYS.REDIRECT_TO_TPAY_PAYMENT_GATEWAY
  )
  const { storeSettings, isLoading: isStoreSettingsLoading } = useStoreSettings()
  
  const isShopEnabled = storeSettings?.is_shop_enabled ?? true

  // Track redirection states to prevent conflicting redirects
  const [redirectedToStatusPage, setRedirectedToStatusPage] = useState(false)
  const [isRedirecting, setIsRedirecting] = useState(false)

  // Handle payment gateway redirect (when returning from Tpay)
  useEffect(() => {
    if (flag && !redirectedToStatusPage && !isRedirecting) {
      setIsRedirecting(true)
      setRedirectedToStatusPage(true)
      clearFlag()
      router.push("/status-zamowienia?canceled=true")
    }
  }, [flag, redirectedToStatusPage, isRedirecting, clearFlag, router])

  // Handle empty cart redirect
  useEffect(() => {
    // Only redirect to cart page if we're not already redirecting somewhere else
    // and we haven't already redirected to the status page
    if (
      cart !== "fetching" &&
      cart === null &&
      !isRedirecting &&
      !redirectedToStatusPage
    ) {
      setIsRedirecting(true)
      router.push("/koszyk")
    }
  }, [cart, isRedirecting, redirectedToStatusPage, router])

  // Handle shop disabled redirect
  useEffect(() => {
    if (!isStoreSettingsLoading && !isShopEnabled && !isRedirecting) {
      setIsRedirecting(true)
      router.push("/chorwacja-atrakcje")
    }
  }, [isShopEnabled, isStoreSettingsLoading, isRedirecting, router])

  if (isLoading || cart === "fetching" || !cart || isStoreSettingsLoading) {
    return <CheckoutSkeleton />
  }

  // Show loading or redirect if shop is disabled
  if (!isShopEnabled) {
    return <CheckoutSkeleton />
  }

  return (
    <>
      <SubpageHero imageSrc={"/images/checkout_bg.webp"} title="Checkout" />
      <div className="grid container lg:grid-cols-2 gap-8 pb-10 md:pb-20">
        <div className="grid gap-8">
          <CheckoutForm form={form} />
          <PolicyConsentCard form={form} />
        </div>
        <CheckoutSummary cart={cart} />
        <CheckoutFooter form={form} setIsRedirecting={setIsRedirecting} />
      </div>
    </>
  )
}

export default CheckoutPage
