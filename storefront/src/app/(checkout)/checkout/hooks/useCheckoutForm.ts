import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { COUNTRIES_MOBILE_CODE } from "@lib/constants/countriesMobileCode"

const countryCodes = Object.values(COUNTRIES_MOBILE_CODE)
  .filter((code) => code && code.trim() !== "")
  .map((code) => {
    const match = code.match(/^\+?(\d+)/)
    return match ? match[1] : code.replace(/^\+/, "")
  })
  .filter(Boolean)

export const checkoutFormSchema = z
  .object({
    firstName: z.string().min(1, "Imię jest wymagane"),
    lastName: z.string().min(1, "Nazwisko jest wymagane"),
    phoneNumber: z
      .string()
      .min(1, "Numer telefonu jest wymagany")
      .regex(/^\+[0-9\s-]+$/, "Numer telefonu wraz z prefiksem, np. +48")
      .min(11, "Numer telefonu musi mieć co najmniej 8 cyfr")
      .refine((val) => {
        const lengthWithoutWhitespace = val.replace(/\s+/g, "").length
        return lengthWithoutWhitespace >= 11
      }, "Numer telefonu musi mieć co najmniej 8 cyfr")
      .refine((val) => {
        const numberPart = val.substring(1)
        return countryCodes.some((code) => numberPart.startsWith(code))
      }, "Nieprawidłowy kod kraju"),

    email: z
      .string()
      .min(1, "Adres e-mail jest wymagany")
      .email("Nieprawidłowy format adresu e-mail"),
    confirmEmail: z
      .string()
      .min(1, "Potwierdzenie adresu e-mail jest wymagane"),
    policyConsent: z.boolean().refine((val) => val === true, {
      message: "Musisz zaakceptować regulamin",
    }),
    additionalDescription: z.string().optional(),
  })
  .refine((data) => data.email === data.confirmEmail, {
    message: "Adresy e-mail muszą być identyczne",
    path: ["confirmEmail"],
  })

export type CheckoutFormData = z.infer<typeof checkoutFormSchema>

export const useCheckoutForm = () => {
  const methods = useForm<CheckoutFormData>({
    resolver: zodResolver(checkoutFormSchema),
    mode: "onChange",
    defaultValues: {
      firstName: "",
      lastName: "",
      phoneNumber: "",
      email: "",
      confirmEmail: "",
      policyConsent: false,
      additionalDescription: "",
    },
  })

  const onSubmit = (data: CheckoutFormData) => {
    console.log("Form submitted:", data)
    // Handle form submission logic here
  }

  return {
    ...methods,
    onSubmit: methods.handleSubmit(onSubmit),
  }
}
