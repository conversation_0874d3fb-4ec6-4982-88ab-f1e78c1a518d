import React from "react"
import { clx, Input, Text, Textarea } from "@medusajs/ui"
import { UseFormRegister } from "react-hook-form"
import { CheckoutFormData } from "../hooks/useCheckoutForm"

interface FormFieldProps {
  label: string
  name: keyof CheckoutFormData
  type?: string
  register: UseFormRegister<CheckoutFormData>
  error?: string
  required?: boolean
  rows?: number
  placeholder?: string
  className?: string
  inputMode?: "text" | "email" | "tel"
}

const FormField = ({
  label,
  name,
  type = "text",
  register,
  error,
  required = true,
  rows,
  className,
  inputMode,
  placeholder,
}: FormFieldProps) => {
  const ResolvedComponent = type === "textarea" ? Textarea : Input

  return (
    <>
      <div className="space-y-2">
        <ResolvedComponent
          type={type}
          inputMode={inputMode}
          placeholder={`${placeholder || label} ${required ? "*" : ""} `}
          className={clx(
            "w-full border px-4 py-4 rounded-xxl min-h-10 font-light",
            className
          )}
          {...register(name)}
          aria-invalid={error ? "true" : "false"}
          required={required}
          {...(type === "textarea" && { rows })}
        />
        {error && <Text className="text-rose-500 text-sm">{error}</Text>}
      </div>
    </>
  )
}

export default FormField
