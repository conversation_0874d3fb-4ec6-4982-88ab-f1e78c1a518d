"use client"

import React from "react"
import { Heading, Text } from "@medusajs/ui"
import { UseFormReturn } from "react-hook-form"
import { CheckoutFormData } from "../hooks/useCheckoutForm"
import <PERSON>Field from "./form-field"

type Props = {
  form: UseFormReturn<CheckoutFormData>
}

const CheckoutForm = ({ form }: Props) => {
  const {
    register,
    formState: { errors },
  } = form

  return (
    <div className="p-5 border rounded-xl pb-12">
      <Heading
        level="h1"
        className="text-heading_quaternary xl:text-heading_main font-bold"
      >
        <PERSON>
      </Heading>

      <Text className="text-gray-50 my-8"><PERSON>:</Text>

      <div className="space-y-6">
        <FormField
          label="Imię"
          name="firstName"
          register={register}
          error={errors.firstName?.message}
        />

        <FormField
          label="Nazwisko"
          name="lastName"
          register={register}
          error={errors.lastName?.message}
        />

        <FormField
          label="Numer telefonu"
          name="phoneNumber"
          type="tel"
          inputMode="tel"
          placeholder="Wpisz numer telefonu (wraz z kodem kraju)"
          register={register}
          error={errors.phoneNumber?.message}
        />

        <FormField
          label="Adres e-mail"
          name="email"
          type="email"
          register={register}
          error={errors.email?.message}
        />

        <FormField
          label="Potwierdź adres e-mail"
          name="confirmEmail"
          type="email"
          register={register}
          error={errors.confirmEmail?.message}
        />

        <FormField
          label="Notatka dla organizatora"
          name="additionalDescription"
          type="textarea"
          rows={4}
          className="!rounded-xl"
          register={register}
          error={errors.additionalDescription?.message}
          required={false}
        />
      </div>
    </div>
  )
}

export default CheckoutForm
