"use client"

import React from "react"
import { Heading, Text, Checkbox, Label } from "@medusajs/ui"
import Link from "next/link"
import { Controller, UseFormReturn } from "react-hook-form"
import { CheckoutFormData } from "../hooks/useCheckoutForm"
import Badge from "app/(checkout)/koszyk/components/badge"

type Props = {
  form: UseFormReturn<CheckoutFormData>
}

const PolicyConsentCard = ({ form }: Props) => {
  const {
    control,
    formState: { errors },
  } = form

  return (
    <div className="p-5 border rounded-xl pb-12 grid gap-5">
      <Heading
        level="h1"
        className="text-heading_quaternary xl:text-heading_main font-bold"
      >
        Regulamin i zgody
      </Heading>

      <Badge>
        <Text className="text-gray-50 font-light">
          Twoje dane osobowe będą użyte do przetworzenia twojego zamówienia,
          obsługi twojej wizyty na naszej stronie oraz dla innych celów o
          których mówi nasza{" "}
          <Link
            href="/polityka-prywatnosci"
            className="underline text-primary "
          >
            polityka prywatności
          </Link>
          .
        </Text>
      </Badge>

      <div className="flex items-center gap-2  p-4 border border-secondary rounded-2xl">
        <Controller
          control={control}
          name="policyConsent"
          render={({ field: { onChange, value } }) => (
            <Checkbox
              className="size-5 shrink-0 rounded-md border-2 border-gray-50"
              id="policy-consent"
              checked={value}
              onCheckedChange={(checked) => {
                onChange(checked)
              }}
            />
          )}
        />
        <Label htmlFor="policy-consent">
          <Text className="text-gray-50 font-light">
            Przeczytałem/am i akceptuję{" "}
            <Link href="/regulamin" className="underline text-primary">
              regulamin
            </Link>
            * .
          </Text>
        </Label>
      </div>
      {errors.policyConsent && (
        <Text className="text-rose-500 text-sm">
          {errors.policyConsent.message}
        </Text>
      )}
    </div>
  )
}

export default PolicyConsentCard
