"use client"

import { Heading, Text } from "@medusajs/ui"
import { ExtendedCart } from "types/cart"
import { useCartCalculations } from "app/koszyk/hooks/useCartCalculations"
import Image from "next/image"
import MoneyBillIcon from "@icons/money_bill.svg"

import CalendarIcon from "@icons/calendar.svg"
import ClockIcon from "@icons/clock.svg"
import LocationIcon from "@icons/map_pin.svg"
import UserIcon from "@icons/user.svg"
import IconWithText from "app/(checkout)/koszyk/components/icon-with-text"
import PriceRow from "app/(checkout)/koszyk/components/price-row"
import { ListTodo, Drumstick } from "lucide-react"
import { formatPrice } from "@lib/util/money"
import { removeDateInParentheses } from "@lib/util/remove-date-in-parentheses"

type Props = {
  cart: ExtendedCart
}

const CheckoutSummary = ({ cart }: Props) => {
  const tourDetails = cart.items.at(0)?.product?.tour

  const cartItem = cart.items.at(0)

  const {
    calculatedAdvance,
    totalAmount,
    remainingAmount,
    getPriceInPLN,
    isSelectedDate,
    formattedDate,
    isBoatRental,
    formattedDateRange,
    isSelectedTime,
    isSelectedPlace,
    isDynamicGroup,
    isRegularVariant,
    selectedFoodOptions,
  } = useCartCalculations(cart)

  return (
    <div className="p-5 border rounded-xl h-fit pb-12">
      <Heading
        level="h2"
        className="text-heading_quaternary xl:text-heading_main font-bold mb-6"
      >
        Twoje zamówienie
      </Heading>

      <div className="flex flex-col gap-10 my-10">
        <Text className="text-gray-50">Produkt:</Text>
        <Text className="">{tourDetails?.name}</Text>
      </div>

      {isSelectedDate || isBoatRental || isSelectedTime || isSelectedPlace ? (
        <div className="flex flex-col gap-10 my-10">
          <Text className="text-gray-50">Data i miejsce atrakcji:</Text>
          <div className="grid sm:grid-cols-3 gap-4">
            {isSelectedDate && (
              <IconWithText
                icon={CalendarIcon}
                alt="Kalendarz - wybrana data"
                text={formattedDate!}
                className="font-semibold"
              />
            )}
            {isBoatRental && (
              <IconWithText
                icon={CalendarIcon}
                alt="Kalendarz - wybrana data"
                text={formattedDateRange!}
                className="font-semibold"
              />
            )}
            {isSelectedTime && (
              <IconWithText
                icon={ClockIcon}
                alt="Czas - wybrany czas"
                text={cart.metadata?.start_time as string}
                className="font-semibold"
              />
            )}
            {isSelectedPlace && (
              <IconWithText
                icon={LocationIcon}
                alt="Miejsce - wybrane miejsce"
                text={cart.metadata?.start_place?.place ?? ""}
                className="font-semibold"
              />
            )}
          </div>
        </div>
      ) : null}

      {isRegularVariant && (
        <div className="flex flex-wrap gap-4">
          <IconWithText
            lucideIcon={<ListTodo className="size-5 stroke-secondary" />}
            alt="Wybrany wariant"
            text={"Wybrany wariant:"}
          />
          <p className="text-sm text-primary font-semibold">
            {cartItem?.title}
          </p>
        </div>
      )}

      {isDynamicGroup && (
        <div className="flex flex-col gap-10 my-10">
          <Text className="text-gray-50">Liczba uczestników:</Text>

          <div className="flex flex-wrap gap-4">
            {cart.items.map((item) => (
              <div key={item.id} className="flex items-center gap-4">
                <IconWithText
                  icon={UserIcon}
                  alt="Użytkownik - liczba osób"
                  text={removeDateInParentheses(item.title)}
                />
                <p className="text-sm text-primary font-semibold">
                  {item.quantity}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="flex flex-wrap gap-4">
        {selectedFoodOptions?.length > 0 && (
          <IconWithText
            alt="Posiłek"
            text="Posiłki"
            lucideIcon={
              <Drumstick className="size-5 stroke-secondary stroke-[2px]" />
            }
          />
        )}

        {(selectedFoodOptions ?? []).map((option) => (
          <div key={option?.id} className="flex items-center gap-4">
            <p className="text-sm text-primary font-semibold">{option?.name}</p>
            <p className="text-sm text-primary font-semibold">
              {option?.quantity}
            </p>
          </div>
        ))}
      </div>

      <hr className="my-14" />

      <div className="flex flex-col gap-6 max-sm:gap-5">
        <div className="flex justify-between  sm:items-center flex-col max-sm:gap-5 sm:flex-row">
          <PriceRow
            label="Pełna cena"
            pricePLN={getPriceInPLN(totalAmount)}
            priceEUR={totalAmount}
          />
        </div>

        <div className="flex justify-between  sm:items-center flex-col max-sm:gap-5 sm:flex-row">
          <PriceRow
            label="Dopłata w dniu atrakcji"
            pricePLN={getPriceInPLN(remainingAmount)}
            priceEUR={remainingAmount}
          />
        </div>

        <div className="flex justify-between  sm:items-center flex-col max-sm:gap-5 sm:flex-row">
          <PriceRow
            label="Zaliczka"
            pricePLN={getPriceInPLN(calculatedAdvance)}
            priceEUR={calculatedAdvance}
          />
        </div>

        <hr className="my-4" />

        <div className="flex justify-between max-sm:gap-8 sm:items-center flex-col  sm:flex-row">
          <div className="flex items-center gap-2">
            <Image
              src={MoneyBillIcon}
              alt="Money Bill"
              width={20}
              height={20}
            />
            <Text className="font-bold">Dziś zapłacisz</Text>
          </div>
          <div className="text-lg sm:text-2xl  space-x-2">
            <span className="text-green font-bold ">
              {formatPrice(getPriceInPLN(calculatedAdvance))}
            </span>
            <span className="text-gray-50 font-light">PLN</span>
            <span className="text-gray-50 font-bold">
              ({formatPrice(calculatedAdvance)} €)
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CheckoutSummary
