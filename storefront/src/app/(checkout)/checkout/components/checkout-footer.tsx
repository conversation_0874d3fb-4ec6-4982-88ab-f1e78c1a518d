"use client"

import Button from "components/reusable/button"
import React, { useEffect, useRef } from "react"
import Image from "next/image"
import { useCartContext } from "context/cart-context"
import { initiatePaymentCollection } from "@lib/data/payment"
import { useRouter } from "next/navigation"
import { CheckoutFormData } from "../hooks/useCheckoutForm"
import { FieldErrors, UseFormReturn } from "react-hook-form"
import CartIcon from "@icons/cart_black.svg"
import { AttractionDetails } from "types/global"
import Link from "next/link"
import useFlagSessionStorage, { KEYS } from "hooks/useFlagSessionStorage"
import useStoreSettings from "hooks/useStoreSettings"

type Props = {
  form: UseFormReturn<CheckoutFormData>
  setIsRedirecting: (isRedirecting: boolean) => void
}

const CheckoutFooter = ({ form, setIsRedirecting }: Props) => {
  const { cart, clearCart } = useCartContext()

  const { setFlag } = useFlagSessionStorage(
    KEYS.REDIRECT_TO_TPAY_PAYMENT_GATEWAY
  )
  const [isLoading, setIsLoading] = React.useState(false)
  const router = useRouter()
  const { storeSettings } = useStoreSettings()
  
  const isShopEnabled = storeSettings?.is_shop_enabled ?? true

  const onInvalidInput = (errors: FieldErrors<CheckoutFormData>) => {
    console.log(errors)
  }

  const beginPaymentCollection = async (data: CheckoutFormData) => {
    if (!cart || cart === "fetching") {
      return
    }
    
    // Additional guard against shop being disabled
    if (!isShopEnabled) {
      console.error("Cannot initiate payment: shop is disabled")
      router.push("/chorwacja-atrakcje")
      return
    }
    
    setIsLoading(true)

    const tourDetails = cart.items.at(0)?.product?.tour as AttractionDetails

    const createdPaymentDescription = cart.items
      .map((item) => {
        return item.title
      })
      .join(", ")

    const tourId = String(tourDetails?.id)

    const max120Characters = 120
    let paymentDescription = `Zakup biletów dla atrakcji: ${tourDetails.name}. ${createdPaymentDescription}`

    if (paymentDescription.length > max120Characters) {
      paymentDescription = paymentDescription.slice(0, max120Characters)
    }

    const selectedDate = cart.metadata?.selected_date
      ? cart.metadata?.selected_date
      : cart.metadata?.date_from
      ? cart.metadata.date_from
      : null

    initiatePaymentCollection(cart, {
      provider_id: "pp_bramka_platnosci_tpay",
      data: {
        email: data.email,
        cart_id: cart.id,
      },
      context: {
        cart_id: cart.id,
        tour_id: tourId,
        email: data.email.trim(),
        phone_number: data.phoneNumber.trim().replace(/\s+/g, ""),
        first_name: data.firstName.trim(),
        last_name: data.lastName.trim(),
        payment_description: paymentDescription,
        date_for_advance_calculation: selectedDate,
        additional_description: data.additionalDescription,
      },
    })
      .catch((error) => console.log(error))
      .then((response) => {
        setIsRedirecting(true)
        router.push(
          response.payment_collection?.payment_sessions.at(0)?.data
            .transactionPaymentUrl
        )
        clearCart()
        setFlag()
      })
      .finally(() => {
        setIsLoading(false)
      })
  }

  return (
    <>
      <div className="justify-between col-span-full flex items-center flex-col xl:flex-row gap-4 border p-5 rounded-xl font-semibold">
        <div className="flex gap-2 self-start">
          <Link
            href={"/koszyk"}
            className="text-primary px-4 py-2 border-secondary border rounded-xxl min-w-48 text-center"
          >
            Anuluj
          </Link>
        </div>

        <Button
          isLoading={isLoading}
          className="min-w-48 flex items-center gap-2 justify-center px-8 ml-auto w-full self-end xl:w-fit"
          cta
          disabled={!isShopEnabled}
          onClick={form.handleSubmit(beginPaymentCollection, onInvalidInput)}
        >
          <Image src={CartIcon} className="size-5" alt="coins" />
          {!isShopEnabled ? "Rezerwacje dostępne od 01.04" : "Kupuję z obowiązkiem zapłaty"}
        </Button>
      </div>
    </>
  )
}

export default CheckoutFooter
