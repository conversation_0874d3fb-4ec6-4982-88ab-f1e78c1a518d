import React from "react"

const CheckoutSkeleton = () => {
  return (
    <div className="container mx-auto py-10 animate-pulse">
      <div className="grid lg:grid-cols-[1fr,400px] gap-8">
        {/* Left column - Form skeletons */}
        <div className="space-y-8">
          {/* Checkout form skeleton */}
          <div className="max-w-2xl mx-auto p-5 border rounded-xl pb-12">
            {/* Heading skeleton */}
            <div className="h-8 bg-gray-200 rounded-md w-3/4 mb-8"></div>

            {/* Form fields skeleton */}
            <div className="space-y-6">
              {[1, 2, 3, 4, 5].map((i) => (
                <div
                  key={i}
                  className="h-12 bg-gray-200 rounded-md w-full"
                ></div>
              ))}
            </div>
          </div>

          {/* Policy consent skeleton */}
          <div className="max-w-2xl mx-auto p-5 border rounded-xl pb-12">
            {/* Heading skeleton */}
            <div className="h-8 bg-gray-200 rounded-md w-1/2 mb-6"></div>

            {/* Policy text skeleton */}
            <div className="h-24 bg-gray-200 rounded-md w-full mb-6"></div>

            {/* Checkbox skeleton */}
            <div className="flex items-center gap-2">
              <div className="h-5 w-5 bg-gray-200 rounded-md"></div>
              <div className="h-4 bg-gray-200 rounded-md w-3/4"></div>
            </div>
          </div>

          {/* Mobile button skeleton (visible on mobile only) */}
          <div className="max-w-2xl mx-auto lg:hidden">
            <div className="h-12 bg-gray-200 rounded-md w-full"></div>
          </div>
        </div>

        {/* Right column - Summary skeleton */}
        <div className="space-y-8">
          {/* Summary skeleton */}
          <div className="p-5 border rounded-xl pb-12">
            {/* Heading skeleton */}
            <div className="h-8 bg-gray-200 rounded-md w-3/4 mb-6"></div>

            {/* Price rows skeleton */}
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex justify-between items-center">
                  <div className="h-4 bg-gray-200 rounded-md w-1/3"></div>
                  <div className="h-4 bg-gray-200 rounded-md w-1/4"></div>
                </div>
              ))}

              <div className="my-4 h-px bg-gray-200 w-full"></div>

              {/* Total price skeleton */}
              <div className="flex justify-between items-center">
                <div className="h-5 bg-gray-200 rounded-md w-1/3"></div>
                <div className="h-6 bg-gray-200 rounded-md w-1/4"></div>
              </div>

              {/* Exchange rate skeleton */}
              <div className="mt-6 bg-gray-100 p-4 rounded-lg">
                <div className="h-4 bg-gray-200 rounded-md w-2/3"></div>
              </div>
            </div>
          </div>

          {/* Desktop button skeleton (visible on desktop only) */}
          <div className="hidden lg:block">
            <div className="h-12 bg-gray-200 rounded-md w-full"></div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CheckoutSkeleton
