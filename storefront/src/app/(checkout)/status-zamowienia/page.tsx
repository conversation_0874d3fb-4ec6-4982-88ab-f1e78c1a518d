"use client"

import { getPaymentStatus } from "@lib/data/payment"
import { useQuery } from "@tanstack/react-query"
import { TimedStatusPopover } from "components/reusable/timed-status-popover"
import { useSearchParams } from "next/navigation"
import React, { useEffect, useLayoutEffect } from "react"

import ThumbsUpIcon from "@icons/thumbs_up.svg"
import NoResponseIcon from "@icons/no_response.svg"
import PaymentCanceledIcon from "@icons/payment_canceled.svg"
import Image from "next/image"
import LoadingDialog from "components/reusable/loading-dialog"
import { Loader2 } from "lucide-react"
import { useCartContext } from "context/cart-context"
import Link from "next/link"
import useFlagSessionStorage, { KEYS } from "hooks/useFlagSessionStorage"
import { ROUTES } from "@lib/routes"
type Props = {}

const PaymentStatusPage = (props: Props) => {
  const searchParams = useSearchParams()

  const { clearFlag } = useFlagSessionStorage(
    KEYS.REDIRECT_TO_TPAY_PAYMENT_GATEWAY
  )

  const [isModalOpen, setIsModalOpen] = React.useState(false)

  const id = searchParams.get("session")
  const success = searchParams.get("success")
  const canceled = searchParams.get("canceled")

  const { clearCart } = useCartContext()

  const { data, isLoading } = useQuery({
    queryKey: ["payment-status", id],
    queryFn: () => getPaymentStatus(id!),
    enabled: !!id,
  })

  useLayoutEffect(() => {
    clearCart()
    clearFlag()
  }, [])

  useEffect(() => {
    if (!isLoading && data) {
      setIsModalOpen(true)
    }
  }, [isLoading])

  useEffect(() => {
    if (success === "true") {
      clearCart()
      clearFlag()
    }
  }, [success])

  const transactionPaymentUrl = data?.data?.data?.transactionPaymentUrl

  return (
    <div>
      {isLoading ? (
        <LoadingDialog isOpen={isLoading} onClose={() => setIsModalOpen(false)}>
          <div className="text-center">
            <Loader2 className="mx-auto mb-4 animate-spin" />
            <p>Pobieramy informacje o płatności</p>
          </div>
        </LoadingDialog>
      ) : success === "true" ? (
        <TimedStatusPopover
          title="PŁATNOŚĆ ZAKOŃCZONA SUKCESEM"
          description="Twoja płatność została pomyślnie zrealizowana! 
          Dalsze informacje otrzymasz drogą mailową."
          icon={
            <Image
              src={ThumbsUpIcon}
              alt="Płatność zakończona sukcesem"
              width={120}
              height={120}
            />
          }
          ctaText="Powrót"
          ctaHref="/"
          isOpen={true}
          onClose={() => setIsModalOpen(false)}
        />
      ) : canceled === "true" ? (
        <TimedStatusPopover
          title="PŁATNOŚĆ ANULOWANA"
          description="Twoja płatność została przerwana. Prosimy ponownie dodać produkt do koszyka i spróbować ponownie."
          icon={
            <Image
              src={PaymentCanceledIcon}
              alt="Płatność anulowana"
              width={120}
              height={120}
            />
          }
          ctaText="Wróć do strony atrakcji"
          ctaHref={ROUTES.atrakcje}
          isOpen={true}
          onClose={() => setIsModalOpen(false)}
        />
      ) : (
        <TimedStatusPopover
          title="Brak reakcji na płatność"
          description="Czas na zakończenie płatności upłynął. W dalszym ciągu możesz opłacić swoje zamówienie."
          icon={
            <Image
              src={NoResponseIcon}
              alt="Brak reakcji na płatność"
              width={120}
              height={120}
            />
          }
          ctaText="Wróć do listy atrakcji"
          ctaHref={ROUTES.atrakcje}
          isOpen={true}
          onClose={() => {
            setIsModalOpen(false)
            clearFlag()
          }}
        >
          <Link
            href={transactionPaymentUrl}
            className="inline-block mt-4 px-6 py-2 bg-background border-secondary border rounded-full text-sm font-medium transition-colors"
          >
            Spróbuj ponownie
          </Link>
        </TimedStatusPopover>
      )}
    </div>
  )
}

export default PaymentStatusPage
