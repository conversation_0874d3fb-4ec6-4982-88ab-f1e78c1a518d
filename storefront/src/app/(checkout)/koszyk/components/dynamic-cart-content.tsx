"use client"

import { CartTimer } from "app/(checkout)/koszyk/components/cart-timer"
import CartFullBadge from "./cart-full-badge"
import CartItemPreview from "./cart-item-preview"
import CartItemPreviewSkeleton from "./cart-item-preview-skeleton"
import EmptyCartMessage from "./empty-cart-message"
import { useCartContext } from "context/cart-context"

const DynamicCartContent = () => {
  const { cart, isLoading, isCartFull } = useCartContext()

  const renderCartContent = () => {
    if (cart === "fetching" || isLoading) {
      return <CartItemPreviewSkeleton />
    }

    if (!isLoading && !cart) {
      return <EmptyCartMessage />
    }

    if (cart && isCartFull) {
      return (
        <>
          <CartFullBadge />
          <CartItemPreview cart={cart} />
        </>
      )
    }
  }
  return (
    <div className="flex flex-col gap-4 mb-14 container grow">
      <CartTimer />
      {renderCartContent()}
    </div>
  )
}

export default DynamicCartContent
