import Image from "next/image"
import React from "react"

type IconWithTextProps = {
  icon: string
  alt: string
  text: string
  className?: string
  lucideIcon?: React.ReactNode
}

type BaseProps = {
  alt: string
  text: string
  className?: string
}

type LucideProps = BaseProps & {
  lucideIcon: React.ReactNode
  icon?: never
}

type ImageProps = BaseProps & {
  icon: string
  lucideIcon?: never
}

type Props = LucideProps | ImageProps

const IconWithText = ({
  icon,
  alt,
  text,
  lucideIcon,
  className = "",
}: Props) => {
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {lucideIcon ? (
        <>{lucideIcon}</>
      ) : (
        <Image src={icon} alt={alt} width={20} height={20} />
      )}
      <p className="text-sm text-primary">{text}</p>
    </div>
  )
}

export default IconWithText
