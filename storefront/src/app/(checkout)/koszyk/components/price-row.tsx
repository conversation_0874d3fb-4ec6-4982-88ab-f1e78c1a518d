import { formatPrice } from "@lib/util/money"
import Image from "next/image"

type PriceRowProps = {
  label: string
  icon?: string
  pricePLN: number | string
  priceEUR: number | string
  hidePLN?: boolean
}

const PriceRow = ({
  label,
  icon,
  pricePLN,
  priceEUR,
  hidePLN = true,
}: PriceRowProps) => {
  const getFormattedPrice = (price: number | string) => {
    if (typeof price === "string") {
      return price
    }
    return formatPrice(price)
  }

  return (
    <>
      <div className="flex items-center gap-2 text-sm max-sm:text-xs">
        {icon && <Image src={icon} alt={label} width={20} height={20} />}
        <p className="text-gray-50">{label}</p>
      </div>
      <div className="items-center text-gray-50 gap-1 text-sm  grid grid-cols-[auto,auto]  text-right max-sm:text-xs">
        {!hidePLN && <div>{getFormattedPrice(pricePLN)}</div>}
        {!hidePLN && <div>PLN</div>}
      </div>
      <div className="sm:grid sm:grid-cols-[auto,auto] text-gray-50 items-center gap-1 text-sm text-right max-sm:text-xs space-x-2">
        <div className="inline-block">{getFormattedPrice(priceEUR)}</div>
        <div className="inline-block">€</div>
      </div>
    </>
  )
}

export default PriceRow
