import React from "react"

type CurrencyDisplayProps = {
  amount: number | string
  currency: string
  className?: string
  isGreen?: boolean
}

const CurrencyDisplay = ({
  amount,
  currency,
  className = "",
  isGreen = false,
}: CurrencyDisplayProps) => (
  <div
    className={`flex gap-3 items-center whitespace-nowrap text-right ${className}`}
  >
    <span
      className={`inline-block  ${
        isGreen ? "text-green font-bold" : "text-gray-50"
      }`}
    >
      {amount}
    </span>
    <span className="inline-block font-light text-gray-50">({currency})</span>
  </div>
)

export default CurrencyDisplay
