import { useCartTimerContext } from "context/cart-timer-context"
import CartDotIcon from "@icons/cart_dot.svg"
import Image from "next/image"
import { TimedStatusPopover } from "components/reusable/timed-status-popover"

type Props = {
  isOpen: boolean
  onClose: () => void
}

export const AddedToCartPopover = ({ isOpen, onClose }: Props) => {
  const { timeLeft, formatTime } = useCartTimerContext()

  return (
    <TimedStatusPopover
      isOpen={isOpen}
      onClose={onClose}
      timer={timeLeft ? formatTime(timeLeft) : "15:00"}
      icon={
        <Image
          src={CartDotIcon}
          alt="Produkt dodany do koszyka"
          width={120}
          height={120}
        />
      }
      title="ATRAKCJA ZOSTAŁA DODANA DO KOSZYKA"
      description={
        <>
          Dlaczego czas rezerwacji atrakcji wygasa?
          <br />
          Aby inni klienci mogli rów<PERSON>ż skorzystać z naszych rabatów,
          opróżniamy wszystkie koszyki, które nie zostały zaktualizowane przez
          dłużej niż 15 minut.
        </>
      }
      ctaText="Przejdź do koszyka"
      ctaHref="/koszyk"
    />
  )
}
