import Image from "next/image"
import { ExtendedCart } from "types/cart"

import CalendarIcon from "@icons/calendar.svg"
import ClockIcon from "@icons/clock.svg"
import LocationIcon from "@icons/map_pin.svg"
import CoinsIcon from "@icons/coins.svg"
import UserIcon from "@icons/user.svg"
import HandCoinsIcon from "@icons/hand_coins.svg"
import MoneyBillIcon from "@icons/money_bill.svg"
import IconWithText from "app/(checkout)/koszyk/components/icon-with-text"
import PriceRow from "app/(checkout)/koszyk/components/price-row"
import CartItemFooter from "./cart-item-footer"
import { useCartCalculations } from "app/koszyk/hooks/useCartCalculations"
import { Drumstick } from "lucide-react"
import { AttractionAvailableStartPlaces } from "types/global"
import { ListTodo } from "lucide-react"
import { formatPrice } from "@lib/util/money"
import { sortByAgeGroup } from "@lib/utils"
import { removeDateInParentheses } from "@lib/util/remove-date-in-parentheses"

type Props = {
  cart: ExtendedCart
}

const CartItemPreview = ({ cart }: Props) => {
  const item = cart?.items?.[0]
  const {
    isSelectedPlace,
    isSelectedDate,
    isSelectedTime,
    isDynamicGroup,
    isBoatRental,
    calculatedAdvance,
    remainingAmount,
    totalAmount,
    getPriceInPLN,
    formattedDate,
    formattedDateRange,
    isRegularVariant,
    selectedFoodOptions,
  } = useCartCalculations(cart)

  const sortedAgeGroups = sortByAgeGroup(cart.items)

  return (
    <div className="grow border rounded-2xl h-full p-2 sm:p-5">
      {/* HEADER */}
      <div className="grid lg:grid-cols-[2.5fr,2fr] gap-6 ">
        <div className="relative w-full rounded-2xl overflow-clip min-h-96">
          <Image
            src={item?.thumbnail || ""}
            alt={item?.title || ""}
            fill
            className="object-cover"
          />
        </div>

        <div className="flex flex-col gap-4 sm:gap-8">
          <h2 className="max-sm:text-xl text-2xl lg:text-4xl font-semibold">
            {item?.product.tour.name}
          </h2>
          {/* <p className="text-sm text-primary">
            {item?.product.tour.description}
          </p> */}

          <div className="grid sm:grid-cols-3 gap-4">
            {isSelectedDate && (
              <IconWithText
                icon={CalendarIcon}
                alt="Kalendarz - wybrana data"
                text={formattedDate!}
                className="font-semibold"
              />
            )}
            {isBoatRental && (
              <IconWithText
                icon={CalendarIcon}
                alt="Kalendarz - wybrana data"
                text={formattedDateRange!}
                className="font-semibold"
              />
            )}
            {isSelectedTime && (
              <IconWithText
                icon={ClockIcon}
                alt="Czas - wybrany czas"
                text={cart.metadata?.start_time as string}
                className="font-semibold"
              />
            )}
            {isSelectedPlace && (
              <IconWithText
                icon={LocationIcon}
                alt="Miejsce - wybrane miejsce"
                text={
                  (cart.metadata?.start_place as AttractionAvailableStartPlaces)
                    .place
                }
                className="font-semibold"
              />
            )}
          </div>

          {isRegularVariant && (
            <div className="flex flex-wrap gap-4">
              <IconWithText
                lucideIcon={<ListTodo className="size-5 stroke-secondary" />}
                alt="Wybrany wariant"
                text={"Wybrany wariant:"}
              />
              <p className="text-sm text-primary font-semibold">{item.title}</p>
            </div>
          )}

          {isDynamicGroup && (
            <>
              <div className="flex flex-wrap gap-4">
                {sortedAgeGroups.map((item) => {
                  return (
                    <div key={item.id} className="flex items-center gap-4">
                      <IconWithText
                        icon={UserIcon}
                        alt="Użytkownik - liczba osób"
                        text={removeDateInParentheses(item.title)}
                      />
                      <p className="text-sm text-primary font-semibold">
                        {item.quantity}
                      </p>
                    </div>
                  )
                })}
              </div>

              <div className="flex flex-wrap gap-4">
                {selectedFoodOptions?.length > 0 && (
                  <IconWithText
                    alt="Posiłek"
                    text="Posiłki"
                    lucideIcon={
                      <Drumstick className="size-5 stroke-secondary stroke-[2px]" />
                    }
                  />
                )}

                {(selectedFoodOptions ?? []).map((option) => (
                  <div key={option?.id} className="flex items-center gap-4">
                    <p className="text-sm text-primary font-semibold">
                      {option?.name}
                    </p>
                    <p className="text-sm text-primary font-semibold">
                      {option?.quantity}
                    </p>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </div>

      {/* BILLING DETAILS */}
      <hr className="my-8" />

      <div className="grid lg:grid-cols-[2fr,1fr]">
        <div className="flex max-lg:flex-col lg:justify-between lg:items-center max-lg:gap-4">
          <h2 className="text-heading_main font-bold">Cena atrakcji</h2>
        </div>
      </div>

      <div className="grid lg:grid-cols-[2fr,1fr] gap-3 mt-16 mb-10">
        <div className="grid grid-cols-[1fr,auto,auto] lg:grid-cols-[8fr,1fr,1fr] gap-2 lg:gap-10">
          <PriceRow
            label="Pełna cena*"
            icon={CoinsIcon}
            pricePLN={getPriceInPLN(totalAmount)}
            priceEUR={totalAmount}
          />
          <PriceRow
            label="Dopłata w dniu atrakcji"
            icon={HandCoinsIcon}
            pricePLN={getPriceInPLN(remainingAmount)}
            priceEUR={remainingAmount}
          />
          <PriceRow
            icon={HandCoinsIcon}
            label="Zaliczka"
            pricePLN={getPriceInPLN(calculatedAdvance)}
            priceEUR={calculatedAdvance}
          />

          <hr className="my-8 col-span-full" />

          <div className="flex justify-between max-lg:items-end col-span-full">
            <div className="flex items-center gap-2 max-sm:mb-1">
              <Image
                src={MoneyBillIcon}
                alt="Money Bill"
                width={20}
                height={20}
              />
              <span className="text-gray-50 inline-block ">Dziś zapłacisz</span>
            </div>

            <div className="flex gap-4 max-sm:flex-col-reverse sm:gap-8 max-sm:self-end max-sm:mt-4">
              <div className="text-lg sm:text-2xl  space-x-2">
                <span className="text-green font-bold ">
                  {formatPrice(getPriceInPLN(calculatedAdvance))}
                </span>
                <span className="text-gray-50 font-light">PLN</span>
                <span className="text-gray-50 font-bold">
                  ({formatPrice(calculatedAdvance)} €)
                </span>
              </div>
              {/* <CurrencyDisplay
                amount={getPriceInPLN(calculatedPrice)}
                currency="PLN"
                className=" text-lg sm:text-2xl"
                isGreen
              />
              <CurrencyDisplay
                amount={calculatedAdvance}
                currency="€"
                className="text-lg sm:text-2xl"
                isGreen
              /> */}
            </div>
          </div>

          <ul className="text-sm text-gray-50 col-span-full inline-block max-sm:text-xs max-sm:my-5">
            <li className="list-disc ml-6">
              Jeżeli atrakcja zawiera dodatkowe wejścia do parku narodowego{" "}
              <br />
              sprawdź na stronię atrakcji kwotę dopłaty za bilety
            </li>
          </ul>
        </div>
      </div>

      <CartItemFooter />
    </div>
  )
}

export default CartItemPreview
