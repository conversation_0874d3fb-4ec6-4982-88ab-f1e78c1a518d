import React from "react"

const CartItemPreviewSkeleton = () => {
  return (
    <div className="grow border rounded-2xl h-full p-3 sm:p-5 animate-pulse">
      {/* HEADER */}
      <div className="grid grid-cols-1 sm:grid-cols-[2.5fr,2fr] gap-4 sm:gap-6">
        {/* Image placeholder */}
        <div className="relative w-full rounded-2xl overflow-clip aspect-video bg-gray-200" />

        <div className="flex flex-col gap-4 sm:gap-8">
          {/* Title placeholder */}
          <div className="h-8 sm:h-12 bg-gray-200 rounded-lg w-3/4" />

          {/* Description placeholder */}
          <div className="space-y-2">
            <div className="h-3 sm:h-4 bg-gray-200 rounded w-full" />
            <div className="h-3 sm:h-4 bg-gray-200 rounded w-5/6" />
          </div>

          {/* Icons row placeholder */}
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 sm:gap-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center gap-2">
                <div className="w-4 sm:w-5 h-4 sm:h-5 rounded-full bg-gray-200" />
                <div className="h-3 sm:h-4 bg-gray-200 rounded w-16 sm:w-20" />
              </div>
            ))}
          </div>

          {/* Items placeholder */}
          <div className="flex flex-wrap gap-2 sm:gap-4">
            {[1, 2].map((i) => (
              <div key={i} className="flex items-center gap-2 sm:gap-4">
                <div className="w-4 sm:w-5 h-4 sm:h-5 rounded-full bg-gray-200" />
                <div className="h-3 sm:h-4 bg-gray-200 rounded w-12 sm:w-16" />
                <div className="h-3 sm:h-4 bg-gray-200 rounded w-6 sm:w-8" />
              </div>
            ))}
          </div>
        </div>
      </div>

      <hr className="my-4 sm:my-8 border-gray-200" />

      {/* BILLING DETAILS */}
      <div className="grid grid-cols-1 sm:grid-cols-[2fr,1fr]">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-0">
          <div className="h-12 sm:h-16 bg-gray-200 rounded-lg w-36 sm:w-48" />
          <div className="h-6 sm:h-8 bg-gray-200 rounded-lg w-full sm:w-96" />
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-[2fr,1fr] gap-6 sm:gap-3 mt-8 sm:mt-16 mb-6 sm:mb-10">
        <div className="space-y-4 sm:space-y-0 sm:grid sm:grid-cols-[8fr,1fr,1fr] sm:gap-10">
          {/* Price rows */}
          {[1, 2, 3].map((i) => (
            <React.Fragment key={i}>
              <div className="flex items-center justify-between sm:justify-start gap-2">
                <div className="flex items-center gap-2">
                  <div className="w-4 sm:w-5 h-4 sm:h-5 rounded-full bg-gray-200" />
                  <div className="h-3 sm:h-4 bg-gray-200 rounded w-24 sm:w-32" />
                </div>
                <div className="flex gap-4 sm:hidden">
                  <div className="h-3 bg-gray-200 rounded w-16" />
                  <div className="h-3 bg-gray-200 rounded w-16" />
                </div>
              </div>
              <div className="hidden sm:block h-4 bg-gray-200 rounded" />
              <div className="hidden sm:block h-4 bg-gray-200 rounded" />
            </React.Fragment>
          ))}

          <hr className="my-4 sm:my-8 col-span-full border-gray-200" />

          {/* Total row */}
          <div className="flex justify-between col-span-full">
            <div className="flex items-center gap-2">
              <div className="w-4 sm:w-5 h-4 sm:h-5 rounded-full bg-gray-200" />
              <div className="h-3 sm:h-4 bg-gray-200 rounded w-20 sm:w-24" />
            </div>

            <div className="flex gap-4 sm:gap-8">
              <div className="h-4 sm:h-6 bg-gray-200 rounded w-20 sm:w-24" />
              <div className="h-4 sm:h-6 bg-gray-200 rounded w-20 sm:w-24" />
            </div>
          </div>

          {/* Footer text */}
          <div className="col-span-full mt-2">
            <div className="h-3 sm:h-4 bg-gray-200 rounded w-full sm:w-3/4" />
          </div>
        </div>

        {/* Exchange rate box */}
        <div className="flex flex-col gap-2 items-center border-t sm:border-t-0 pt-4 sm:pt-0">
          <div className="flex flex-col gap-2 items-center">
            <div className="h-3 sm:h-4 bg-gray-200 rounded w-28 sm:w-32" />
            <div className="h-6 sm:h-8 bg-gray-200 rounded w-20 sm:w-24" />
            <div className="h-2 sm:h-3 bg-gray-200 rounded w-16 sm:w-20" />
          </div>
        </div>
      </div>

      {/* Footer placeholder */}
      <div className="h-12 sm:h-16 bg-gray-200 rounded-lg w-full" />
    </div>
  )
}

export default CartItemPreviewSkeleton
