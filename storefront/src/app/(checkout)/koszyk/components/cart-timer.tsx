import { useCartTimerContext } from "context/cart-timer-context"
import Badge from "./badge"
import { TimedStatusPopover } from "components/reusable/timed-status-popover"
import CartDotIcon from "@icons/cart_dot.svg"
import Image from "next/image"

export const CartTimer = () => {
  const {
    timeLeft,
    hasExpired,
    isExpiredModalOpen,
    setIsExpiredModalOpen,
    formatTime,
  } = useCartTimerContext()

  if (!timeLeft || hasExpired) {
    return (
      <TimedStatusPopover
        isOpen={isExpiredModalOpen}
        onClose={() => setIsExpiredModalOpen(false)}
        icon={
          <Image src={CartDotIcon} alt="Czas minął" width={120} height={120} />
        }
        title="🕒 Czas na opłacenie zamówienia minął!"
        description="Twoje zamówienie zostało anulowane, poniewa<PERSON> nie zostało opłacone w ciągu 15 minut. <PERSON><PERSON><PERSON> nadal chcesz dokonać zakupu, dodaj produkt ponownie do koszyka i przejdź do płatności."
        ctaText="Rozumiem"
        ctaHref="#"
        ctaAs="button"
      />
    )
  }

  return (
    <>
      <Badge>
        <div className="flex items-center gap-2">
          <span className="">Czas do wygaśnięcia:</span>
          <span className="font-bold">{formatTime(timeLeft)}</span>
        </div>
      </Badge>
    </>
  )
}
