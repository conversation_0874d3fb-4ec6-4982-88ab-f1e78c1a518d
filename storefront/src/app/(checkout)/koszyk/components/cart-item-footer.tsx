"use client"

import Button from "components/reusable/button"
import Image from "next/image"
import TrashIcon from "@icons/trash_bin.svg"
import { useCartContext } from "context/cart-context"
import Link from "next/link"
const CartItemFooter = () => {
  const { clearCart } = useCartContext()

  return (
    <>
      <hr className="my-8" />
      <div className="justify-between flex lg:items-center flex-col gap-4 lg:flex-row-reverse">
        <Button
          variant="outline"
          onClick={clearCart}
          className="w-fit self-end"
        >
          <Image src={TrashIcon} className="size-5" alt="trash" />
        </Button>
        <div className="flex gap-2 w-full justify-between text-base-semi">
          <Link
            href="/checkout"
            className="min-w-48 bg-secondary text-primary text-center rounded-full py-2 px-4 flex items-center justify-center gap-2"
          >
            <PERSON><PERSON><PERSON><PERSON>
          </Link>
        </div>
      </div>
    </>
  )
}

export default CartItemFooter
