"use client"

import { useCartContext } from "context/cart-context"

type Props = {}

const CartItemsCounter = (props: Props) => {
  const { cart } = useCartContext()

  if (cart === "fetching") {
    return null
  }

  if (!cart) {
    return null
  }

  return (
    <div className="absolute -top-2 -right-2 w-4 h-4 bg-primary rounded-full flex items-center justify-center">
      <p className="text-white text-xs">1</p>
    </div>
  )
}

export default CartItemsCounter
