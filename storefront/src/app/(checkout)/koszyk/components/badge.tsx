import Image from "next/image"
import React, { PropsWithChildren } from "react"

interface IProps extends PropsWithChildren {
  className?: string
}

const Badge = ({ children, className }: IProps) => {
  return (
    <div
      className={`grid grid-cols-[auto,1fr] gap-2 sm:gap-5 border border-secondary rounded-2xl p-4 sm:p-5 w-fit ${className}`}
    >
      <Image
        src={"icons/info_square.svg"}
        alt="cart full"
        width={20}
        height={20}
      />
      {children}
    </div>
  )
}

export default Badge
