import { getBaseURL } from "@lib/util/env"
import Footer from "components/partials/footer"
import Navbar from "components/partials/nav"
import { Metadata } from "next"
import { Plus_Jakarta_Sans } from "next/font/google"
import { NuqsAdapter } from "nuqs/adapters/next/app"

import "../../index.css"
import { CartContextProvider } from "context/cart-context"
import { CartTimerProvider } from "context/cart-timer-context"
import QueryProvider from "components/reusable/query-provider"
import { cookies } from "next/headers"
import { GoogleAnalytics, GoogleTagManager } from "@next/third-parties/google"
import LogRocketProvider from "components/reusable/logrocket-provider"
import ForceBrowserDataVersioning from "components/reusable/force-browser-data-versioning"
import ZipyProvider from "components/reusable/zipyai-provider"

export const metadata: Metadata = {
  facebook: {
    appId: "1469779620241547",
  },
  alternates: {
    canonical: "./",
  },
  title: "Chorwacja - wakacyjnepomysly.pl - atrakcje i poradniki o Chorwacji!",
  description:
    "Z polską obsługą łatwiej zaplanujesz atrakcje w Chorwacji. Wybieramy najlepsze opcje, abyś mógł poznać Chorwację z lepszej strony! Chorwacja czeka!",
  openGraph: {
    locale: "pl_PL",
    type: "website",
    siteName: "wakacyjnepomysly.pl",
    url: getBaseURL(),
    title:
      "Chorwacja - wakacyjnepomysly.pl - atrakcje i poradniki o Chorwacji!",
    description:
      "Z polską obsługą łatwiej zaplanujesz atrakcje w Chorwacji. Wybieramy najlepsze opcje, abyś mógł poznać Chorwację z lepszej strony! Chorwacja czeka!",
  },
  twitter: {
    card: "summary_large_image",
    title: "Chorwacja - atrakcje i poradniki - wszystko w jednym miejscu!",
    description:
      "Z polską obsługą łatwiej zaplanujesz atrakcje w Chorwacji. Wybieramy najlepsze opcje, abyś mógł poznać Chorwację z lepszej strony! Chorwacja czeka!",
  },
  metadataBase: new URL(getBaseURL()),
  verification: {
    google: "google",
    yandex: "yandex",
    yahoo: "yahoo",
  },
}

const inter = Plus_Jakarta_Sans({
  style: "normal",
  display: "swap",
  subsets: ["latin"],
})

export default async function RootLayout(props: { children: React.ReactNode }) {
  const consentCookie = (await cookies()).get("CookieConsent")
  const isConsentAccepted = consentCookie?.value === "accepted"

  return (
    <html lang="en" data-mode="light">
      <body>
        <NuqsAdapter>
          <QueryProvider>
            <CartContextProvider>
              <CartTimerProvider>
                <Navbar />
                <main
                  className={`relative min-h-screen mt-[104px] lg:mt-[106px] ${inter.className}`}
                >
                  {props.children}
                </main>
                <Footer />
                <ForceBrowserDataVersioning cookieValue={consentCookie} />
              </CartTimerProvider>
            </CartContextProvider>
          </QueryProvider>
        </NuqsAdapter>
      </body>
      {isConsentAccepted &&
        process.env.NEXT_PUBLIC_GA_ID &&
        process.env.NEXT_PUBLIC_GTM_ID && (
          <>
            <GoogleAnalytics gaId={process.env.NEXT_PUBLIC_GA_ID} />
            <GoogleTagManager gtmId={process.env.NEXT_PUBLIC_GTM_ID} />
          </>
        )}
      {isConsentAccepted && process.env.NEXT_PUBLIC_LOGROCKET_KEY && (
        <LogRocketProvider />
      )}
      {isConsentAccepted && process.env.NEXT_PUBLIC_ZIPY_API_KEY && (
        <ZipyProvider />
      )}
    </html>
  )
}
