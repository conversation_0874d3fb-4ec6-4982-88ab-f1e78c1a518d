"use client"

import { Input } from "@medusajs/ui"
import { useQuery } from "@tanstack/react-query"
import { useQueryState } from "nuqs"
import { useDebounce } from "@uidotdev/usehooks"
import { listBlogPosts } from "@lib/data/guides"
import GuideCard from "./guide-card"
import { MagnifyingGlass } from "@medusajs/icons"
import { createPortal } from "react-dom"
import { useEffect, useState } from "react"
import { GuideNavigationSkeletonGrid } from "components/skeletons/guides-navigation-skeleton"
import { useInView } from "react-intersection-observer"

const GUIDES_PER_PAGE = 30

interface GuidesClientProps {
  searchSlotId: string
  initialGuides: Awaited<ReturnType<typeof listBlogPosts>>
}

export default function GuidesClient({
  searchSlotId,
  initialGuides,
}: GuidesClientProps) {
  const [q, setQ] = useQueryState("q")
  const debouncedQ = useDebounce(q, 300)
  const { ref, inView } = useInView()
  const [page, setPage] = useState(1)

  const { data, isLoading } = useQuery({
    queryKey: ["guides", { debouncedQ }],
    queryFn: () => listBlogPosts(debouncedQ ?? ""),
    initialData: debouncedQ ? undefined : initialGuides,
  })

  // Reset page when search query changes
  useEffect(() => {
    setPage(1)
  }, [debouncedQ])

  // Load more when scrolling to bottom
  useEffect(() => {
    if (inView && hasNextPage) {
      setPage((p) => p + 1)
    }
  }, [inView])

  const guides = data || []
  const paginatedGuides = guides.slice(0, page * GUIDES_PER_PAGE)
  const hasNextPage = guides.length > page * GUIDES_PER_PAGE

  // Portal setup
  const [searchMounted, setSearchMounted] = useState(false)

  useEffect(() => {
    setSearchMounted(true)
  }, [])

  return (
    <>
      {searchMounted &&
        createPortal(
          <div className="relative w-full">
            <Input
              className="w-full h-11 px-6 rounded-full border-2 border-secondary backdrop-blur-sm text-lg placeholder:text-gray-50 focus:outline-none"
              placeholder="Szukaj poradników"
              value={q ?? ""}
              onChange={(e) => setQ(e.target.value)}
            />
            <MagnifyingGlass className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-500" />
          </div>,
          document.getElementById(searchSlotId)!
        )}

      <div className="inner-container mb-16 mt-0">
        <div className="grid grid-cols-2 lg:grid-cols-3 gap-4 md:gap-8">
          {isLoading ? (
            <GuideNavigationSkeletonGrid count={12} />
          ) : (
            paginatedGuides.map((guide) => (
              <GuideCard guide={guide} key={guide.slug} />
            ))
          )}
        </div>
        <div
          ref={ref}
          className="grid grid-cols-2 lg:grid-cols-3 gap-4 md:gap-8 justify-center mt-8"
        >
          {hasNextPage && <GuideNavigationSkeletonGrid count={3} />}
        </div>
      </div>
      {/* Loading indicator */}
    </>
  )
}
