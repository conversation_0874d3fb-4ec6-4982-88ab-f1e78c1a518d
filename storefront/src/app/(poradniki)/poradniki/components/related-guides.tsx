import Carousel from "components/carousel/carousel"
import GuideCard from "./guide-card"
import { BaseGuidePost } from "types/global"
import clsx from "clsx"

type Props = {
  relatedGuides: BaseGuidePost[]
  className?: string
}

const RelatedGuides = ({ relatedGuides, className }: Props) => {
  return (
    <Carousel
      className={clsx(
        "w-full overflow-x-hidden xl:overflow-x-visible",
        className
      )}
      key={relatedGuides.length}
      asFooterCarousel={true}
      options={{
        dragFree: true,
      }}
    >
      {relatedGuides.map((guide) => (
        <div key={`${guide.id}-${guide.slug}`}>
          <GuideCard guide={guide} aspect="video" />
        </div>
      ))}
    </Carousel>
  )
}

export default RelatedGuides
