import Image from "next/image"
import Link from "next/link"
import { BaseGuidePost } from "types/global"
import NextArrow from "@images/link-arrow.png"
import clsx from "clsx"
import WithLoadingImage from "components/reusable/with-loading-image"

type Props = {
  guide: BaseGuidePost
  className?: string
  aspect?: "square" | "video"
}

const GuideCard = ({ guide, className, aspect = "square" }: Props) => {
  return (
    <Link
      href={`/${guide.slug}`}
      className={clsx("group flex flex-col", className)}
    >
      <div
        className={clsx(
          "relative w-full mb-2 overflow-hidden rounded-xl",
          aspect === "video" ? "aspect-[16/11]" : "aspect-square"
        )}
      >
        <WithLoadingImage
          imageSrc={guide.preview_image}
          className="object-cover rounded-xl group-hover:scale-105 transition-transform duration-300"
          fill
          alt={guide.name}
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
      </div>

      <h4 className="text-heading_quinary md:text-heading_quaternary font-medium mb-2 grow">
        {guide.name}
      </h4>
      <p className="text-gray-50 text-sm mb-4 line-clamp-2">
        {guide.meta_description}
      </p>

      <div className="flex items-center">
        <Image
          src={NextArrow}
          alt="next arrow"
          width={75}
          height={20}
          className="h-auto ml-1 transition-transform group-hover:translate-x-1"
        />
      </div>
    </Link>
  )
}

export default GuideCard
