import QueryProvider from "components/reusable/query-provider"
import React, { PropsWithChildren, Suspense } from "react"
import { Metadata } from "next"
import { getBaseURL } from "@lib/util/env"

export const metadata: Metadata = {
  title: "Poradniki - zaplanuj wakacje w Chorwacji",
  description:
    "Pełne poradniki wakacyjne dla planujących wakacje w Chorwacji. Z nami dowiesz się, jakie miejsca warto odwiedzić i jak dobrze zaplanować swój czas na wakacjach.",
  openGraph: {
    locale: "pl_PL",
    type: "article",
    title: "Poradniki - zaplanuj wakacje w Chorwacji",
    description:
      "Pełne poradniki wakacyjne dla planujących wakacje w Chorwacji. Z nami dowiesz się, jakie miejsca warto odwiedzić i jak dobrze zaplan<PERSON>ć swój czas na wakacjach.",
    siteName: "wakacyjnepomysly.pl",
    images: [
      {
        url: "/images/zbior_poradnikow.jpg",
        width: 1528,
        height: 1281,
        alt: "Poradniki - zaplanuj wakacje w Chorwacji",
      },
    ],
    url: `${getBaseURL()}/poradniki`,
  },
  twitter: {
    card: "summary_large_image",
    title: "Poradniki",
  },
  metadataBase: new URL(getBaseURL()),
}

const GuidesLayout = ({ children }: PropsWithChildren) => {
  return (
    <QueryProvider>
      <Suspense>
        <main className="min-h-screen flex flex-col">{children}</main>
      </Suspense>
    </QueryProvider>
  )
}

export default GuidesLayout
