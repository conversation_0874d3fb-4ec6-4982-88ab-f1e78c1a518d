import SubpageHero from "components/reusable/subpage-hero"
import OurSocialMedia from "components/reusable/our-social-media"
import { listBlogPosts } from "@lib/data/guides"
import GuidesClient from "./components/guides-client"

const SEARCH_SLOT_ID = "guides-search-slot"

async function GuidesPage() {
  // Fetch all guides on the server
  const initialGuides = await listBlogPosts("")

  return (
    <div className="flex flex-col min-h-screen">
      <SubpageHero
        imageSrc="/images/guides_background.webp"
        title="Zbiór poradników"
        imageClassName="lg:[object-position:0_65%]"
      >
        <div id={SEARCH_SLOT_ID} />
      </SubpageHero>

      <GuidesClient
        searchSlotId={SEARCH_SLOT_ID}
        initialGuides={initialGuides}
      />

      <OurSocialMedia />
    </div>
  )
}

export default GuidesPage
