import { getBlogPost, getRelatedBlogPosts } from "@lib/data/guides"
import Image from "next/image"

import { Metadata } from "next"
import RelatedGuides from "app/(poradniki)/poradniki/components/related-guides"
import PreviousPageBtn from "app/(poradniki)/poradniki/components/previous-page-btn"
import { notFound } from "next/navigation"
import SubpageHero from "components/reusable/subpage-hero"
import { getBaseURL } from "@lib/util/env"
type Props = {
  params: {
    slug: string
  }
}

export const generateMetadata = async ({ params }: Props) => {
  const guideDetails = await getBlogPost(params.slug)

  if (!guideDetails) {
    return {
      notFound: true,
    }
  }

  return {
    title: guideDetails.seo_title || guideDetails.name,
    description: guideDetails.meta_description,
    keywords: guideDetails.tags.join(","),
    openGraph: {
      title: guideDetails.og_title || guideDetails.name,
      description: guideDetails.meta_description,
      images: [guideDetails.preview_image],
      url: `${getBaseURL()}/${guideDetails.slug}`,
    },
    twitter: {
      title: guideDetails.og_title || guideDetails.name,
      description: guideDetails.meta_description,
      images: [guideDetails.preview_image],
      url: `${getBaseURL()}/${guideDetails.slug}`,
    },
  } as Metadata
}

const GuideDetailsPage = async ({ params }: Props) => {
  const guideDetails = await getBlogPost(params.slug)

  if (!guideDetails) {
    return notFound()
  }

  const relatedGuides = await getRelatedBlogPosts(guideDetails.id)

  return (
    <div className="w-full pb-16">
      <SubpageHero imageSrc={guideDetails.featured_image} />

      <div className="blog">
        <div
          className="ProseMirror"
          dangerouslySetInnerHTML={{ __html: guideDetails.content }}
        />
      </div>

      {relatedGuides.length > 0 && (
        <div className="md:mt-20 inner-container-max-w">
          <h1 className="text-heading_secondary md:text-heading_main font-bold mb-4">
            Więcej podobnych
          </h1>
          <RelatedGuides relatedGuides={relatedGuides} className="!my-8" />
        </div>
      )}

      <div className="w-fit mx-auto">
        <PreviousPageBtn />
      </div>
    </div>
  )
}

export default GuideDetailsPage
