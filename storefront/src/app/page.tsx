import NavigationCard from "components/home/<USER>"
import { Icons } from "components/reusable/Icons"
import OurSocialMedia from "components/reusable/our-social-media"
import { Star } from "lucide-react"
import Image from "next/image"
import React from "react"

type Props = {}

const HomePage = (props: Props) => {
  return (
    <div className="z-0 relative">
      <div className="sm:bg-[url('/images/hero_bg.webp')] bg-fixed bg-cover min-h-[calc(100vh_-_106px)] flex items-center justify-center py-8 bg-center rounded-b-[50px] lg:justify-start lg:py-0">
        <div className="grid lg:flex lg:justify-between 2xl:items-center gap-[clamp(2rem,5vw,2rem+1vw)] container">
          <div className="w-full h-[calc(100vh_+_106px)] bg-[url('/images/hero_bg.webp')] absolute top-0 left-0 z-0 bg-[57.5%_0%] rounded-b-[50px] sm:hidden" />
          <div className="flex 2xl:justify-between 2xl:max-h-[625px] flex-col h-[calc(100vh_-_7rem_-_2rem_-_2rem)] justify-between relative z-10 lg:h-[calc(100vh_-_7rem_-_2rem_-_4rem)]">
            <div className="flex flex-col justify-center text-white gap-12">
              <h1 className="text-5xl-bold font-bold text-balance max-w-screen-md leading-none">
                Polska obsługa, chorwackie marzenia!
              </h1>
              <p className="text-[22px] font-normal lg:text-xl-regular max-w-[339px]">
                Odkrywaj z nami najciekawsze atrakcje w Chorwacji! Działamy od
                2020 roku.
              </p>
            </div>
            <div className="flex gap-4 flex-col xl:flex-row">
              <div className="text-white flex flex-col gap-2">
                <span className="text-4xl-bold rounded-lg border border-white px-[35px] py-[10px] w-fit">
                  40.000
                </span>
                <p className="text-large-regular ">Tylu Klientów nam zaufało</p>
              </div>

              <div className="rounded-lg flex flex-col gap-1 justify-center lg border border-secondary w-fit px-[35px] py-2 h-[70px]">
                <div className="flex gap-1">
                  <p className="text-sm text-white shrink-0 font-light">
                    100% poleceń na
                  </p>
                  <Image
                    src="icons/facebook_label.svg"
                    alt="facebook"
                    width={75}
                    height={15}
                  />
                </div>
                <div className="flex gap-1 whitespace-nowrap text-secondary items-center justify-between">
                  <div className="flex gap-1">
                    <Star className="fill-secondary size-4" />
                    <Star className="fill-secondary size-4" />
                    <Star className="fill-secondary size-4" />
                    <Star className="fill-secondary size-4" />
                    <Star className="fill-secondary size-4" />
                  </div>
                  <span className="text-secondary/80 font-light inline-block ml-1">
                    (520 opinii)
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="flex flex-wrap md:flex-nowrap gap-4 lg:w-full lg:justify-end lg:items-end lg:max-h-[600px]">
            <NavigationCard
              title="Atrakcje"
              description="Oferta ponad 75 atrakcji w Chorwacji"
              image="/images/sakarun.webp"
              link="/chorwacja-atrakcje"
              className="2xl:aspect-[8/12] "
            />
            <NavigationCard
              title="Poradniki"
              image="/images/poradniki_miniatura.webp"
              link="/poradniki"
              className="lg:hidden"
            />
            <div className="flex-wrap w-full lg:flex-col justify-between sm:gap-4 lg:gap-6 lg:flex-nowrap lg:max-w-[400px] lg:max-h-[400px] hidden lg:flex">
              <div className="relative lg:aspect-square">
                <NavigationCard
                  title="Poradniki"
                  image="/images/poradniki_miniatura.webp"
                  link="/poradniki"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <OurSocialMedia />
      </div>
    </div>
  )
}

export default HomePage
