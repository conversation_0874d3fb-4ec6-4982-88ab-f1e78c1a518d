import { ArrowUpRightMini } from "@medusajs/icons"
import { Text } from "@medusajs/ui"
import { Metadata } from "next"
import Image from "next/image"
import Link from "next/link"

export const metadata: Metadata = {
  title: "404",
  description: "Strona nie znaleziona",
}

export default function NotFound() {
  return (
    <div className="flex flex-col gap-4 items-center justify-center min-h-[calc(100vh)] relative">
      <Image
        src="/images/hero_bg.webp"
        alt="404"
        fill
        className="object-cover"
      />
      <div className="absolute p-1 rounded-md aspect-[4/3] lg:p-10 bg-white/50 text-white backdrop-blur-sm flex flex-col items-center gap-4 place-content-center z-10">
        <h1 className="text-2xl-semi text-ui-fg-base">
          Nie udało się znaleźć strony
        </h1>
        <p className="text-small-regular text-ui-fg-base">
          Strona, któr<PERSON> próbujesz otworzyć, nie istnieje.
        </p>
        <Link
          className="flex items-center group bg-secondary rounded-xl px-4 py-2 text-primary "
          href="/"
        >
          <Text className="text-ui-fg-interactive">Wróć na stronę główną</Text>
        </Link>
      </div>
    </div>
  )
}
