import { getAttractionBySlug } from "@lib/data/attractions"
import AddToCartSection from "../components/add-to-cart-section"
import RichTextRenderer from "components/reusable/rich-text-renderer"
import AttractionContentBlocks from "../components/attraction-content-blocks"
import { redirect } from "next/navigation"
import { AttractionDetails } from "types/global"
import ProductDetailsGallery from "../components/product-details-gallery"
import { Metadata } from "next"
import RecommendedAttractionsDataProvider from "../components/recommended-attractions-data-provider"
import clsx from "clsx"
import { getBaseURL } from "@lib/util/env"
import { ScrollToSectionButton } from "components/reusable/scroll-to-section-button"
type Props = {
  params: Promise<{ slug: string }>
}

export const generateMetadata = async ({
  params,
}: Props): Promise<Metadata> => {
  const { slug } = await params
  const attraction = await getAttractionBySlug(slug)

  const { title, description, keywords } = attraction.tour_seo || {}

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: "website",
      locale: "pl_PL",
      images: [attraction.featured_image],
      url: `${getBaseURL()}/produkt/${attraction.slug}`,
    },
    twitter: {
      title,
      description,
      images: [attraction.featured_image],
    },
  }
}

const AttractionDetailsPage = async (props: Props) => {
  let attraction: AttractionDetails | null = null

  try {
    const { slug } = await props.params
    attraction = await getAttractionBySlug(slug)
  } catch (error) {
    console.error(error)
    redirect("/chorwacja-atrakcje")
  }

  const recommendedAttractionIds = attraction?.recommended_tour_ids || []

  const areRecommendedAttractions = recommendedAttractionIds.length > 0

  return (
    <>
      <div className="w-full animate-fade-in-top pb-16">
        {/* CR: Client wanted to get rid of that section but lets keep it commented out for now */}
        {/* <SubpageHero
          imageSrc={attraction.featured_image}
          className="w-full sm:w-fit"
        >
          <AttractionDetailsBar attraction={attraction} />
        </SubpageHero> */}

        <section className="container sticky top-32 flex justify-end z-[100]">
          <ScrollToSectionButton
            targetId="attraction-form"
            className="border rounded-full px-4 py-2"
            navbarHeight={75}
          >
            Sprawdź dostępność
          </ScrollToSectionButton>
        </section>

        <div className="grid lg:grid-cols-2 gap-24 container 2xl:px-0 max-w-[1440px] mx-auto mt-10">
          <section>
            <h1 className="text-heading_main font-bold mb-8 leading-[125%]">
              {attraction.name}
            </h1>
            <RichTextRenderer
              content={attraction.description}
              className="text-gray-50 leading-relaxed max-w-md"
            />
          </section>

          <ProductDetailsGallery attraction={attraction} />
        </div>

        <AttractionContentBlocks contentBlocks={attraction.content_blocks} />

        <section
          className={clsx("mt-12", !areRecommendedAttractions && "mb-20")}
        >
          <AddToCartSection attraction={attraction} />
        </section>

        {areRecommendedAttractions && (
          <section className="mt-20 lg:mt-40 container">
            <h1 className="text-heading_secondary md:text-heading_main font-bold mb-4 md:mb-6">
              Więcej atrakcji
            </h1>
            <RecommendedAttractionsDataProvider
              attractionIds={recommendedAttractionIds}
              recommendedToursCarouselProps={{
                aspect: "video",
              }}
            />
          </section>
        )}
      </div>
    </>
  )
}

export default AttractionDetailsPage
