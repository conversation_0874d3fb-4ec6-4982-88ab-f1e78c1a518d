"use client"

import clsx from "clsx"
import Carousel from "components/carousel/carousel"
import Link from "next/link"
import Image from "next/image"
import NextArrow from "@images/link-arrow.png"
import { StoreProductCategory } from "@medusajs/types"
import WithLoadingImage from "components/reusable/with-loading-image"

type Props = {
  className?: string
  categories: StoreProductCategory[]
  aspect?: "video" | "square"
}

const CategoriesCarousel = ({
  categories,
  className,
  aspect = "square",
}: Props) => {
  return (
    <Carousel
      className={clsx("w-full", className)}
      key={categories.length}
      options={{
        dragFree: true,
      }}
    >
      {categories.map((category) => (
        <div
          className="embla__slide no_selection"
          key={`${category.id}-${category.name}`}
        >
          <Link
            href={`/kategoria-produktu/${category.metadata?.slug}`}
            className="group"
          >
            <div
              className={clsx(
                "relative w-full mb-2 overflow-hidden rounded-xl",
                aspect === "video" ? "aspect-video" : "aspect-square"
              )}
            >
              <WithLoadingImage
                imageSrc={category.metadata?.image as string}
                className="object-cover rounded-xl group-hover:scale-105 transition-transform duration-300"
                fill
                alt={category.name}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
            </div>

            <h3 className="text-heading_quaternary font-medium mb-2 mt-4 grow">
              {category.name}
            </h3>

            <div className="flex items-center">
              <Image
                src={NextArrow}
                alt="next arrow"
                className="w-[15%] h-auto ml-1 transition-transform group-hover:translate-x-1"
              />
            </div>
          </Link>
        </div>
      ))}
    </Carousel>
  )
}

export default CategoriesCarousel
