"use client"

import { useEffect, useState } from "react"
import { AttractionDetails } from "types/global"
import Carousel from "../../../../components/carousel/carousel"
import { Dialog, DialogPanel } from "@headlessui/react"
import { X } from "lucide-react"
import WithLoadingImage from "components/reusable/with-loading-image"

type Props = {
  attraction: AttractionDetails
}

const ProductDetailsGallery = ({ attraction }: Props) => {
  const gallery = attraction.gallery
  const galleryLongerThan3 = gallery.length > 3
  const remainingCount = gallery.length - 3
  const [isOpen, setIsOpen] = useState(false)
  const [startIndex, setStartIndex] = useState(0)

  const openGallery = (index: number) => {
    setStartIndex(index)
    setIsOpen(true)
  }

  const handleNextSlide = () => {
    if (startIndex < gallery.length - 1) {
      setStartIndex(startIndex + 1)
    }
  }

  const handlePreviousSlide = () => {
    if (startIndex > 0) {
      setStartIndex(startIndex - 1)
    }
  }

  // keyboard navigation
  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === "ArrowRight") {
      handleNextSlide()
    }
    if (event.key === "ArrowLeft") {
      handlePreviousSlide()
    }
    if (event.key === "Escape") {
      setIsOpen(false)
    }
  }

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown)
    return () => {
      window.removeEventListener("keydown", handleKeyDown)
    }
  }, [startIndex])

  return (
    <>
      <div className="grid grid-cols-2 gap-4 h-fit">
        <div
          className="col-span-full relative size-full aspect-[4/3] sm:aspect-[8/3] cursor-pointer"
          onClick={() => openGallery(0)}
        >
          <WithLoadingImage
            imageSrc={attraction.gallery[0] || "/icons/placeholder.svg"}
            alt={attraction.name}
            fill
            className="object-cover rounded-lg"
          />
        </div>

        {attraction.gallery.slice(1, 3).map((image, index) => (
          <div
            className="col-span-1 relative size-full aspect-[4/3] cursor-pointer"
            key={image}
            onClick={() => openGallery(index + 1)}
          >
            <WithLoadingImage
              imageSrc={image}
              alt={attraction.name}
              fill
              className="object-cover rounded-lg"
            />
            {galleryLongerThan3 && index === 1 && (
              <div className="absolute inset-0 bg-black/50 rounded-lg flex flex-col gap-4 items-center justify-center">
                <span className="text-white sm:text-lg font-semibold">
                  Wyświetl galerię
                </span>
                <span className="text-white text-3xl mr-3 sm:text-4xl font-semibold">
                  +{remainingCount}
                </span>
              </div>
            )}
          </div>
        ))}
      </div>

      <Dialog
        open={isOpen}
        onClose={() => setIsOpen(false)}
        className="relative z-[100]"
      >
        <div className="fixed inset-0 bg-black/90" aria-hidden="true" />

        <div className="fixed inset-0 flex items-center justify-center p-4">
          <DialogPanel className="w-full max-w-7xl grid h-full">
            <div className="relative h-fit my-auto">
              <button
                onClick={() => setIsOpen(false)}
                className="fixed right-8 top-8 z-10 p-2 bg-white/10 hover:bg-white/20 rounded-full transition-colors"
              >
                <X className="w-6 h-6 text-white" />
              </button>

              <Carousel
                options={{ startIndex, loop: false }}
                asFooterCarousel={false}
                overflowHidden={true}
              >
                {gallery.map((image, index) => (
                  <div
                    key={index}
                    className="relative flex items-center justify-center w-full h-[calc(100vh-10rem)] max-h-[calc(100vh-10rem)]"
                  >
                    <img
                      src={image}
                      alt={`${attraction.name} - zdjęcie ${index + 1}`}
                      className="object-contain max-w-full max-h-full !rounded-md mx-auto"
                    />
                  </div>
                ))}
              </Carousel>
            </div>
          </DialogPanel>
        </div>
      </Dialog>
    </>
  )
}

export default ProductDetailsGallery
