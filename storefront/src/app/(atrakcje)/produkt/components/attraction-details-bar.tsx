import { AttractionDetails } from "types/global"
import AttractionAttribute from "./attraction-conditional-attributes"

type Props = {
  attraction: AttractionDetails
}

const AttractionDetailsBar = ({ attraction }: Props) => {
  return (
    <div className="border-2 border-secondary p-2 rounded-full bg-white ">
      <div className="flex gap-1 sm:gap-4 lg:gap-8 mx-4 md:mx-12 xl:mx-24 justify-between  flex-wrap max-md:gap-4 max-md:p-4">
        <AttractionAttribute.City mode="detailed" attraction={attraction} />
        <AttractionAttribute.HourlyLength attraction={attraction} />
        <AttractionAttribute.DatesFromTo attraction={attraction} />
        <AttractionAttribute.PriceFrom attraction={attraction} />
      </div>
    </div>
  )
}

export default AttractionDetailsBar
