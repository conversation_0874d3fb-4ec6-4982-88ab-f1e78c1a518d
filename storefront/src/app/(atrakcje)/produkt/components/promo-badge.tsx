import clsx from "clsx"
import React from "react"

type Props = {
  children: React.ReactNode
  className?: string
  backgroundColor?: string
  textColor?: string
}

const PromoBadge = ({
  children,
  className,
  backgroundColor,
  textColor,
}: Props) => {
  return (
    <div
      className={clsx("absolute -top-[18px] left-4 flex gap-2", className)}
    >
      <span
        className={clsx(
          "px-8 py-1.5 md:py-2 rounded-full text-xs font-medium",
          textColor,
          backgroundColor
        )}
      >
        {children}
      </span>
    </div>
  )
}

export default PromoBadge
