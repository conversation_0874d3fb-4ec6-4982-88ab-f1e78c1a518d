"use client"

import React, { useState } from "react"
import { ContentBlocks } from "types/global"
import AccordionPlusIcon from "@icons/accordion_plus.svg"
import AccordionMinusIcon from "@icons/accordion_minus.svg"
import Image from "next/image"
import RichTextRenderer from "components/reusable/rich-text-renderer"

import CalendarIcon from "@icons/calendar_day.svg"
import ReservationIcon from "@icons/calendar.svg"
import ProgramIcon from "@icons/program.svg"
import TicketIcon from "@icons/ticket.svg"
import InfoIcon from "@icons/info.svg"
import EuroIcon from "@icons/euro.svg"

type Props = {
  contentBlocks: ContentBlocks
}

type FaqItemContent = {
  answer: string
  question: string
  position: number
}

const AccordionItem = ({
  label,
  content,
  icon,
  isFaq,
  isLast,
  defaultOpen = false,
}: {
  label: string
  content: any
  icon: string
  isFaq: boolean
  isLast: boolean
  defaultOpen?: boolean
}) => {
  const [isOpen, setIsOpen] = useState(defaultOpen)
  const [height, setHeight] = useState<number>(0)
  const contentRef = React.useRef<HTMLDivElement>(null)

  React.useEffect(() => {
    if (contentRef.current) {
      setHeight(isOpen ? contentRef.current.scrollHeight : 0)
    }
  }, [isOpen])

  return (
    <div
      className={`overflow-hidden border-t border-secondary ${
        isLast ? "border-b" : ""
      }`}
    >
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex w-full items-center justify-between bg-white py-6"
      >
        <div className="flex items-center gap-4">
          {/* <span className="text-2xl">{icon}</span> */}
          <Image src={icon} alt={label} className="size-7" />
          <span className="text-heading_quaternary font-semibold">{label}</span>
        </div>
        <Image
          src={isOpen ? AccordionMinusIcon : AccordionPlusIcon}
          alt={isOpen ? "Zwiń" : "Rozwiń"}
          className="h-6 w-6"
        />
      </button>

      <div
        style={{ height: height }}
        className="transition-[height] duration-200 ease-out"
      >
        <div ref={contentRef} className="bg-white  pb-8">
          {isFaq ? (
            <div className="flex flex-col max-w-[55ch] mx-auto gap-8">
              {content
                .sort(
                  (a: FaqItemContent, b: FaqItemContent) =>
                    a.position - b.position
                )
                .map(
                  (
                    item: FaqItemContent,
                    index: number,
                    array: FaqItemContent[]
                  ) => {
                    return (
                      <div
                        key={item.position + "-" + item.question}
                        className="text-center"
                      >
                        <h2 className="text-heading_quaternary font-bold text-secondary mb-4">
                          {item.question}
                        </h2>
                        <RichTextRenderer content={item.answer} className="wrap-long-links" />
                        {index !== array.length - 1 && (
                          <div className="w-full h-[1px] bg-gray-20 my-4" />
                        )}
                      </div>
                    )
                  }
                )}
            </div>
          ) : (
            <RichTextRenderer content={content} className="max-w-[80ch]" />
          )}
        </div>
      </div>
    </div>
  )
}

const AttractionContentBlocks = ({ contentBlocks }: Props) => {
  const blockMap = new Map<
    keyof ContentBlocks,
    {
      label: string
      content: any
      icon: string
    }
  >()

  blockMap.set("description", {
    label: "Opis",
    content: contentBlocks.description,
    icon: InfoIcon,
  })
  if (
    contentBlocks.specification_only_for_boats !== "" &&
    contentBlocks.specification_only_for_boats !== "<p></p>"
  ) {
    blockMap.set("specification_only_for_boats", {
      label: "Specyfikacja",
      content: contentBlocks.specification_only_for_boats,
      icon: InfoIcon,
    })
  }
  blockMap.set("calendar", {
    label: "Kalendarz",
    content: contentBlocks.calendar,
    icon: CalendarIcon,
  })
  blockMap.set("price", {
    label: "Cennik",
    content: contentBlocks.price,
    icon: EuroIcon,
  })
  blockMap.set("how_to_book", {
    label: "Rezerwacja",
    content: contentBlocks.how_to_book,
    icon: ReservationIcon,
  })
  blockMap.set("program", {
    label: "Program",
    content: contentBlocks.program,
    icon: ProgramIcon,
  })
  blockMap.set("ticket", {
    label: "Bilet",
    content: contentBlocks.ticket,
    icon: TicketIcon,
  })
  blockMap.set("faq", {
    label: "Co musisz wiedzieć",
    content: contentBlocks.faq,
    icon: InfoIcon,
  })

  const onlyFilledBlocks = Array.from(blockMap.values()).filter((entry) => {
    if (Array.isArray(entry.content)) {
      return entry.content.length > 0
    }
    return entry.content && entry.content !== "<p></p>"
  })

  return (
    <div className="max-w-[1440px] container mx-auto 2xl:px-0 mt-16 flex flex-col">
      {onlyFilledBlocks.map((block, index, array) => (
        <AccordionItem
          key={block.label}
          label={block.label}
          content={block.content}
          icon={block.icon}
          isFaq={block.label === "Co musisz wiedzieć"}
          isLast={index === array.length - 1}
        />
      ))}
    </div>
  )
}

export default AttractionContentBlocks
