"use client"

import React, { useRef, useState } from "react"
import Image from "next/image"
import { Toaster } from "@medusajs/ui"
import { AttractionDetails, ProductPricingVariant } from "types/global"
import { sdk } from "@lib/config"
import AnimatedCartButton from "components/reusable/animated-cart-button"
import useStoreSettings from "hooks/useStoreSettings"
import { DynamicAgeGroups } from "./cart-forms/dynamic-age-groups"
import {
  DynamicGroupGetCartPayloadResult,
  BoatRentalGetCartPayloadResult,
  FoodOption,
  RegularSimplifiedProductGetCartPayloadResult,
} from "types/cart"
import BoatRental from "./cart-forms/boat-rental"
import RegularSimplifiedProduct from "./cart-forms/regular-simplified-product"
import { useCartContext } from "context/cart-context"
import { AddedToCartPopover } from "app/(checkout)/koszyk/components/added-to-cart-popover"
import WithLoadingImage from "components/reusable/with-loading-image"

interface Props {
  attraction: AttractionDetails
}

type CartPayload =
  | DynamicGroupGetCartPayloadResult
  | BoatRentalGetCartPayloadResult
  | RegularSimplifiedProductGetCartPayloadResult

const AddToCartSection: React.FC<Props> = ({ attraction }) => {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const [showAddedToCart, setShowAddedToCart] = useState(false)

  const { setCartId } = useCartContext()
  const { storeSettings, isLoading: isStoreSettingsLoading } = useStoreSettings()
  
  const isShopEnabled = storeSettings?.is_shop_enabled ?? true

  const dynamicGroupPayloadRef = useRef<{
    getPayload: () => DynamicGroupGetCartPayloadResult
  }>(null)

  const boatRentalPayloadRef = useRef<{
    getPayload: () => BoatRentalGetCartPayloadResult
  }>(null)

  const regularSimplifiedProductPayloadRef = useRef<{
    getPayload: () => RegularSimplifiedProductGetCartPayloadResult
  }>(null)

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsSubmitting(true)

    let payload: CartPayload | undefined

    try {
      switch (attraction.product_pricing_variant) {
        case ProductPricingVariant.DYNAMIC_AGE_GROUPS:
          payload = dynamicGroupPayloadRef.current?.getPayload()
          break
        case ProductPricingVariant.BOAT_RENTAL:
          payload = boatRentalPayloadRef.current?.getPayload()
          break
        case ProductPricingVariant.REGULAR_VARIANTS:
          payload = regularSimplifiedProductPayloadRef.current?.getPayload()
          break
        default:
          throw new Error(
            `Unsupported pricing variant: ${attraction.product_pricing_variant}`
          )
      }

      if (!payload) {
        throw new Error("Failed to get cart payload")
      }

      const cartItems = isBoatRentalPayload(payload)
        ? [
            ...payload.variants.map((variant) => ({
              variant_id: variant.id,
              quantity: 1,
            })),
          ]
        : isRegularSimplifiedProductPayload(payload)
        ? [
            {
              variant_id: payload.variant.id,
              quantity: 1,
            },
          ]
        : payload.selectedGroups.map((group) => ({
            variant_id: group.activeVariant.id,
            quantity: group.count,
          }))

      const response = await sdk.store.cart.create({
        items: cartItems,
        metadata: {
          product_type: attraction.product_pricing_variant,
          attraction_id: attraction.id,
          attraction_name: attraction.name,
          ...(isDynamicGroupPayload(payload) && {
            selected_date: payload.selectedDate,
            selected_food_options: payload.foodOptions,
            start_place: payload.startPlace,
            start_time: payload.startTime,
          }),
          ...(isBoatRentalPayload(payload) && {
            date_from: payload.selectedDate.from,
            date_to: payload.selectedDate.to,
            amount_of_days: payload.variants.length,
            start_time: payload.startTime,
          }),
          ...(isRegularSimplifiedProductPayload(payload) && {
            selected_date: payload.selectedDate,
            start_time: payload.startTime,
            variant_id: payload.variant.id,
          }),
        },
        currency_code: "EUR",
      })

      if (!response || !response.cart || !response.cart.id) {
        console.error("Invalid cart response from SDK:", response)
        throw new Error("Invalid cart response from SDK")
      }

      try {
        if (typeof window !== "undefined" && window.localStorage) {
          localStorage.setItem("cart_id", response.cart.id)
        } else {
          console.warn(
            "localStorage is not available. Cart ID could not be saved locally."
          )
        }
      } catch (storageError) {
        console.error("Failed to save cart_id to localStorage:", storageError, {
          cartId: response.cart.id,
        })
      }

      setCartId(response.cart.id)
      setIsSuccess(true)
      setShowAddedToCart(true)

      setTimeout(() => {
        setIsSuccess(false)
      }, 2000)
    } catch (error) {
      console.error("Failed to create or process cart:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const renderCartForm = () => {
    const uniqueFoodOptions = Array.from(
      new Set(attraction.food_options.map((option) => option.name))
    ).map((name) =>
      attraction.food_options.find((option) => option.name === name)
    ) as FoodOption[]

    switch (attraction.product_pricing_variant) {
      case ProductPricingVariant.DYNAMIC_AGE_GROUPS:
        return (
          <DynamicAgeGroups
            isAtLeastOneFoodOptionRequired={
              attraction.at_least_one_food_option_required
            }
            blockedDates={attraction.blocked_dates}
            blockedDatesByMonth={attraction.blocked_dates_by_month}
            foodOptions={uniqueFoodOptions}
            variants={attraction.product.variants}
            getPayloadRef={dynamicGroupPayloadRef}
            startPlacesExcludedByDate={attraction.start_places_by_date}
            startPlaces={attraction.available_start_places}
            startTimes={attraction.start_times}
            blockedStartTimes={attraction.blocked_start_times}
          />
        )
      case ProductPricingVariant.BOAT_RENTAL:
        return (
          <BoatRental
            blockedDates={attraction.blocked_dates}
            blockedDatesByMonth={attraction.blocked_dates_by_month}
            variants={attraction.product.variants}
            getPayloadRef={boatRentalPayloadRef}
            startTimes={attraction.start_times}
            blockedStartTimes={attraction.blocked_start_times}
          />
        )
      case ProductPricingVariant.REGULAR_VARIANTS:
        return (
          <RegularSimplifiedProduct
            variants={attraction.product.variants}
            getPayloadRef={regularSimplifiedProductPayloadRef}
            blockedDates={attraction.blocked_dates}
            blockedDatesByMonth={attraction.blocked_dates_by_month}
            startTimes={attraction.start_times}
            blockedStartTimes={attraction.blocked_start_times}
          />
        )
      default:
        return null
    }
  }

  return (
    <div
      className="relative min-h-[80vh] flex flex-col items-center justify-center"
      id="attraction-form"
    >
      {attraction.cart_section_image && (
        <WithLoadingImage
          imageSrc={attraction.cart_section_image}
          alt="Cart section background"
          className="absolute inset-0 object-cover"
          fill
        />
      )}

      <div className="max-xs:px-6  px-12 py-8 sm:px-24 sm:py-16 backdrop-blur-lg bg-black/50 relative z-50 transition-all duration-300 rounded-xl w-[90vw] max-w-[1000px]">
        <h1 className="text-hero_mobile lg:text-heading_main leading-tight grow-[2] mb-8 text-white font-bold">
          Dane do rezerwacji
        </h1>

        <Toaster />

        <form
          onSubmit={handleSubmit}
          className="flex flex-col gap-6 grow-[3] justify-between"
        >
          <div className="flex flex-col gap-6">{renderCartForm()}</div>

          <div className="flex flex-col gap-4">
            <AnimatedCartButton
              isLoading={isSubmitting || isStoreSettingsLoading}
              isSuccess={isSuccess}
              disabled={!isShopEnabled}
              buttonText={!isShopEnabled ? "Rezerwacje dostępne od 01.04" : "Dodaj do koszyka"}
            />
          </div>
        </form>
      </div>

      <AddedToCartPopover
        isOpen={showAddedToCart}
        onClose={() => setShowAddedToCart(false)}
      />
    </div>
  )
}

function isBoatRentalPayload(
  payload: CartPayload
): payload is BoatRentalGetCartPayloadResult {
  return "variants" in payload
}

function isDynamicGroupPayload(
  payload: CartPayload
): payload is DynamicGroupGetCartPayloadResult {
  return "selectedGroups" in payload
}

function isRegularSimplifiedProductPayload(
  payload: CartPayload
): payload is RegularSimplifiedProductGetCartPayloadResult {
  return "variant" in payload
}

export default AddToCartSection
