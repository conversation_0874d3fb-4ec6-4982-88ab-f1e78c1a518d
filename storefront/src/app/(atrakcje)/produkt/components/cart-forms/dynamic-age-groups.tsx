import { useEffect, useImperativeHandle, useState } from "react"
import CalendarPicker from "components/reusable/calendar-picker"
import { isWithinInterval, parseISO } from "date-fns"
import { MinusCircle, PlusCircle } from "lucide-react"
import { clsx } from "clsx"
import CustomPopover from "components/reusable/custom-popover"
import { AgeGroup, FoodOption } from "types/cart"
import { DynamicGroupGetCartPayloadResult } from "types/cart"
import { Variant } from "types/cart"
import {
  AttractionAvailableStartPlaces,
  AttractionBlockedDates,
  AttractionBlockedDatesByMonth,
  StartPlacesByDate,
  StartTimes,
  TourBlockedStartTimes,
} from "types/global"
import ErrorMessage from "components/reusable/error-message"
import useStartTimes from "hooks/useStartTimes"
import { sortByAgeGroup } from "@lib/utils"
import TimeSlotPicker from "components/reusable/time-slot-picker"

type Props = {
  foodOptions: FoodOption[]
  variants: Variant[]
  isAtLeastOneFoodOptionRequired: boolean
  blockedDates: AttractionBlockedDates[]
  blockedDatesByMonth: AttractionBlockedDatesByMonth[]
  startPlacesExcludedByDate: StartPlacesByDate[] | null
  startPlaces: AttractionAvailableStartPlaces[]
  startTimes: StartTimes | null
  blockedStartTimes: TourBlockedStartTimes[]
  getPayloadRef: React.RefObject<{
    getPayload: () => DynamicGroupGetCartPayloadResult
  }>
}

export const DynamicAgeGroups = ({
  foodOptions,
  variants,
  blockedDates,
  blockedDatesByMonth,
  startPlaces,
  startPlacesExcludedByDate,
  startTimes,
  getPayloadRef,
  isAtLeastOneFoodOptionRequired,
  blockedStartTimes,
}: Props) => {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined)

  const [selectedStartPlace, setSelectedStartPlace] = useState<
    AttractionAvailableStartPlaces | undefined
  >(undefined)

  const [ageGroups, setAgeGroups] = useState<AgeGroup[]>([])

  const [availableStartPlaces, setAvailableStartPlaces] = useState<
    AttractionAvailableStartPlaces[]
  >([])

  const [foodOptionsState, setFoodOptionsState] = useState<FoodOption[]>(
    foodOptions.map((option) => ({
      ...option,
      quantity: 0,
    }))
  )

  const [errors, setErrors] = useState({
    selectedDate: null,
    selectedStartPlace: null,
    selectedStartTime: null,
    ageGroups: null,
    foodOptions: null,
  })

  const { selectedStartTime, availableStartTimes, handleChangeStartTime } =
    useStartTimes({
      selectedDate: selectedDate || undefined,
      startTimes,
      disabledStartTimes: blockedStartTimes,
    })

  const handleRaiseError = (field: keyof typeof errors, error: string) => {
    setErrors((prev) => ({ ...prev, [field]: error }))
  }

  const handleClearError = (field: keyof typeof errors) => {
    setErrors((prev) => ({ ...prev, [field]: null }))
  }

  // Update available start places when selected date changes
  useEffect(() => {
    if (!selectedDate || !startPlaces) {
      setAvailableStartPlaces([])
      setSelectedStartPlace(undefined)
      return
    }

    // By default, all start places are available
    let placesToShow = [...startPlaces]

    // If we have exclusion data, filter out places that should be excluded for the selected date
    if (startPlacesExcludedByDate && startPlacesExcludedByDate.length > 0) {
      const excludedPlaces = startPlacesExcludedByDate
        .filter((placeData) => {
          if (selectedDate.getMonth() !== placeData.month - 1) return false
          const excludedDays = placeData.days
            .split(",")
            .map((day) => parseInt(day.trim(), 10))
          return excludedDays.includes(selectedDate.getDate())
        })
        .map((placeData) => placeData.place)

      // Remove excluded places from the available places
      if (excludedPlaces.length > 0) {
        placesToShow = startPlaces.filter(
          (place) => !excludedPlaces.includes(place.place)
        )
      }
    }

    setAvailableStartPlaces(placesToShow)

    // Revalidate selected start place after places are updated
    if (selectedStartPlace) {
      const isStillAvailable = placesToShow.some(
        (place) => place.id === selectedStartPlace.id
      )
      if (!isStillAvailable) {
        setSelectedStartPlace(undefined)
      }
    }
  }, [selectedDate, startPlaces, startPlacesExcludedByDate])

  const revalidateError = (field: keyof typeof errors) => {
    setErrors((prev) => ({ ...prev, [field]: null }))
  }

  // Determine which variant to use based on selected date
  const getActiveVariant = (group: AgeGroup): Variant => {
    if (
      selectedDate &&
      group.seasonalVariants &&
      group.seasonalVariants.length > 0
    ) {
      const compareDate = new Date(selectedDate.setHours(0, 0, 0, 0))

      // Find the first matching seasonal variant for the selected date
      const matchingVariant = group.seasonalVariants.find((variant) => {
        if (
          typeof variant.metadata?.start_date === "string" &&
          typeof variant.metadata?.end_date === "string"
        ) {
          const startDate = new Date(
            parseISO(variant.metadata.start_date).setHours(0, 0, 0, 0)
          )
          const endDate = new Date(
            parseISO(variant.metadata.end_date).setHours(0, 0, 0, 0)
          )

          return isWithinInterval(compareDate, {
            start: startDate,
            end: endDate,
          })
        }
        return false
      })

      if (matchingVariant) {
        return matchingVariant
      }
    }
    return group.defaultVariant
  }

  useImperativeHandle(
    getPayloadRef,
    () => ({
      getPayload: () => {
        if (!selectedDate) {
          handleRaiseError("selectedDate", "Wybierz datę")
          throw new Error("Wybierz datę")
        }
        handleClearError("selectedDate")

        if (!selectedStartPlace && availableStartPlaces.length > 0) {
          handleRaiseError("selectedStartPlace", "Wybierz miejsce startu")
          throw new Error("Wybierz miejsce startu")
        }
        handleClearError("selectedStartPlace")

        if (availableStartTimes.length > 0 && !selectedStartTime) {
          handleRaiseError("selectedStartTime", "Wybierz godzinę")
          throw new Error("Wybierz godzinę")
        }
        handleClearError("selectedStartTime")

        if (!areParticipantsSelected) {
          handleRaiseError("ageGroups", "Dodaj co najmniej jedną osobę")
          throw new Error("Dodaj co najmniej jedną osobę")
        }
        handleClearError("ageGroups")

        if (
          foodOptions.length > 0 &&
          isAtLeastOneFoodOptionRequired &&
          !areFoodOptionsSelected
        ) {
          handleRaiseError("foodOptions", "Dodaj co najmniej jeden posiłek")
          throw new Error("Dodaj co najmniej jeden posiłek")
        }
        handleClearError("foodOptions")

        return {
          selectedDate,
          selectedGroups: ageGroups
            .filter((group) => group.count > 0)
            .map((group) => ({
              ...group,
              defaultVariant: group.defaultVariant,
              activeVariant: getActiveVariant(group),
            })),
          foodOptions: foodOptionsState.filter((option) => option.quantity > 0),
          startPlace: selectedStartPlace,
          startTime: selectedStartTime,
        }
      },
    }),
    [
      ageGroups,
      foodOptionsState,
      selectedDate,
      selectedStartPlace,
      selectedStartTime,
      availableStartPlaces,
      availableStartTimes,
    ]
  )

  // Process variants into age groups
  useEffect(() => {
    const groups: AgeGroup[] = []
    const processedGroups = new Set()

    variants.forEach((variant) => {
      const groupName = variant.metadata?.age_group_name || variant.title

      if (!processedGroups.has(groupName)) {
        processedGroups.add(groupName)

        const defaultVariant = variants.find(
          (v) =>
            v.title === groupName &&
            !v.metadata?.start_date &&
            !v.metadata?.end_date
        )

        const seasonalVariants = variants.filter(
          (v) =>
            v.metadata?.age_group_name === groupName &&
            v.metadata?.start_date &&
            v.metadata?.end_date
        )

        if (defaultVariant) {
          groups.push({
            name: groupName,
            defaultVariant,
            seasonalVariants,
            activeVariant: defaultVariant,
            count: 0,
          })
        }
      }
    })

    setAgeGroups(groups)
  }, [variants])

  const handleAgeGroupSelection = (group: AgeGroup, increment: boolean) => {
    setAgeGroups((prev) => {
      const newGroups = [...prev]
      const index = newGroups.findIndex((g) => g.name === group.name)

      if (index !== -1) {
        newGroups[index] = {
          ...newGroups[index],
          count: increment
            ? newGroups[index].count + 1
            : Math.max(0, newGroups[index].count - 1),
        }
      } else {
        newGroups.push({
          ...group,
          count: increment ? 1 : 0,
        })
      }

      return newGroups
    })
  }

  const handleChangeStartPlace = (
    selectedStartPlace: AttractionAvailableStartPlaces
  ) => {
    setSelectedStartPlace(selectedStartPlace)
    revalidateError("selectedStartPlace")
  }

  const updateFoodCount = (index: number, increment: boolean) => {
    setFoodOptionsState((prev) => {
      const newOptions = [...prev]
      const option = newOptions[index]

      if (increment) {
        newOptions[index] = { ...option, quantity: option.quantity + 1 }
      } else {
        // Don't allow negative quantities
        if (option.quantity <= 0) return prev
        newOptions[index] = { ...option, quantity: option.quantity - 1 }
      }

      return newOptions
    })
  }

  const amountOfParticipants = ageGroups.reduce(
    (acc, group) => acc + group.count,
    0
  )

  const areParticipantsSelected = amountOfParticipants > 0
  const areFoodOptionsAvailable = foodOptions.length > 0
  const areFoodOptionsSelected = foodOptionsState.some(
    (option) => option.quantity > 0
  )
  const shouldShowFoodOptions =
    areFoodOptionsSelected ||
    (areParticipantsSelected && areFoodOptionsAvailable)
  const shouldShowStartPlace =
    selectedStartPlace ||
    (areParticipantsSelected &&
      (!areFoodOptionsAvailable || areFoodOptionsSelected) &&
      availableStartPlaces.length > 0)
  const shouldShowStartTime =
    selectedStartTime ||
    (areParticipantsSelected &&
      (!areFoodOptionsAvailable || areFoodOptionsSelected) &&
      startTimes &&
      availableStartTimes.length > 0)

  const sortedAgeGroups = sortByAgeGroup(
    ageGroups.map((group) => ({
      ...group,
      title: group.name,
    }))
  )

  return (
    <div className="grid sm:grid-cols-2 gap-4">
      <div className="col-span-full">
        <CalendarPicker
          blockedDates={blockedDates}
          blockedDatesByMonth={blockedDatesByMonth}
          value={selectedDate || undefined}
          onChange={(date) => {
            setSelectedDate(date)
            revalidateError("selectedDate")
          }}
          variant="transparent"
          footerSlot={<ErrorMessage error={errors.selectedDate} />}
        />
        <ErrorMessage error={errors.selectedDate} />
      </div>
      {selectedDate && (
        <div
          className={clsx("opacity-0 grow", {
            "opacity-100": selectedDate,
          })}
        >
          <CustomPopover
            popoverButtonContent={
              <>
                <div className="flex items-center gap-2 justify-between">
                  Ilość osób
                  {areParticipantsSelected && (
                    <div className="px-4 py-1 text-sm border-secondary border animate-fade-in-right rounded-xl ">
                      {amountOfParticipants}
                    </div>
                  )}
                </div>
                <ErrorMessage error={errors.ageGroups} />
              </>
            }
            fitWidthPanel
            variant="transparent"
          >
            {sortedAgeGroups.map((group) => (
              <div
                key={group.name}
                className="flex items-center justify-between p-4 border rounded-lg gap-4"
              >
                <div>
                  <p className="text-sm text-secondary">
                    {group.defaultVariant.title}
                  </p>
                </div>

                <div className="flex items-center gap-2">
                  <button
                    type="button"
                    onClick={() => {
                      handleAgeGroupSelection(group, false)
                      revalidateError("ageGroups")
                    }}
                    className="text-secondary hover:text-gray-90 transition-colors"
                  >
                    <MinusCircle className="w-6 h-6" />
                  </button>

                  <span className="w-8 text-center font-medium">
                    {group.count}
                  </span>

                  <button
                    type="button"
                    onClick={() => {
                      handleAgeGroupSelection(group, true)
                      revalidateError("ageGroups")
                    }}
                    className="text-secondary hover:text-gray-90 transition-colors"
                  >
                    <PlusCircle className="w-6 h-6" />
                  </button>
                </div>
              </div>
            ))}
          </CustomPopover>
        </div>
      )}
      {shouldShowFoodOptions && (
        <div
          className={clsx("opacity-0 transition-opacity duration-300 grow", {
            "opacity-100": shouldShowFoodOptions,
          })}
        >
          <CustomPopover
            popoverButtonContent={
              <>
                <div className="flex items-center gap-2 justify-between">
                  Posiłki
                  {areFoodOptionsSelected && (
                    <div className="px-4 py-1 text-sm border-secondary border animate-fade-in-right rounded-xl ">
                      {foodOptionsState.reduce(
                        (acc, option) => acc + option.quantity,
                        0
                      )}
                    </div>
                  )}
                </div>
              </>
            }
            variant="transparent"
          >
            <>
              {isAtLeastOneFoodOptionRequired && !areFoodOptionsSelected && (
                <div className="flex items-center justify-between px-4 py-2 border rounded-lg">
                  <p className="text-sm text-gray-50 text-center">
                    Przynajmniej jeden posiłek jest wymagany
                  </p>
                </div>
              )}
              {foodOptionsState.map((option, index) => (
                <div
                  key={option.id}
                  className="flex items-center justify-between px-4 py-2 border rounded-lg"
                >
                  <div>
                    <p className="text-sm">{option.name}</p>
                  </div>

                  <div className="flex items-center gap-2">
                    <button
                      type="button"
                      onClick={() => updateFoodCount(index, false)}
                      className={clsx("text-secondary transition-colors", {
                        "opacity-50 cursor-not-allowed":
                          (option.required && option.quantity <= 1) ||
                          option.quantity <= 0,
                        "hover:text-gray-90":
                          !option.required || option.quantity > 1,
                      })}
                      disabled={
                        (option.required && option.quantity <= 1) ||
                        option.quantity <= 0
                      }
                    >
                      <MinusCircle className="w-6 h-6" />
                    </button>

                    <span className="w-8 text-center font-medium">
                      {option.quantity}
                    </span>

                    <button
                      type="button"
                      onClick={() => updateFoodCount(index, true)}
                      className="text-secondary hover:text-gray-90 transition-colors"
                    >
                      <PlusCircle className="w-6 h-6" />
                    </button>
                  </div>
                </div>
              ))}
            </>
          </CustomPopover>
          <ErrorMessage error={errors.foodOptions} />
        </div>
      )}
      {shouldShowStartPlace && (
        <div
          className={clsx("opacity-0 transition-opacity duration-300 grow", {
            "opacity-100": shouldShowStartPlace,
          })}
        >
          <CustomPopover
            fitWidthPanel
            popoverButtonContent={
              <div className="flex items-center gap-2 justify-between">
                Miejsce startu
                {selectedStartPlace && (
                  <div className="px-4 py-1 text-sm border-secondary border animate-fade-in-right rounded-xl ">
                    {selectedStartPlace.place}
                  </div>
                )}
                <ErrorMessage error={errors.selectedStartPlace} />
              </div>
            }
            variant="transparent"
          >
            {(close) => (
              <>
                {availableStartPlaces.map((place) => (
                  <button
                    type="button"
                    onClick={() => {
                      handleChangeStartPlace(place)
                      close()
                    }}
                    key={place.id}
                    className="border px-4 py-2 hover:shadow-sm hover:border hover:border-secondary cursor-pointer rounded-md min-w-[150px]"
                  >
                    {place.place}
                  </button>
                ))}
              </>
            )}
          </CustomPopover>
        </div>
      )}
      {shouldShowStartTime && (
        <div
          className={clsx("opacity-0 transition-opacity duration-300 grow", {
            "opacity-100": shouldShowStartTime,
          })}
        >
          <TimeSlotPicker
            availableTimeSlots={availableStartTimes}
            selectedTime={selectedStartTime ?? undefined}
            onChange={handleChangeStartTime}
            error={errors["selectedStartTime"] ?? undefined}
            label="Godzina startu"
            onValidationChange={() => revalidateError("selectedStartTime")}
          />
        </div>
      )}
    </div>
  )
}
