import { useImperative<PERSON><PERSON><PERSON>, useState } from "react"
import { Variant } from "types/cart"
import { BoatRentalGetCartPayloadResult } from "types/cart"
import CalendarPicker from "components/reusable/calendar-picker"
import {
  isWithinInterval,
  parseISO,
  eachDayOfInterval,
  isSameDay,
} from "date-fns"
import {
  AttractionBlockedDates,
  AttractionBlockedDatesByMonth,
  StartTimes,
  TourBlockedStartTimes,
} from "types/global"
import { DateRange } from "react-day-picker"
import ErrorMessage from "components/reusable/error-message"
import useStartTimes from "hooks/useStartTimes"
import TimeSlotPicker from "components/reusable/time-slot-picker"

interface BoatRentalProps {
  variants: Variant[]
  blockedDates?: AttractionBlockedDates[]
  blockedDatesByMonth?: AttractionBlockedDatesByMonth[]
  startTimes?: StartTimes | null
  getPayloadRef: React.RefObject<{
    getPayload: () => BoatRentalGetCartPayloadResult
  }>
  blockedStartTimes?: TourBlockedStartTimes[]
}

const BoatRental: React.FC<BoatRentalProps> = ({
  variants,
  blockedDates = [],
  blockedDatesByMonth = [],
  startTimes = null,
  getPayloadRef,
  blockedStartTimes = [],
}) => {
  const [startDate, setStartDate] = useState<Date | undefined>(undefined)
  const [endDate, setEndDate] = useState<Date | undefined>(undefined)

  const [error, setError] = useState<string | null>(null)
  const [errors, setErrors] = useState<{
    selectedDate: string | null
    selectedStartTime: string | null
  }>({
    selectedDate: null,
    selectedStartTime: null,
  })

  // Find default variant (without seasonal dates)
  const defaultVariant = variants.find(
    (variant) => !variant.metadata?.start_date && !variant.metadata?.end_date
  )

  const { selectedStartTime, availableStartTimes, handleChangeStartTime } =
    useStartTimes({
      selectedDate: startDate || undefined,
      startTimes,
      disabledStartTimes: blockedStartTimes,
    })

  // Check if a date is blocked
  const isDateBlocked = (date: Date): boolean => {
    // Check blockedDates
    const isInBlockedDates = blockedDates.some((blockedDate) => {
      if (blockedDate.only_one_day) {
        return isSameDay(parseISO(blockedDate.date_from), date)
      }
      return isWithinInterval(date, {
        start: parseISO(blockedDate.date_from),
        end: parseISO(blockedDate.date_to),
      })
    })
    if (isInBlockedDates) return true

    // Check blockedDatesByMonth
    const month = date.getMonth() + 1 // JavaScript months are 0-based
    const day = date.getDate()

    return blockedDatesByMonth.some(
      (blocked) =>
        blocked.month === month &&
        blocked.days.split(",").map(Number).includes(day)
    )
  }

  // Check if date range contains any blocked dates
  const hasBlockedDatesInRange = (from: Date, to: Date): boolean => {
    const dates = eachDayOfInterval({ start: from, end: to })
    return dates.some((date) => isDateBlocked(date))
  }

  // Get appropriate variant for a specific date
  const getVariantForDate = (date: Date): Variant => {
    const compareDate = new Date(date.setHours(0, 0, 0, 0))

    // Find seasonal variant that matches the date
    const seasonalVariant = variants.find((variant) => {
      if (variant.metadata?.start_date && variant.metadata?.end_date) {
        const startDate = new Date(
          parseISO(variant.metadata.start_date).setHours(0, 0, 0, 0)
        )
        const endDate = new Date(
          parseISO(variant.metadata.end_date).setHours(0, 0, 0, 0)
        )

        return isWithinInterval(compareDate, {
          start: startDate,
          end: endDate,
        })
      }
      return false
    })

    return seasonalVariant || defaultVariant || variants[0]
  }

  // Get array of daily variants between start and end date
  const getDailyVariants = (): Variant[] => {
    if (!startDate || !endDate) return []

    return eachDayOfInterval({ start: startDate, end: endDate }).map((date) =>
      getVariantForDate(date)
    )
  }

  const handleDateSelect = (date: DateRange) => {
    setError(null)
    revalidateError("selectedDate")

    if (date?.from && date?.to && hasBlockedDatesInRange(date.from, date.to)) {
      setError(
        "Wybrane daty zawierają zablokowane terminy. Wybierz ciągły zakres dostępnych dat."
      )
      return
    }

    setStartDate(date?.from ?? undefined)
    setEndDate(date?.to ?? undefined)
  }

  const handleRaiseError = (field: keyof typeof errors, error: string) => {
    setErrors((prev) => ({ ...prev, [field]: error }))
  }

  const revalidateError = (field: keyof typeof errors) => {
    setErrors((prev) => ({ ...prev, [field]: null }))
  }

  const shouldShowStartTime =
    startDate && endDate && startTimes && availableStartTimes.length > 0

  useImperativeHandle(
    getPayloadRef,
    () => ({
      getPayload: () => {
        if (!startDate || !endDate) {
          handleRaiseError("selectedDate", "Wybierz datę początkową i końcową")
          throw new Error("Wybierz datę początkową i końcową")
        }

        if (hasBlockedDatesInRange(startDate, endDate)) {
          setError("Wybrane daty zawierają zablokowane terminy")
          throw new Error("Wybrane daty zawierają zablokowane terminy")
        }

        const dailyVariants = getDailyVariants()
        if (dailyVariants.length === 0) {
          setError("Brak dostępnych wariantów dla wybranych dat")
          throw new Error("Brak dostępnych wariantów dla wybranych dat")
        }

        if (
          startTimes &&
          !selectedStartTime &&
          availableStartTimes.length > 0
        ) {
          handleRaiseError("selectedStartTime", "Wybierz godzinę startu")
          throw new Error("Wybierz godzinę startu")
        }

        return {
          selectedDate: {
            from: startDate,
            to: endDate,
          },
          variants: dailyVariants,
          startTime: selectedStartTime,
        }
      },
    }),
    [
      startDate,
      endDate,
      error,
      startTimes,
      selectedStartTime,
      availableStartTimes.length,
    ]
  )

  return (
    <div className="grid grid-cols-2 gap-4">
      <div className="col-span-full">
        <CalendarPicker
          blockedDates={blockedDates}
          blockedDatesByMonth={blockedDatesByMonth}
          value={{ from: startDate ?? undefined, to: endDate ?? undefined }}
          range={true}
          onChange={handleDateSelect}
          variant="transparent"
          footerSlot={
            error ? (
              <ErrorMessage error={error} />
            ) : (
              <div className="text-white text-sm">
                Wybierz datę
                <span className="text-secondary font-medium">początkową</span> i
                <span className="text-secondary font-medium">końcową</span>
              </div>
            )
          }
        />
        {errors.selectedDate && <ErrorMessage error={errors.selectedDate} />}
      </div>

      {shouldShowStartTime && (
        <div className="col-span-full">
          <TimeSlotPicker
            availableTimeSlots={availableStartTimes}
            selectedTime={selectedStartTime ?? undefined}
            onChange={handleChangeStartTime}
            error={errors["selectedStartTime"] ?? undefined}
            label="Godzina startu"
            onValidationChange={() => revalidateError("selectedStartTime")}
          />
        </div>
      )}

      <div className="col-span-full">
        {error && <div className="text-destructive text-sm mb-2">{error}</div>}
        {startDate && !endDate && (
          <div className="text-white text-sm">Wybierz datę końcową</div>
        )}
        {startDate && endDate && (
          <div className="space-y-2">
            <div className="text-white text-sm font-medium">Wybrane daty:</div>
            <div className="px-4 py-2 bg-white/10 rounded-lg">
              <div className="text-white">
                {startDate.toLocaleDateString("pl-PL")} -{" "}
                {endDate.toLocaleDateString("pl-PL")}
              </div>
              <div className="text-sm text-white/70 mt-1">
                Liczba dni: {getDailyVariants().length}
              </div>
              {selectedStartTime && (
                <div className="text-sm text-white/70 mt-1">
                  Godzina startu: {selectedStartTime}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default BoatRental
