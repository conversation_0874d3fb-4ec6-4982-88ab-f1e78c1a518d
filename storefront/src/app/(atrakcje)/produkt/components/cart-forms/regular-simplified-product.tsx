import { useEffect, useImperativeHandle, useState } from "react"
import {
  RegularSimplifiedProductGetCartPayloadResult,
  Variant,
} from "types/cart"
import CustomPopover from "components/reusable/custom-popover"
import { clsx } from "clsx"
import ErrorMessage from "components/reusable/error-message"
import CalendarPicker from "components/reusable/calendar-picker"
import useCalendarDate from "hooks/useCalendarDate"
import useStartTimes from "hooks/useStartTimes"
import {
  AttractionBlockedDates,
  AttractionBlockedDatesByMonth,
  StartTimes,
  TourBlockedStartTimes,
} from "types/global"
import { useQueryState } from "nuqs"
import QUERY_PARAM_KEYS from "@lib/constants/urlParams"
import TimeSlotPicker from "components/reusable/time-slot-picker"

interface RegularSimplifiedProductProps {
  variants: Variant[]
  getPayloadRef: React.RefObject<{
    getPayload: () => RegularSimplifiedProductGetCartPayloadResult
  }>
  blockedDates?: AttractionBlockedDates[]
  blockedDatesByMonth?: AttractionBlockedDatesByMonth[]
  startTimes?: StartTimes | null
  blockedStartTimes?: TourBlockedStartTimes[]
}

const RegularSimplifiedProduct: React.FC<RegularSimplifiedProductProps> = ({
  variants,
  getPayloadRef,
  blockedDates = [],
  blockedDatesByMonth = [],
  blockedStartTimes = [],
  startTimes = null,
}) => {
  const [selectedVariant, setSelectedVariant] = useState<Variant | null>(null)
  const [selectedVariantId, setSelectedVariantId] = useState<string | null>(
    null
  )

  useEffect(() => {
    setSelectedVariant(
      variants.find((variant) => variant.id === selectedVariantId) || null
    )
  }, [selectedVariantId])

  const [errors, setErrors] = useState<{
    variant: string | null
    selectedDate: string | null
    selectedStartTime: string | null
  }>({
    variant: null,
    selectedDate: null,
    selectedStartTime: null,
  })

  const { selectedDate, setSelectedDate } = useCalendarDate({
    blockedDates,
    blockedDatesByMonth,
  })

  const { selectedStartTime, availableStartTimes, handleChangeStartTime } =
    useStartTimes({
      selectedDate: selectedDate || undefined,
      startTimes,
      disabledStartTimes: blockedStartTimes,
    })

  const handleRaiseError = (field: keyof typeof errors, error: string) => {
    setErrors((prev) => ({ ...prev, [field]: error }))
  }

  const revalidateError = (field: keyof typeof errors) => {
    setErrors((prev) => ({ ...prev, [field]: null }))
  }

  const shouldShowStartTime =
    selectedVariant &&
    selectedDate &&
    startTimes &&
    availableStartTimes &&
    availableStartTimes.length > 0

  useImperativeHandle(
    getPayloadRef,
    () => ({
      getPayload: () => {
        if (!selectedDate) {
          handleRaiseError("selectedDate", "Wybierz datę")
          throw new Error("Wybierz datę")
        }

        if (!selectedVariant) {
          handleRaiseError("variant", "Wybierz wariant produktu")
          throw new Error("Wybierz wariant produktu")
        }

        if (startTimes && !selectedDate) {
          handleRaiseError("selectedDate", "Wybierz datę")
          throw new Error("Wybierz datę")
        }

        if (
          startTimes &&
          selectedDate &&
          !selectedStartTime &&
          availableStartTimes &&
          availableStartTimes?.length > 0
        ) {
          handleRaiseError("selectedStartTime", "Wybierz godzinę startu")
          throw new Error("Wybierz godzinę startu")
        }

        return {
          variant: selectedVariant,
          selectedDate,
          startTime: selectedStartTime,
        }
      },
    }),
    [selectedVariant, selectedDate, selectedStartTime, startTimes]
  )

  return (
    <div className="grid grid-cols-1 gap-4">
      <CalendarPicker
        variant="transparent"
        blockedDates={blockedDates}
        blockedDatesByMonth={blockedDatesByMonth}
        value={selectedDate || undefined}
        onChange={(date) => {
          setSelectedDate(date)
          revalidateError("selectedDate")
        }}
      />
      {errors.selectedDate && <ErrorMessage error={errors.selectedDate} />}

      <div className="flex flex-wrap gap-4">
        <div className="grow">
          {selectedDate && (
            <CustomPopover
              popoverButtonContent={
                <>
                  <div className="flex items-center gap-2 justify-between ">
                    Wybierz wariant
                    {selectedVariant && (
                      <div className="px-4 py-1 text-sm border-secondary border animate-fade-in-right rounded-xl ">
                        {selectedVariant.title}
                      </div>
                    )}
                  </div>

                  <ErrorMessage error={errors.variant} />
                </>
              }
              variant="transparent"
            >
              {(close) => (
                <div className="grid gap-2">
                  {variants.map((variant) => (
                    <button
                      type="button"
                      key={variant.id}
                      onClick={() => {
                        setSelectedVariant(variant)
                        setSelectedVariantId(variant.id)
                        revalidateError("variant")
                        close()
                      }}
                      className={clsx(
                        "border px-4 py-2 hover:shadow-sm hover:border-secondary cursor-pointer rounded-md text-left transition-all",
                        {
                          "border-secondary":
                            selectedVariant?.id === variant.id,
                          "border-gray-200": selectedVariant?.id !== variant.id,
                        }
                      )}
                    >
                      <div className="font-medium">{variant.title}</div>
                    </button>
                  ))}
                </div>
              )}
            </CustomPopover>
          )}
        </div>

        {shouldShowStartTime && (
          <div
            className={clsx("opacity-0 transition-opacity duration-300 grow ", {
              "opacity-100": shouldShowStartTime,
            })}
          >
            <TimeSlotPicker
              availableTimeSlots={availableStartTimes}
              selectedTime={selectedStartTime ?? undefined}
              onChange={handleChangeStartTime}
              error={errors["selectedStartTime"] ?? undefined}
              label="Godzina startu"
              onValidationChange={() => revalidateError("selectedStartTime")}
            />
          </div>
        )}
      </div>
    </div>
  )
}

export default RegularSimplifiedProduct
