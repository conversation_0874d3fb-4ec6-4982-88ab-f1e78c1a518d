import CalendarIcon from "@icons/calendar.svg"
import ClockIcon from "@icons/clock.svg"
import MoneyIcon from "@icons/money.svg"
import EuroIcon from "@icons/euro.svg"

import Image from "next/image"
import { AttractionDetails } from "types/global"
import Link from "next/link"
import AttractionAttribute from "./attraction-conditional-attributes"
import RichTextRenderer from "components/reusable/rich-text-renderer"
import WithLoadingImage from "components/reusable/with-loading-image"
import clsx from "clsx"
import PromoBadge from "./promo-badge"

type Props = {
  attraction: AttractionDetails
}

const AttractionCard = ({ attraction }: Props) => {
  const getBorderClass = () => {
    if (attraction.is_recommended) return "border-recommended"
    if (attraction.is_bargain) return "border-bargain"
    if (attraction.is_promotion) return "border-promotion"
    return "border-gray-200"
  }

  return (
    <Link href={`/produkt/${attraction.slug}`} className="h-full">
      <div
        className={clsx(
          "p-3.5 sm:p-5 relative border-2 rounded-2xl flex flex-col h-full text-ellipsis",
          getBorderClass()
        )}
      >
        {attraction.is_recommended && (
          <PromoBadge backgroundColor="bg-recommended" textColor="text-white">
            Polecane
          </PromoBadge>
        )}

        {attraction.is_bargain && (
          <PromoBadge backgroundColor="bg-bargain" textColor="text-white">
            Okazja!
          </PromoBadge>
        )}

        {attraction.is_promotion && (
          <PromoBadge backgroundColor="bg-promotion" textColor="text-white">
            Promocja!
          </PromoBadge>
        )}

        <div className="relative min-h-48 sm:min-h-64 overflow-hidden rounded-xl">
          <WithLoadingImage
            imageSrc={attraction.featured_image}
            alt={attraction.name}
            fill
            className="object-cover"
          />

          {/* TODO: Commented out for now - leave this if requirements change */}
          {/* <AttractionAttribute.City mode="short" attraction={attraction} /> */}

          {/* Badge overlay */}
        </div>

        <h2 className="sm:text-xl font-semibold grow my-3">
          {attraction.name}
        </h2>

        <div className="text-gray-50 line-clamp-2 text-xs min-h-8">
          <RichTextRenderer content={attraction.description} />
        </div>

        <div className="flex gap-3 mt-3 text-xs text-gray-50 flex-wrap items-center justify-end">
          {attraction.hourly_length ? (
            <div className="flex gap-2 items-center">
              <Image src={ClockIcon} alt="price" className="size-4" />
              <span className="inline-block whitespace-nowrap">
                {attraction?.hourly_length}
              </span>
            </div>
          ) : null}
          {attraction.dates_from_to ? (
            <div className="flex gap-2 items-center">
              <Image src={CalendarIcon} alt="price" className="size-4" />
              <span className="inline-block whitespace-nowrap">
                {attraction?.dates_from_to}
              </span>
            </div>
          ) : null}
          {attraction.price_from || attraction.promotion_price_from ? (
            <div className="flex gap-2 items-center justify-end">
              <Image src={MoneyIcon} alt="price" className="size-4" />
              <span className="inline-block whitespace-nowrap">
                {attraction.promotion_price_from ? (
                  <div className="flex gap-2 items-center">
                    <span className="line-through text-gray-50">
                      od {attraction.price_from} €
                    </span>{" "}
                    <span className="text-bargain font-semibold text-2xl">
                      {attraction.promotion_price_from} €
                    </span>
                  </div>
                ) : (
                  <>od {attraction?.price_from}</>
                )}
              </span>
            </div>
          ) : null}
        </div>
      </div>
    </Link>
  )
}

export default AttractionCard
