"use client"

import clsx from "clsx"
import Carousel from "components/carousel/carousel"
import Link from "next/link"
import Image from "next/image"
import NextArrow from "@images/link-arrow.png"
import WithLoadingImage from "components/reusable/with-loading-image"
import { ShortAttraction } from "types/global"

type Props = {
  className?: string
  attractions: ShortAttraction[]
  aspect?: "video" | "square"
}

const RecommendedToursCarousel = ({
  attractions,
  className,
  aspect = "square",
}: Props) => {
  return (
    <Carousel
      className={clsx("w-full max-xl:overflow-x-hidden", className)}
      key={attractions.length}
      options={{
        dragFree: true,
      }}
      asFooterCarousel={true}
    >
      {attractions.map((attraction) => (
        <div key={`${attraction.id}-${attraction.name}`}>
          <Link
            href={`/produkt/${attraction.slug}`}
            className="group flex flex-col h-full "
          >
            <div
              className={clsx(
                "relative w-full mb-2 overflow-hidden rounded-xl",
                aspect === "video" ? "aspect-[16/11]" : "aspect-square"
              )}
            >
              <WithLoadingImage
                imageSrc={attraction.featured_image}
                className="object-cover rounded-xl group-hover:scale-105 transition-transform duration-300"
                fill
                alt={attraction.name}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
            </div>

            <h3 className="text-heading_quaternary font-medium mb-2 mt-4 grow">
              {attraction.name}
            </h3>

            <div className="flex items-center mt-2">
              <Image
                src={NextArrow}
                alt="next arrow"
                className="h-auto w-[20%] min-w-[50px] max-w-[70px] ml-1 transition-transform group-hover:translate-x-1"
              />
            </div>
          </Link>
        </div>
      ))}
    </Carousel>
  )
}

export default RecommendedToursCarousel
