import { ComponentProps } from "react"
import { getRecommendedAttractions } from "@lib/data/attractions"
import RecommendedToursCarousel from "./recommended_tours-carousel"

type Props = {
  attractionIds: string[]
  recommendedToursCarouselProps: Omit<
    ComponentProps<typeof RecommendedToursCarousel>,
    "attractions"
  >
}

const RecommendedAttractionsDataProvider = async ({
  attractionIds,
  recommendedToursCarouselProps,
}: Props) => {
  const { attractions } = await getRecommendedAttractions(attractionIds)

  return (
    <RecommendedToursCarousel
      attractions={attractions}
      {...recommendedToursCarouselProps}
    />
  )
}

export default RecommendedAttractionsDataProvider
