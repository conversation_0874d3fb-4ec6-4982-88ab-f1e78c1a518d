import React from "react"
import { AttractionDetails } from "types/global"

import MapPinIcon from "@icons/map_pin.svg"
import ClockIcon from "@icons/clock.svg"
import CalendarIcon from "@icons/calendar.svg"
import MoneyIcon from "@icons/money.svg"
import EuroIcon from "@icons/euro.svg"
import Image from "next/image"

type Props = {
  attraction: AttractionDetails
}

type CitiesProps = {
  attraction: AttractionDetails
  mode: "short" | "detailed"
}

const AttractionAttribute = (props: Props) => {
  return <div>AttractionAttribute</div>
}

const AttractionAttributeCity = (props: CitiesProps) => {
  const citiesCount = props.attraction.cities.length

  const citiesLongerThanOne = citiesCount > 1

  const sortedCitiesAlphabetically = props.attraction.cities.sort((a, b) =>
    a.name.localeCompare(b.name)
  )

  if (props.mode === "short") {
    return citiesCount > 0 ? (
      <div className="backdrop-blur-lg bg-white/20 w-fit px-4 rounded-xl m-2 flex items-center gap-2 p-2">
        <Image src={MapPinIcon} alt="location" className="size-4" />
        <span className="text-sm text-white inline-block">
          {sortedCitiesAlphabetically[0].name}
          {citiesLongerThanOne &&
            ` + ${sortedCitiesAlphabetically.length - 1} innych`}
        </span>
      </div>
    ) : null
  }

  return (
    <div className="flex gap-2 items-center ">
      <div className="p-2 flex gap-2 border items-center rounded-full w-fit px-3">
        <Image src={MapPinIcon} alt="location" className="size-4" />
        <span className="text-sm text-gray-50 inline-block pr-3 sm:pr-0">
          {sortedCitiesAlphabetically[0].name}
        </span>
      </div>
      {sortedCitiesAlphabetically.length > 1 && (
        <span className="inline-block whitespace-nowrap text-sm text-gray-50 ">
          + {sortedCitiesAlphabetically.length - 1} innych
        </span>
      )}
    </div>
  )
}

const AttractionAttributeHourlyLength = (props: Props) => {
  return props.attraction.hourly_length ? (
    <div className="flex gap-2 items-center text-gray-50">
      <Image src={ClockIcon} alt="zegar" className="size-4" />
      <span className="inline-block whitespace-nowrap">
        {props.attraction?.hourly_length}
      </span>
    </div>
  ) : null
}
const AttractionAttributeDatesFromTo = (props: Props) => {
  return props.attraction.dates_from_to ? (
    <div className="flex gap-2 items-center text-gray-50">
      <Image src={CalendarIcon} alt="kalendarz" className="size-4" />
      <span className="inline-block whitespace-nowrap">
        {props.attraction?.dates_from_to}
      </span>
    </div>
  ) : null
}

const AttractionAttributePriceFrom = (props: Props) => {
  return props.attraction.price_from ? (
    <div className="flex gap-2 items-center text-gray-50">
      <Image src={MoneyIcon} alt="cena" className="size-4" />
      <span className="inline-block whitespace-nowrap">
        od {props.attraction?.price_from}
      </span>
      <Image src={EuroIcon} alt="euro" className="size-4" />
    </div>
  ) : null
}

AttractionAttribute.HourlyLength = AttractionAttributeHourlyLength
AttractionAttribute.DatesFromTo = AttractionAttributeDatesFromTo
AttractionAttribute.PriceFrom = AttractionAttributePriceFrom
AttractionAttribute.City = AttractionAttributeCity

export default AttractionAttribute
