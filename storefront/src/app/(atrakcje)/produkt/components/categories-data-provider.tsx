import { listCategories } from "@lib/data/categories"
import React, { ComponentProps } from "react"
import CategoriesCarousel from "./categories-carousel"

type Props = {
  categoriesCarouselProps: Omit<
    ComponentProps<typeof CategoriesCarousel>,
    "categories"
  >
}

const CategoriesDataProvider = async ({ categoriesCarouselProps }: Props) => {
  const categories = await listCategories()

  return (
    <CategoriesCarousel categories={categories} {...categoriesCarouselProps} />
  )
}

export default CategoriesDataProvider
