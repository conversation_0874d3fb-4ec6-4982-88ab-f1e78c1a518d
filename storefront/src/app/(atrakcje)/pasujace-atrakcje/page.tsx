import SubpageHero from "components/reusable/subpage-hero"
import { listBlogPosts } from "@lib/data/guides"
import PreviousPageBtn from "app/(poradniki)/poradniki/components/previous-page-btn"
import { getAllAvailableAttractions } from "@lib/data/attractions"
import { AttractionsSearchProvider } from "contexts/AttractionsSearchContext"
import { Metadata } from "next"
import MatchingAttractionsBackground from "@images/cart_background.webp"
import AllMatchingAttractions from "./components/all-matching-attractions"

const FILTER_SLOT_ID = "category-filter-slot"

interface Props {
  params: {
    slug: string
  }
}

export const metadata: Metadata = {
  title: "Pasujące atrakcje",
  description: "Pasujące atrakcje",
  robots: {
    index: false,
    follow: false,
    noimageindex: true,
    noarchive: true,
  },
}

async function CategoryDetailsPage({ params }: Props) {
  // Fetch initial data on the server
  const [guides, attractions] = await Promise.all([
    listBlogPosts("", 5),
    getAllAvailableAttractions({
      c: [],
      f: "",
      t: "",
    }),
  ])

  return (
    <div>
      <div className="mb-20">
        <SubpageHero
          imageSrc={MatchingAttractionsBackground.src}
          title={"Pasujące atrakcje"}
        >
          <div id={FILTER_SLOT_ID} />
        </SubpageHero>
      </div>

      <AttractionsSearchProvider>
        <AllMatchingAttractions
          initialGuides={guides}
          initialAttractions={attractions}
        />
      </AttractionsSearchProvider>

      <div className="w-fit mx-auto my-20">
        <PreviousPageBtn />
      </div>
    </div>
  )
}

export default CategoryDetailsPage
