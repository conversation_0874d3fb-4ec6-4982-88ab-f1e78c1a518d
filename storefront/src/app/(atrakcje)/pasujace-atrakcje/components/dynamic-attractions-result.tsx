"use client"

import { queryKeys } from "@lib/constants/queryKeys"
import { getAllAvailableAttractions } from "@lib/data/attractions"
import { useQuery } from "@tanstack/react-query"
import AttractionCard from "app/(atrakcje)/produkt/components/attraction-card"
import { AttractionNavigationSkeletonGrid } from "components/skeletons/attraction-navigation-skeleton"
import { DateRange } from "react-day-picker"
import { AttractionShort } from "types/global"

type Props = {
  selectedCities: string[] | null
  initialAttractions: { attractions: AttractionShort[] }
  dateRange: DateRange
}

const DynamicAttractionsResult = ({
  selectedCities,
  initialAttractions,
  dateRange,
}: Props) => {
  const hasDateRangeSelected = dateRange.from || dateRange.to

  const { data, isPending: isLoading } = useQuery({
    queryKey: [queryKeys.ALL_MATCHING_ATTRACTIONS, selectedCities, dateRange],
    queryFn: () =>
      getAllAvailableAttractions({
        c: selectedCities || null,
        f: dateRange.from?.toISOString() || null,
        t: dateRange.to?.toISOString() || null,
      }),
    initialData:
      !selectedCities && !hasDateRangeSelected ? initialAttractions : undefined,
  })

  const validAttractions = data?.attractions.filter((attraction) => attraction)

  const noMatchingAttractionsFound =
    !isLoading && validAttractions?.length === 0

  return (
    <div className="grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-4 gap-y-5 inner-container-max-w mobile-filter-results-margin">
      {isLoading ? (
        <AttractionNavigationSkeletonGrid count={6} />
      ) : noMatchingAttractionsFound ? (
        <div className="col-span-full min-h-[20vh] flex items-center justify-center">
          <p className="text-center text-gray-50">
            Nie znaleziono atrakcji spełniających podane kryteria.
          </p>
        </div>
      ) : (
        validAttractions?.map((attraction, index) => {
          // @ts-ignore - Type mismatch being handled by filter
          return <AttractionCard attraction={attraction} key={index} />
        })
      )}
    </div>
  )
}

export default DynamicAttractionsResult
