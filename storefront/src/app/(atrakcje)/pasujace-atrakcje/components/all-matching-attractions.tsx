"use client"

import { useQuery } from "@tanstack/react-query"
import { queryKeys } from "@lib/constants/queryKeys"
import AttractionsFilter from "components/reusable/cities-filter"
import { useCitiesFilter } from "hooks/useCitiesFilter"
import { Suspense, useEffect, useState } from "react"
import { listBlogPosts } from "@lib/data/guides"
import RelatedGuides from "app/(poradniki)/poradniki/components/related-guides"
import { createPortal } from "react-dom"
import { AttractionShort } from "types/global"
import DynamicAttractionsResult from "./dynamic-attractions-result"
import { useAttractionsSearchContext } from "contexts/AttractionsSearchContext"
import SearchResults from "components/reusable/search-results"

const FILTER_SLOT_ID = "category-filter-slot"

interface AllMatchingAttractionsProps {
  initialGuides: Awaited<ReturnType<typeof listBlogPosts>>
  initialAttractions: { attractions: AttractionShort[] }
}

export default function AllMatchingAttractions({
  initialGuides,
  initialAttractions,
}: AllMatchingAttractionsProps) {
  const { data: guidesData } = useQuery({
    queryKey: [queryKeys.GUIDES, "all"],
    queryFn: () => listBlogPosts("", 5),
    initialData: initialGuides,
  })

  const {
    selectedCities,
    cities,
    onCityClick,
    handleClearCities,
    dateRange,
    handleClearAllQueries,
    updateDateRangeQuery,
    handleClearDateRange,
  } = useCitiesFilter({})

  const {
    query,
    results: searchResults,
    isLoading: isSearchLoading,
    hasActiveSearch,
    resultsCount,
  } = useAttractionsSearchContext()

  // Portal setup
  const [filterMounted, setFilterMounted] = useState(false)

  useEffect(() => {
    setFilterMounted(true)
  }, [])

  return (
    <>
      {filterMounted &&
        createPortal(
          <AttractionsFilter
            selectedCities={selectedCities}
            cities={cities}
            onCityClick={onCityClick}
            handleClearCities={handleClearCities}
            handleClearAllQueries={handleClearAllQueries}
            updateDateRangeQuery={updateDateRangeQuery}
            dateRange={dateRange}
            handleClearDateRange={handleClearDateRange}
          />,
          document.getElementById(FILTER_SLOT_ID)!
        )}

      <Suspense>
        {hasActiveSearch ? (
          <SearchResults
            results={searchResults}
            isLoading={isSearchLoading}
            query={query}
            resultsCount={resultsCount}
          />
        ) : (
          <DynamicAttractionsResult
            selectedCities={selectedCities}
            initialAttractions={initialAttractions}
            dateRange={dateRange}
          />
        )}
      </Suspense>

      {guidesData && guidesData?.length > 0 && (
        <div className="mt-20 inner-container-max-w">
          <h1 className="text-heading_secondary md:text-heading_main font-bold mb-4">
            Poradniki
          </h1>
          <RelatedGuides relatedGuides={guidesData} className="!my-8" />
        </div>
      )}
    </>
  )
}
