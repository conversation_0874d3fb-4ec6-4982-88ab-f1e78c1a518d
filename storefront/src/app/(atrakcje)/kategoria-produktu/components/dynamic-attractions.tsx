"use client"

import { queryKeys } from "@lib/constants/queryKeys"
import { getAttractionsByCategory } from "@lib/data/attractions"
import { useQuery } from "@tanstack/react-query"
import AttractionCard from "app/(atrakcje)/produkt/components/attraction-card"
import { AttractionNavigationSkeletonGrid } from "components/skeletons/attraction-navigation-skeleton"
import { DateRange } from "react-day-picker"
import { AttractionsByCategoryResult } from "types/global"

type Props = {
  slug: string
  selectedCities: string[] | null
  initialAttractions: AttractionsByCategoryResult
  dateRange: DateRange
  hasActiveSearch?: boolean
}

const DynamicAttractions = ({
  slug,
  selectedCities,
  initialAttractions,
  dateRange,
  hasActiveSearch = false,
}: Props) => {
  const hasDateRangeSelected = dateRange.from || dateRange.to

  const { data, isPending: isLoading } = useQuery({
    queryKey: [queryKeys.CATEGORY_ATTRACTIONS, slug, selectedCities, dateRange],
    queryFn: () =>
      getAttractionsByCategory(slug, {
        c: selectedCities || null,
        f: dateRange.from?.toISOString() || null,
        t: dateRange.to?.toISOString() || null,
      }),
    enabled: !!slug && !hasActiveSearch, // Don't fetch when search is active
    // Only use initialData if no filters (date or cities) are applied
    initialData:
      !selectedCities && !hasDateRangeSelected ? initialAttractions : undefined,
  })

  // Don't render anything when search is active
  if (hasActiveSearch) {
    return null
  }

  const validAttractions = data?.attractions.filter((attraction) => attraction)

  const noMatchingAttractionsFound =
    !isLoading && validAttractions?.length === 0

  return (
    <div className="grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-4 gap-y-5 inner-container-max-w mobile-filter-results-margin">
      {isLoading ? (
        <AttractionNavigationSkeletonGrid count={6} />
      ) : noMatchingAttractionsFound ? (
        <div className="col-span-full min-h-[20vh] flex items-center justify-center">
          <p className="text-center text-gray-50">
            Nie znaleziono atrakcji spełniających podane kryteria.
          </p>
        </div>
      ) : (
        validAttractions?.map((attraction) => {
          // @ts-ignore - Type mismatch being handled by filter
          return <AttractionCard attraction={attraction} key={attraction.id} />
        })
      )}
    </div>
  )
}

export default DynamicAttractions
