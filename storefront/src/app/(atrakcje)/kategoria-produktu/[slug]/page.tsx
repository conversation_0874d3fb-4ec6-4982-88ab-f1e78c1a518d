import SubpageHero from "components/reusable/subpage-hero"
import { Suspense } from "react"
import { getCategoryBySlug } from "@lib/data/categories"
import { listBlogPosts } from "@lib/data/guides"
import PreviousPageBtn from "app/(poradniki)/poradniki/components/previous-page-btn"
import CategoryDetailsClient from "../components/category-details-client"
import { getAttractionsByCategory } from "@lib/data/attractions"
import { AttractionsSearchProvider } from "contexts/AttractionsSearchContext"
import { Metadata } from "next"
import { getBaseURL } from "@lib/util/env"
const FILTER_SLOT_ID = "category-filter-slot"

interface Props {
  params: {
    slug: string
  }
}

export const generateMetadata = async ({
  params,
}: Props): Promise<Metadata> => {
  const { slug } = params
  const categoryDetails = await getCategoryBySlug(slug)

  return {
    title: categoryDetails?.metadata?.seo_title || categoryDetails?.name || "",
    description: categoryDetails?.metadata?.seo_description || "",
    keywords: categoryDetails?.metadata?.seo_keywords || [],
    openGraph: {
      type: "website",
      locale: "pl_PL",
      url: `${getBaseURL()}/kategoria-produktu/${slug}`,
      images: [categoryDetails?.metadata?.image || ""],
      title:
        categoryDetails?.metadata?.seo_title || categoryDetails?.name || "",
      description: categoryDetails?.metadata?.seo_description || "",
    },
    twitter: {
      title:
        categoryDetails?.metadata?.seo_title || categoryDetails?.name || "",
      description: categoryDetails?.metadata?.seo_description || "",
      images: [categoryDetails?.metadata?.image || ""],
    },
  }
}

async function CategoryDetailsPage({ params }: Props) {
  const { slug } = params

  // Fetch initial data on the server
  const [categoryDetails, guides, attractions] = await Promise.all([
    getCategoryBySlug(slug),
    listBlogPosts("", 5),
    getAttractionsByCategory(slug, {
      c: [],
      f: "",
      t: "",
    }),
  ])

  return (
    <div>
      <div className="mb-20">
        <SubpageHero
          imageSrc={categoryDetails?.metadata?.image || ""}
          title={categoryDetails?.name || ""}
        >
          <div id={FILTER_SLOT_ID} />
        </SubpageHero>
      </div>

      <AttractionsSearchProvider>
        <CategoryDetailsClient
          slug={slug}
          filterSlotId={FILTER_SLOT_ID}
          initialGuides={guides}
          initialAttractions={attractions}
        />
      </AttractionsSearchProvider>

      <div className="w-fit mx-auto my-20">
        <PreviousPageBtn />
      </div>
    </div>
  )
}

export default CategoryDetailsPage
