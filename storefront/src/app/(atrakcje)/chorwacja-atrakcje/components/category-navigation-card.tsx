import { StoreProductCategory } from "@medusajs/types"
import Link from "next/link"
import React from "react"
import { useSearchParams } from "next/navigation"
import WithLoadingImage from "components/reusable/with-loading-image"
import { Icons } from "components/reusable/Icons"

type Props = {
  category: StoreProductCategory
}

const CategoryNavigationCard = ({ category }: Props) => {
  const searchParams = useSearchParams()
  const currentQuery = searchParams.toString()
  const queryString = currentQuery ? `?${currentQuery}` : ""

  return (
    <Link
      href={`/kategoria-produktu/${category.metadata?.slug}${queryString}`}
      key={category.id}
    >
      <div className="relative aspect-video min-[420px]:aspect-square sm:aspect-video rounded-xl overflow-hidden">
        {category.metadata?.image ? (
          <WithLoadingImage
            imageSrc={category.metadata.image as string}
            alt={category.name}
            fill
            className="object-cover"
          />
        ) : null}
        <div className="bg-black/40 absolute inset-0"></div>
        <div className="absolute inset-0 p-4 flex h-fit gap-2 justify-between lg:justify-start">
          <h3 className="text-lg lg:text-heading_secondary font-semibold text-white">
            {category.name}
          </h3>
          <Icons.ChevronRight className="bg-secondary rounded-full p-1 w-5 h-5 mt-1" />
        </div>
      </div>
    </Link>
  )
}

export default CategoryNavigationCard
