"use client"

import { useQuery } from "@tanstack/react-query"
import { queryKeys } from "@lib/constants/queryKeys"
import { useCitiesFilter } from "hooks/useCitiesFilter"
import { useAttractionsSearchContext } from "contexts/AttractionsSearchContext"
import AttractionsFilter from "components/reusable/cities-filter"
import CategoryNavigationCard from "./category-navigation-card"
import SearchResults from "components/reusable/search-results"
import { CategoryNavigationSkeletonGrid } from "components/skeletons/category-navigation-skeleton"
import { StoreProductCategory } from "@medusajs/types"
import { createPortal } from "react-dom"
import { useEffect, useState } from "react"
import { listCategories } from "@lib/data/categories"

interface CategoriesClientProps {
  initialCategories: StoreProductCategory[]
  filterSlotId: string
}

export default function CategoriesClient({
  initialCategories,
  filterSlotId,
}: CategoriesClientProps) {
  const {
    selectedCities,
    cities,
    onCityClick,
    handleClearCities,
    dateRange,
    updateDateRangeQuery,
    handleClearAllQueries,
    handleClearDateRange,
  } = useCitiesFilter({})

  const {
    query,
    results: searchResults,
    isLoading: isSearchLoading,
    hasActiveSearch,
    resultsCount,
  } = useAttractionsSearchContext()

  const { data: categories, isLoading } = useQuery({
    queryKey: [queryKeys.CATEGORIES, selectedCities, dateRange],
    queryFn: () =>
      listCategories({
        c: selectedCities,
        f: dateRange.from?.toISOString(),
        t: dateRange.to?.toISOString(),
      }),
    initialData:
      selectedCities || dateRange.from || dateRange.to
        ? undefined
        : initialCategories,
  })

  // Portal setup
  const [filterMounted, setFilterMounted] = useState(false)

  useEffect(() => {
    setFilterMounted(true)
  }, [])

  const noCategoriesFound = categories?.length === 0

  return (
    <>
      {filterMounted &&
        createPortal(
          <AttractionsFilter
            selectedCities={selectedCities}
            cities={cities}
            onCityClick={onCityClick}
            handleClearCities={handleClearCities}
            dateRange={dateRange}
            updateDateRangeQuery={updateDateRangeQuery}
            handleClearAllQueries={handleClearAllQueries}
            handleClearDateRange={handleClearDateRange}
          />,
          document.getElementById(filterSlotId)!
        )}

      {hasActiveSearch ? (
        <SearchResults
          results={searchResults}
          isLoading={isSearchLoading}
          query={query}
          resultsCount={resultsCount}
        />
      ) : (
        <div className="container grid grid-cols-1 min-[420px]:grid-cols-2 gap-4 max-w-screen-xl mx-auto mb-16 mobile-filter-results-margin">
          {isLoading ? (
            <CategoryNavigationSkeletonGrid count={4} />
          ) : noCategoriesFound ? (
            <div className="col-span-full text-center text-gray-50 min-h-[20vh] flex items-center justify-center">
              Brak atrakcji pasujących do Twoich kryteriów
            </div>
          ) : (
            categories?.map((category) => (
              <CategoryNavigationCard key={category.id} category={category} />
            ))
          )}
        </div>
      )}
    </>
  )
}
