import { Suspense } from "react"
import SubpageHero from "components/reusable/subpage-hero"
import { listCategories } from "@lib/data/categories"
import OurSocialMedia from "components/reusable/our-social-media"
import { CategoryNavigationSkeletonGrid } from "components/skeletons/category-navigation-skeleton"
import CategoriesClient from "./components/categories-client"
import { AttractionsSearchProvider } from "contexts/AttractionsSearchContext"
import { Metadata } from "next"
import { getBaseURL } from "@lib/util/env"

const FILTER_SLOT_ID = "categories-filter-slot"

export const metadata: Metadata = {
  title:
    "Chorwacja - rejsy, wycie<PERSON>ki, zipline, rafting, kajaki, łodzie motorowe",
  description:
    "Odkryj niezwykłe atrakcje Chorwacji! Zwiedzaj malownicze miasta, wyspy i parki narodowe, ciesz się relaksem na rejsach po Adriatyku!",
  openGraph: {
    locale: "pl_PL",
    type: "article",
    url: `${getBaseURL()}/chorwacja-atrakcje`,
    title:
      "Chorwacja - rej<PERSON>, wycie<PERSON>ki, zipline, rafting, kajaki, łodzie motorowe",
    description:
      "Odkryj niezwykłe atrakcje Chorwacji! Zwiedzaj malownicze miasta, wyspy i parki narodowe, ciesz się relaksem na rejsach po Adriatyku!",
    siteName: "wakacyjnepomysly.pl",
    images: [
      {
        url: "/images/chorwacja_atrakcje.jpg",
        width: 200,
        height: 2000,
        alt: "Chorwacja - rejsy, wycieczki, zipline, rafting, kajaki, łodzie motorowe",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
  },
  metadataBase: new URL(getBaseURL()),
}

async function CategoriesPage() {
  // Fetch initial categories on the server
  const initialCategories = await listCategories()

  return (
    <section>
      <SubpageHero
        imageSrc="/images/categories_bg.webp"
        title="Najciekawsze atrakcje"
      >
        <div id={FILTER_SLOT_ID} />
      </SubpageHero>

      <AttractionsSearchProvider>
        <Suspense fallback={<CategoryNavigationSkeletonGrid count={4} />}>
          <CategoriesClient
            initialCategories={initialCategories}
            filterSlotId={FILTER_SLOT_ID}
          />
        </Suspense>
      </AttractionsSearchProvider>

      <OurSocialMedia />
    </section>
  )
}

export default CategoriesPage
