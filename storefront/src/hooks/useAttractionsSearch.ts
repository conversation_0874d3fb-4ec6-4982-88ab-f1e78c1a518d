import { useCallback, useMemo } from "react"
import { useQuery } from "@tanstack/react-query"
import { searchAttractions } from "@lib/data/attractions"
import { AttractionShort } from "types/global"
import { useDebounce } from "@uidotdev/usehooks"
import { useQueryState } from "nuqs"
import { queryKeys } from "@lib/constants/queryKeys"

interface UseAttractionsSearchReturn {
  query: string
  setQuery: (query: string) => void
  results: AttractionShort[] | null
  isLoading: boolean
  error: string | null
  clearSearch: () => void
  hasActiveSearch: boolean
  resultsCount: number
}

export const useAttractionsSearch = (): UseAttractionsSearchReturn => {
  const [query, setQuery] = useQueryState("q", {
    defaultValue: "",
  })

  const debouncedQuery = useDebounce(query, 300)

  const shouldSearch = debouncedQuery.length >= 2

  const {
    data: searchData,
    isLoading,
    error,
  } = useQuery({
    queryKey: [queryKeys.ATTRACTIONS_SEARCH, debouncedQuery],
    queryFn: () => searchAttractions(debouncedQuery),
    enabled: shouldSearch,
    staleTime: 3 * 60 * 1000, // 3 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
  })

  const clearSearch = useCallback(() => {
    setQuery("")
  }, [])

  const results = useMemo(() => {
    if (!shouldSearch || !searchData) return null
    return searchData.tours || []
  }, [shouldSearch, searchData])

  const hasActiveSearch = useMemo(() => {
    return debouncedQuery.length >= 2
  }, [debouncedQuery])

  const resultsCount = useMemo(() => {
    return results?.length || 0
  }, [results])

  const errorMessage = useMemo(() => {
    if (error) {
      return error instanceof Error
        ? error.message
        : "An error occurred while searching"
    }
    return null
  }, [error])

  return {
    query,
    setQuery,
    results,
    isLoading: isLoading && hasActiveSearch,
    error: errorMessage,
    clearSearch,
    hasActiveSearch,
    resultsCount,
  }
}
