import React, { useEffect } from "react"

export const KEYS = {
  REDIRECT_TO_TPAY_PAYMENT_GATEWAY: "redirect_to_tpay_payment_gateway",
}

const useFlagSessionStorage = (key: string) => {
  const [flag, setFlag] = React.useState(false)

  useEffect(() => {
    const flag = sessionStorage.getItem(key)
    if (flag) {
      setFlag(flag === "true")
    }
  }, [])

  const handleSetFlag = () => {
    sessionStorage.setItem(key, "true")
    setFlag(true)
  }

  const handleClearFlag = () => {
    sessionStorage.removeItem(key)
    setFlag(false)
  }

  return { flag, setFlag: handleSetFlag, clearFlag: handleClearFlag }
}

export default useFlagSessionStorage
