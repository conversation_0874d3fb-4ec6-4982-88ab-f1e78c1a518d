"use client"

import { sdk } from "@lib/config"
import { queryKeys } from "@lib/constants/queryKeys"
import { retrieveCart } from "@lib/data/cart"
import { useMutation, useQuery } from "@tanstack/react-query"
import { useEffect, useRef } from "react"
import { useState } from "react"
import { ExtendedCart } from "types/cart"

const useCart = () => {
  const [cartId, setCartId] = useState<string | null>(null)

  const [cart, setCart] = useState<ExtendedCart | null | "fetching">("fetching")

  useEffect(() => {
    const cartIdFromLocalStorage = localStorage.getItem("cart_id")
    setCartId(cartIdFromLocalStorage)
  }, [])

  const { data, isLoading, refetch, error, isSuccess } = useQuery({
    queryKey: [queryKeys.CART, cartId],
    queryFn: () =>
      retrieveCart(cartId!, {
        fields: "*items.product.tour, *items.product.tour.pricing",
      }),
  })

  const { mutate: deactivateCart } = useMutation({
    mutationFn: () =>
      sdk.store.cart.update(cartId!, {
        metadata: {
          deactivated_at: new Date().toISOString(),
        },
      }),
    onSuccess: () => {
      clearCart()
    },
    onError: (error: unknown) => {
      // Medusa returns Error if one of cart's items does not have price but for its aligned with the shop design
      clearCart()
      console.error("Wystąpił błąd podczas usuwania koszyka", error)
    },
  })

  useEffect(() => {
    if (data && isSuccess) {
      setCart(data as ExtendedCart)
    } else if (!data && isSuccess) {
      setCart(null)
    }
  }, [data, isSuccess])

  const isCartFull = Boolean(
    isCartFullGuard(cart) && cart?.metadata?.attraction_id
  )

  const clearCart = () => {
    localStorage.removeItem("cart_id")
    setCartId(null)
    setCart(null)
  }

  return {
    cart,
    cartId,
    isLoading,
    error,
    isCartFull,
    clearCart,
    refetch,
    setCartId,
    deactivateCart,
  }
}

export default useCart

function isCartFullGuard(
  cart: ExtendedCart | null | "fetching"
): cart is ExtendedCart {
  return Boolean(cart && typeof cart === "object" && "items" in cart)
}
