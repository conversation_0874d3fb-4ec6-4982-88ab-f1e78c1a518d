import { useEffect, useState } from "react"
import { addHours } from "date-fns"
import { StartTimes, TourBlockedStartTimes } from "types/global"
import useStoreSettings from "./useStoreSettings"
import useAvailableTimeSlots from "./useAvailableTimeSlots"

interface UseStartTimesProps {
  selectedDate?: Date
  startTimes: StartTimes | null
  disabledStartTimes?: TourBlockedStartTimes[]
}

interface StartTimeOption {
  time: string
  isDisabled: boolean
}

export default function useStartTimes({
  selectedDate,
  startTimes,
  disabledStartTimes,
}: UseStartTimesProps) {
  const [selectedStartTime, setSelectedStartTime] = useState<
    string | undefined
  >(undefined)
  const [availableStartTimes, setAvailableStartTimes] = useState<
    StartTimeOption[]
  >([])
  const { storeSettings } = useStoreSettings()

  // Update available start times when selected date changes
  useEffect(() => {
    if (!selectedDate || !startTimes) {
      setAvailableStartTimes([])
      setSelectedStartTime(undefined)
      return
    }

    const generateTimeSlots = (
      start: string,
      end: string,
      intervalMinutes: number
    ) => {
      const slots: string[] = []
      const [startHour, startMinute] = start.split(":").map(Number)
      const [endHour, endMinute] = end.split(":").map(Number)

      if (startTimes.use_manual_start_times) {
        return startTimes.manual_start_times
      }

      const startTime = startHour * 60 + startMinute
      const endTime = endHour * 60 + endMinute

      for (let time = startTime; time <= endTime; time += intervalMinutes) {
        const hour = Math.floor(time / 60)
        const minute = time % 60
        slots.push(
          `${hour.toString().padStart(2, "0")}:${minute
            .toString()
            .padStart(2, "0")}`
        )
      }

      return slots
    }

    const timeSlots = generateTimeSlots(
      startTimes.start,
      startTimes.stop,
      startTimes.interval
    )

    // Calculate the earliest available time based on handicap hours
    const now = new Date()
    const selectedDateIsToday =
      selectedDate.getDate() === now.getDate() &&
      selectedDate.getMonth() === now.getMonth() &&
      selectedDate.getFullYear() === now.getFullYear()

    const handicapHours =
      storeSettings?.metadata?.global_amount_of_hours_calendar_handicap || 0
    const earliestAvailableTime = selectedDateIsToday
      ? addHours(now, handicapHours)
      : null

    // Filter out times that are within the handicap period
    const availableTimes = (timeSlots ?? []).map((time) => {
      const [hour, minute] = time.split(":").map(Number)
      const timeDate = new Date(selectedDate)
      timeDate.setHours(hour, minute, 0, 0)

      const isDisabled = Boolean(
        selectedDateIsToday &&
          earliestAvailableTime &&
          timeDate <= earliestAvailableTime
      )

      if (startTimes.use_manual_start_times) {
        return {
          time,
          isDisabled: !startTimes.manual_start_times?.includes(time),
        }
      }

      return {
        time,
        isDisabled,
      }
    })

    setAvailableStartTimes(availableTimes)

    // Clear selected start time if it's no longer available or disabled
    if (selectedStartTime) {
      const selectedTimeObj = availableTimes.find(
        (t) => t.time === selectedStartTime
      )
      if (selectedTimeObj?.isDisabled) {
        setSelectedStartTime(undefined)
      }
    }
  }, [
    selectedDate,
    startTimes,
    selectedStartTime,
    storeSettings?.metadata?.global_amount_of_hours_calendar_handicap,
  ])

  const handleChangeStartTime = (time: string) => {
    setSelectedStartTime(time)
  }

  const sortedAscending = availableStartTimes?.sort((a, b) => {
    const [aHour, aMinute] = a.time.split(":").map(Number)
    const [bHour, bMinute] = b.time.split(":").map(Number)
    return aHour * 60 + aMinute - (bHour * 60 + bMinute)
  })

  // Use the hook to get filtered time slots
  const filteredTimeSlots = useAvailableTimeSlots({
    availableTimeSlots: sortedAscending,
    selectedDate,
    disabledStartTimes,
  })

  // Clear selected start time if it's not available in the final filtered list or becomes disabled
  useEffect(() => {
    if (selectedStartTime && filteredTimeSlots.length > 0) {
      const selectedTimeInFiltered = filteredTimeSlots.find(
        (slot) => slot.time === selectedStartTime
      )
      
      // Clear if time is not found or is disabled in the filtered list
      if (!selectedTimeInFiltered || selectedTimeInFiltered.isDisabled) {
        setSelectedStartTime(undefined)
      }
    }
  }, [selectedStartTime, filteredTimeSlots])

  return {
    selectedStartTime,
    availableStartTimes: filteredTimeSlots,
    handleChangeStartTime,
    setSelectedStartTime,
  }
}
