import { useMemo } from "react"
import { TimeSlot } from "../components/reusable/time-slot-picker"
import { TourBlockedStartTimes } from "types/global"

interface UseAvailableTimeSlotsProps {
  availableTimeSlots: TimeSlot[]
  selectedDate?: Date
  disabledStartTimes?: TourBlockedStartTimes[]
  disabledMessage?: string
}

/**
 * A hook to filter available time slots based on disabled start times for the selected date
 * This handles both base availability and specific date-based disabling of time slots
 */
export default function useAvailableTimeSlots({
  availableTimeSlots,
  selectedDate,
  disabledStartTimes = [],
  disabledMessage = "niedostępne w tym dniu",
}: UseAvailableTimeSlotsProps) {
  // Filter availableTimeSlots based on disabledStartTimes for the selectedDate
  const filteredTimeSlots = useMemo(() => {
    if (!selectedDate || !disabledStartTimes.length) {
      return availableTimeSlots
    }

    return availableTimeSlots.map((timeSlot) => {
      // Check if this time is disabled for the selected date
      const isDisabledForDate = disabledStartTimes.some((disabledTime) => {
        // Check if the month and day match
        const currentMonth = selectedDate.getMonth() + 1 // JavaScript months are 0-indexed
        const currentDay = selectedDate.getDate()

        // Check if the month matches
        if (disabledTime.month !== currentMonth) {
          return false
        }

        // Check if the day is included in the comma-separated days list
        const blockedDays = disabledTime.days
          .split(",")
          .map((day) => parseInt(day.trim(), 10))
        const isDayBlocked = blockedDays.includes(currentDay)

        // If the day is blocked and the time is in the blocked times list
        // Cast the timeSlot.time to any to avoid TypeScript error with the restricted TimeString type
        return (
          isDayBlocked &&
          disabledTime.blocked_times.includes(timeSlot.time as any)
        )
      })

      // If the time is already disabled or is disabled for this date
      if (timeSlot.isDisabled || isDisabledForDate) {
        return {
          ...timeSlot,
          isDisabled: true,
          disabledReason: isDisabledForDate
            ? disabledMessage
            : timeSlot.disabledReason,
        }
      }

      return timeSlot
    })
  }, [availableTimeSlots, selectedDate, disabledStartTimes, disabledMessage])

  return filteredTimeSlots
}
