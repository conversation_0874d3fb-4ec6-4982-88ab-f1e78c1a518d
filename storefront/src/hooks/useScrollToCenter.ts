"use client"

import { useCallback } from "react"
import { useInView } from "react-intersection-observer"

interface UseScrollToCenterOptions {
  targetId: string
  navbarHeight: number
}

export const useScrollToCenter = ({
  targetId,
  navbarHeight,
}: UseScrollToCenterOptions) => {
  const { ref, inView } = useInView({
    threshold: 0.7,
    rootMargin: `400px 0px 0px 0px`,
  })

  // Set the ref on the target element
  const setTargetRef = useCallback(
    (element: HTMLElement | null) => {
      if (element && element.id === targetId) {
        ref(element)
      }
    },
    [ref, targetId]
  )

  const scrollToCenter = useCallback(() => {
    const targetElement = document.getElementById(targetId)
    if (!targetElement) return

    const elementRect = targetElement.getBoundingClientRect()
    const elementTop = elementRect.top + window.pageYOffset
    const elementHeight = elementRect.height
    const windowHeight = window.innerHeight

    // Calculate scroll position to center the element with navbar offset
    const scrollTo =
      elementTop - (windowHeight - elementHeight) / 2 + navbarHeight / 2

    window.scrollTo({
      top: scrollTo,
      behavior: "smooth",
    })
  }, [targetId, navbarHeight])

  return {
    isTargetVisible: inView,
    scrollToCenter,
    setTargetRef,
  }
}
