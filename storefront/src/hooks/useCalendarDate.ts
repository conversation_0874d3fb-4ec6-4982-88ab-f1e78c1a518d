import { useState } from "react"
import {
  AttractionBlockedDates,
  AttractionBlockedDatesByMonth,
} from "types/global"

interface UseCalendarDateProps {
  blockedDates?: AttractionBlockedDates[]
  blockedDatesByMonth?: AttractionBlockedDatesByMonth[]
}

export default function useCalendarDate({
  blockedDates = [],
  blockedDatesByMonth = [],
}: UseCalendarDateProps) {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined)

  const handleDateChange = (date: Date | undefined) => {
    setSelectedDate(date || undefined)
  }

  return {
    selectedDate,
    setSelectedDate: handleDateChange,
  }
}
