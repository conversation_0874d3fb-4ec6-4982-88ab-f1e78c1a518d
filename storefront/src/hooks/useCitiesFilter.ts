import { useQuery } from "@tanstack/react-query"
import { getCities, getCitiesByCategorySlug } from "@lib/data/cities"
import { useQueryState } from "nuqs"
import { useDeferredValue, useMemo } from "react"
import { queryKeys } from "@lib/constants/queryKeys"
import { DateRange } from "react-day-picker"
import { parse, isValid } from "date-fns"

import { dateUtils } from "@lib/util/date-format"

type Props = {
  slugToNarrowCities?: string
}

export const useCitiesFilter = ({ slugToNarrowCities }: Props) => {
  const [citiesQuery, setCitiesQuery] = useQueryState("c")

  const [from, setFrom] = useQueryState("f")
  const [to, setTo] = useQueryState("t")

  const dateRange = useMemo(() => {
    const parseDate = (dateString: string | null | undefined) => {
      if (!dateString) return undefined;

      // Try parsing with the new dd-MM-yyyy format
      const parsedNewFormat = parse(dateString, "dd-MM-yyyy", new Date());
      if (isValid(parsedNewFormat)) {
        return parsedNewFormat;
      }

      // Fallback to parsing as ISO string (old format)
      const parsedOldFormat = new Date(dateString);
      if (isValid(parsedOldFormat)) {
        return parsedOldFormat;
      }

      return undefined;
    };

    return {
      from: parseDate(from),
      to: parseDate(to),
    };
  }, [from, to]);

  const citiesQueryArray = citiesQuery ? citiesQuery?.split(",") : null

  const deferredCities = useDeferredValue(citiesQueryArray)

  const { data: cities } = useQuery({
    queryKey: [queryKeys.CITIES],
    queryFn: () =>
      slugToNarrowCities
        ? getCitiesByCategorySlug(slugToNarrowCities)
        : getCities(),
  })

  const handleCityClick = (city: string) => {
    if (citiesQuery?.includes(city)) {
      setCitiesQuery(
        citiesQuery
          ?.split(",")
          .filter((c) => c !== city)
          .join(",")
      )
    } else {
      setCitiesQuery(citiesQuery ? `${citiesQuery},${city}` : city)
    }
  }

  const handleClearCities = () => {
    setCitiesQuery(null)
  }

  const handleClearDateRange = () => {
    setFrom(null)
    setTo(null)
  }

  const citiesAlphabeticallySorted = cities?.cities.sort((a, b) =>
    a.name.localeCompare(b.name)
  )

  const handleClearAllQueries = () => {
    setCitiesQuery(null)
    setFrom(null)
    setTo(null)
  }

  const updateDateRangeQuery = (dateRange: DateRange) => {
    setFrom(dateRange.from ? dateUtils.formatDateToUrl(dateRange.from) : null)
    setTo(dateRange.to ? dateUtils.formatDateToUrl(dateRange.to) : null)
  }

  return {
    selectedCities: citiesQueryArray,
    cities: citiesAlphabeticallySorted || [],
    onCityClick: handleCityClick,
    deferredCities,
    handleClearCities,
    handleClearAllQueries,
    updateDateRangeQuery,
    dateRange,
    handleClearDateRange,
  }
}
