import { useState, useEffect } from "react"

/**
 * Hook to detect if the current viewport matches a mobile media query.
 * Handles SSR hydration safely.
 * @param {number} mobileBreakpoint - Width threshold in pixels (default: 768px).
 *                                    The hook checks if width is LESS THAN this value.
 * @returns {boolean} - True if viewport width is below the mobile breakpoint.
 */
const useMobile = (mobileBreakpoint = 768): boolean => {
  // 1. Initialize state to a default value (e.g., false) to prevent hydration mismatch.
  //    The true value will be determined client-side after mounting.
  const [isMobile, setIsMobile] = useState<boolean>(false)

  useEffect(() => {
    // 2. Check if window is defined (runs only on client).
    if (typeof window === "undefined") {
      return
    }

    // 3. Use matchMedia for efficient breakpoint checking.
    //    Note: `max-width` is inclusive, but the convention is often "mobile is *below* X".
    //    So, if breakpoint is 768, we check for max-width: 767px.
    const mediaQuery = window.matchMedia(
      `(max-width: ${mobileBreakpoint - 1}px)`
    )

    // 4. Define the function to update state based on matchMedia result.
    const handleMediaChange = (event: MediaQueryListEvent | MediaQueryList) => {
      setIsMobile(event.matches)
    }

    // 5. Run the check once initially on mount.
    handleMediaChange(mediaQuery)

    // 6. Add listener for changes in the media query state.
    //    Use `addEventListener` which is the modern standard.
    //    Use try/catch for potentially older browsers that only support `addListener`.
    try {
      mediaQuery.addEventListener("change", handleMediaChange)
    } catch (e) {
      // Fallback for older browsers
      try {
        mediaQuery.addListener(handleMediaChange)
      } catch (e2) {
        console.error("useMobile: Error adding media query listener", e2)
      }
    }

    // 7. Cleanup listener on unmount.
    return () => {
      try {
        mediaQuery.removeEventListener("change", handleMediaChange)
      } catch (e) {
        // Fallback for older browsers
        try {
          mediaQuery.removeListener(handleMediaChange)
        } catch (e2) {
          console.error("useMobile: Error removing media query listener", e2)
        }
      }
    }

    // 8. Re-run effect only if mobileBreakpoint changes.
  }, [mobileBreakpoint])

  return isMobile
}

export default useMobile
