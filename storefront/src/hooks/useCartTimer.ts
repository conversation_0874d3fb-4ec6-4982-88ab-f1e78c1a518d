import { useEffect, useState } from "react"
import { useCartContext } from "context/cart-context"

const CART_TIMEOUT_MINUTES = 15
const MILLISECONDS_IN_MINUTE = 60000

export type CartTimerState = {
  timeLeft: number | null
  hasExpired: boolean
  isExpiredModalOpen: boolean
  setIsExpiredModalOpen: (isOpen: boolean) => void
  formatTime: (ms: number) => string
}

export const useCartTimer = (): CartTimerState => {
  const { cart, deactivateCart } = useCartContext()
  const [timeLeft, setTimeLeft] = useState<number | null>(null)
  const [hasExpired, setHasExpired] = useState(false)
  const [isExpiredModalOpen, setIsExpiredModalOpen] = useState(false)
  const [lastProcessedCartId, setLastProcessedCartId] = useState<string | null>(
    null
  )

  useEffect(() => {
    if (!cart || cart === "fetching" || !cart.created_at) {
      return
    }

    if (cart.id !== lastProcessedCartId) {
      setHasExpired(false)
      setLastProcessedCartId(cart.id)
    }

    const calculateTimeLeft = () => {
      if (!cart.created_at) return 0

      const createdAtDate =
        typeof cart.created_at === "string"
          ? new Date(cart.created_at)
          : cart.created_at
      const createdAt = createdAtDate.getTime()
      const now = new Date().getTime()
      const timeoutTime =
        createdAt + CART_TIMEOUT_MINUTES * MILLISECONDS_IN_MINUTE
      return Math.max(0, timeoutTime - now)
    }

    const initialTimeLeft = calculateTimeLeft()
    setTimeLeft(initialTimeLeft)

    if (initialTimeLeft <= 0 && !hasExpired) {
      handleExpiredCart()
      return
    }

    const timer = setInterval(() => {
      const remaining = calculateTimeLeft()
      setTimeLeft(remaining)

      if (remaining <= 0 && !hasExpired) {
        handleExpiredCart()
        clearInterval(timer)
      }
    }, 1000)

    return () => clearInterval(timer)
  }, [cart, deactivateCart, hasExpired, lastProcessedCartId])

  const handleExpiredCart = () => {
    setHasExpired(true)
    setIsExpiredModalOpen(true)
    deactivateCart()
  }

  const formatTime = (ms: number) => {
    const minutes = Math.floor(ms / (60 * 1000))
    const seconds = Math.floor((ms % (60 * 1000)) / 1000)
    return `${minutes}:${seconds.toString().padStart(2, "0")}`
  }

  return {
    timeLeft,
    hasExpired,
    isExpiredModalOpen,
    setIsExpiredModalOpen,
    formatTime,
  }
}

export default useCartTimer
