import { queryKeys } from "@lib/constants/queryKeys"
import { getStoreSettings } from "@lib/data/settings"
import { useQuery } from "@tanstack/react-query"

const useStoreSettings = () => {
  const {
    data: storeSettings,
    isLoading,
    error,
  } = useQuery({
    queryKey: [queryKeys.STORE_SETTINGS],
    queryFn: getStoreSettings,
  })

  return { storeSettings, isLoading, error }
}

export default useStoreSettings
