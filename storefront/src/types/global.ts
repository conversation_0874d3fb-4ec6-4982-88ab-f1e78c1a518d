import { StorePrice } from "@medusajs/types"
import { FoodOption, Variant } from "./cart"

export type Attraction = {
  id: string
  name: string
  description: string
  image: string
  slug: string
  tags: string[]
  created_at: string
  is_published: boolean
}

export enum ProductPricingVariant {
  DYNAMIC_AGE_GROUPS = "dynamic_age_groups",
  REGULAR_VARIANTS = "regular_variants",
  BOAT_RENTAL = "boat_rental",
}
export type ContentBlocks = {
  id: string
  description: string
  calendar: string
  price: string
  how_to_book: string
  program: string
  ticket: string
  specification_only_for_boats?: string
  faq: Array<{
    question: string
    answer: string
    index: number
  }>
}

export type Pricing = {
  id: string
  base_price: number
  discount_price: number
  discount_active_from: string
  discount_active_to: string
}

export type TourSEO = {
  id: string
  title: string
  description: string
  keywords: string[]
}

export type AttractionBlockedDates = {
  date_from: string
  date_to: string
  id: string
  only_one_day: boolean
  tour_id_id: string
}

export type AttractionBlockedDatesByMonth = {
  days: string
  month: number
  tour_id_id: string
}

export type StartPlacesByDate = {
  id: string
  place: string
  tour_id: string
  month: number
  // comma separated days eg: 1,2,3
  days: string
}

export type StartTimes = {
  interval: number
  // like 8:30
  start: string
  // like 16:30
  stop: string
  use_manual_start_times: boolean
  manual_start_times: string[] | null
}

export type RecommendedTour = {
  created_at: string
  id: string
  recommended_tour_id: string
  tour_id_id: string
}

export type AttractionAvailableStartPlaces = {
  id: string
  place: string
  tour_id: string
  rich_text_content: string
}

export type ShortAttraction = {
  id: string
  name: string
  featured_image: string
  slug: string
}

export type AttractionDetails = {
  id: string
  name: string
  description: string
  featured_image: string
  blocked_dates: AttractionBlockedDates[]
  blocked_dates_by_month: AttractionBlockedDatesByMonth[]
  at_least_one_food_option_required: boolean
  cart_section_image: string | null
  slug: string
  price_from?: number
  dates_from_to?: string
  hourly_length?: number
  product_pricing_variant: ProductPricingVariant
  content_blocks: ContentBlocks
  created_at: string
  deleted_at: string | null
  food_options: FoodOption[]
  available_start_places: AttractionAvailableStartPlaces[]
  start_places_by_date: StartPlacesByDate[]
  gallery: string[]
  is_active: boolean
  organizator_mail: string
  pricing: Pricing
  start_times: StartTimes | null
  cities: {
    id: string
    name: string
  }[]
  is_recommended: boolean
  is_bargain: boolean
  is_promotion: boolean
  promotion_price_from?: string

  product: {
    id: string
    title: string
    handle: string
    subtitle: string | null
    description: string
    variants: (Variant & {
      metadata?: {
        age_group_name?: string
        start_date?: string
        end_date?: string
      }
      prices: { amount: number }[]
    })[]
  }
  tour_seo: TourSEO
  recommended_tour_ids: string[]
  updated_at: string
  blocked_start_times: TourBlockedStartTimes[]
}

type Digit = "0" | "1" | "2" | "3" | "4" | "5" | "6" | "7" | "8" | "9"
type TimeString = `${Digit}${Digit}:${Digit}${Digit}`

export type TourBlockedStartTimes = {
  id: string
  start_time: string
  tour_id: string
  // eg: ["10:00", "11:00"]
  blocked_times: TimeString[]
  // comma separated days eg: 1,2,3
  days: string
  month: number
}

export type BaseGuidePost = {
  id: string
  name: string
  featured_image: string
  meta_description: string
  slug: string
  related_guides: string[]
  seo_title: string
  og_title: string
  tags: string[]
  content: string
  created_at: string
  is_published: boolean
  preview_image: string
}

export type GetGuidesResult = BaseGuidePost[]

export type GetGuideDetailsResult = BaseGuidePost & {}

export type FeaturedProduct = {
  id: string
  title: string
  handle: string
  thumbnail?: string
}

export type VariantPrice = {
  calculated_price_number: number
  calculated_price: string
  original_price_number: number
  original_price: string
  currency_code: string
  price_type: string
  percentage_diff: string
}

export type StoreFreeShippingPrice = StorePrice & {
  target_reached: boolean
  target_remaining: number
  remaining_percentage: number
}

export type AttractionShort = {
  cities: {
    id: string
    name: string
  }[]
  description: string
  featured_image: string
  id: string
  name: string
  slug: string
  position: number
  blocked_dates: AttractionBlockedDates[]
  blocked_dates_by_month: AttractionBlockedDatesByMonth[]
  is_recommended: boolean
  is_bargain: boolean
  is_promotion: boolean
  promotion_price_from?: string
  price_from?: string
  dates_from_to?: string
  hourly_length?: string
}

export type AttractionsByCategoryResult = {
  attractions: AttractionShort[]
  categoryDetails: {
    name: string
    description: string
    handle: string
    image: string
  }
}
