import {
  StoreCart,
  StoreCartLineItem,
} from "@medusajs/types/dist/http/cart/store"
import {
  BaseProduct,
  BaseProductVariant,
} from "@medusajs/types/dist/http/product/common"
import { AttractionAvailableStartPlaces, ProductPricingVariant } from "./global"

export type Variant = BaseProductVariant & {
  title: string
  metadata?: {
    age_group_name?: string
    start_date?: string
    end_date?: string
  }
}

export type CartTourPricing = {
  id: string
  tour_id_id: string
  prepaid_percentage: number
}

export type CartTour = {
  id: string
  name: string
  slug: string
  product_pricing_variant: ProductPricingVariant
  description: string
  gallery: string[]
  featured_image: string
  pricing: CartTourPricing
}

export type CartItem = StoreCartLineItem & {
  product: BaseProduct & {
    tour: CartTour
  }
}

export type ExtendedCart = Omit<StoreCart, "items"> & {
  items: CartItem[]
  metadata: {
    start_place?: AttractionAvailableStartPlaces
    selected_date?: string
    date_from?: string
    date_to?: string
    start_time?: string
    selected_food_options?: FoodOption[]
    attraction_id?: string
    attraction_name?: string
    product_type?: ProductPricingVariant
    variant_id?: string
  }
}

export type FoodOption = {
  id: string
  name: string
  price: number
  quantity: number
  required?: boolean
}
export type AgeGroup = {
  name: string
  defaultVariant: Variant
  activeVariant: Variant
  seasonalVariants: Variant[]
  count: number
}

export type DynamicGroupGetCartPayloadResult = {
  selectedDate: Date | undefined
  selectedGroups: AgeGroup[]
  foodOptions: FoodOption[]
  startPlace: AttractionAvailableStartPlaces | undefined
  startTime: string | undefined
}

export type BoatRentalGetCartPayloadResult = {
  selectedDate: {
    from: Date | undefined
    to: Date | undefined
  }
  variants: Variant[]
  startTime: string | undefined
}

export type RegularSimplifiedProductGetCartPayloadResult = {
  variant: Variant
  selectedDate?: Date | undefined
  startTime?: string | undefined
}

export type ExchangeRate = {
  id: string
  currency_code: string
  rate: number
  created_at: string
  updated_at: string
}
