import LZString from "lz-string"

export const parseDateString = (dateString: string) => {
  const isValidDate = !isNaN(new Date(dateString).getTime())

  if (!isValidDate) {
    return null
  }

  return new Date(dateString).toLocaleString("pl-PL", {
    timeZone: "Europe/Warsaw",
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  })
}

export const getHashedState = <T>(state: T) => {
  const serializedState = JSON.stringify(state)
  const compressedState =
    LZString.compressToEncodedURIComponent(serializedState)

  return compressedState
}

export const getStateFromUrl = <T>(encodedState: string): T | null => {
  if (encodedState) {
    const decodedState =
      LZString.decompressFromEncodedURIComponent(encodedState)
    return JSON.parse(decodedState)
  }
  return null
}

export const sortByAgeGroup = <T extends { title: string }>(array: T[]) => {
  // Create a function to determine the min age from the name
  const getMinAge = (name: string) => {
    // Extract the first number found in the string
    const match = name.match(/\d+/g)
    if (!match) return -1 // Use -1 for items with no numbers
    return parseInt(match[0])
  }

  // Sort the array based on the min age in each name
  return array.sort((a, b) => {
    const ageA = getMinAge(a.title)
    const ageB = getMinAge(b.title)

    // If either item has no number, move it to the end
    if (ageA === -1 && ageB === -1) return 0
    if (ageA === -1) return 1 // Move 'a' to the end
    if (ageB === -1) return -1 // Move 'b' to the end

    // Sort by descending order (older ages first)
    return ageB - ageA // This puts higher numbers (older age groups) first
  })
}
