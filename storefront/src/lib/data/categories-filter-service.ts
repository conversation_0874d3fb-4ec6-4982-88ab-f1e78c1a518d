import { HttpTypes } from "@medusajs/types"
import {
  AttractionDetails,
  AttractionBlockedDates,
  AttractionBlockedDatesByMonth,
  AttractionShort,
} from "types/global"

/**
 * Checks if a date is blocked by any of the blocked dates
 */
const isDateBlockedByFullDates = (
  date: Date,
  blockedDates: AttractionBlockedDates[]
): boolean => {
  return blockedDates.some((blockedDate) => {
    const fromDate = new Date(blockedDate.date_from)
    const toDate = new Date(blockedDate.date_to)

    fromDate.setHours(0, 0, 0, 0)
    toDate.setHours(23, 59, 59, 999)

    return date >= fromDate && date <= toDate
  })
}

/**
 * Checks if a date is blocked by the monthly blocked dates configuration
 */
const isDateBlockedByMonth = (
  date: Date,
  blockedDatesByMonth: AttractionBlockedDatesByMonth[]
): boolean => {
  return blockedDatesByMonth.some((blocked) => {
    if (date.getMonth() !== blocked.month - 1) return false

    const blockedDays = blocked.days
      .split(",")
      .map((day) => parseInt(day.trim(), 10))

    return blockedDays.includes(date.getDate())
  })
}

/**
 * Checks if a date falls within the yearly disabled periods:
 * - January 1st to March 31st
 * - April 1st
 * - November 1st to December 31st
 */
const isInYearlyDisabledPeriod = (date: Date): boolean => {
  const month = date.getMonth() // 0-indexed (0 = January, 11 = December)
  const day = date.getDate() // Day of the month (1-31)

  // January (0) to March (2) OR November (10) to December (11)
  // OR April (3) and day is 1 (April 1st)
  return (
    (month >= 0 && month <= 2) ||
    (month >= 10 && month <= 11) ||
    (month === 3 && day === 1)
  )
}

/**
 * Checks if a specific date is available for booking a tour
 * This is the core logic used by both filter functions
 */
const isDateAvailableForTour = (
  date: Date,
  tour: {
    blocked_dates?: AttractionBlockedDates[]
    blocked_dates_by_month?: AttractionBlockedDatesByMonth[]
  }
): boolean => {
  // Clone date to avoid modifying original
  const checkDate = new Date(date)
  checkDate.setHours(12, 0, 0, 0) // Set to noon to avoid timezone issues

  // Extract blocked dates info
  const blockedDates = tour.blocked_dates || []
  const blockedDatesByMonth = tour.blocked_dates_by_month || []

  // Check if date is blocked by any mechanism
  return (
    !isDateBlockedByFullDates(checkDate, blockedDates) &&
    !isDateBlockedByMonth(checkDate, blockedDatesByMonth) &&
    !isInYearlyDisabledPeriod(checkDate)
  )
}

/**
 * Normalizes and validates date inputs
 * Returns [fromDateObj, toDateObj, isValid]
 */
const normalizeDateInputs = (
  fromDate?: string,
  toDate?: string
): [Date | null, Date | null, boolean] => {
  // If no dates provided, return null dates but valid status
  if (!fromDate || !toDate) return [null, null, true]

  // Parse dates
  const fromDateObj = fromDate ? new Date(fromDate) : null
  const toDateObj = toDate ? new Date(toDate) : null

  // Check if dates are valid
  const isValid = Boolean(
    fromDateObj &&
      toDateObj &&
      !isNaN(fromDateObj.getTime()) &&
      !isNaN(toDateObj.getTime())
  )

  return [fromDateObj, toDateObj, isValid]
}

/**
 * Checks if a tour is available for at least one day in the given date range
 * This is used by both filter functions
 */
const isTourAvailableBetweenDates = (
  fromDate: Date | null,
  toDate: Date | null,
  tour: {
    blocked_dates?: AttractionBlockedDates[]
    blocked_dates_by_month?: AttractionBlockedDatesByMonth[]
  }
): boolean => {
  // If no dates are provided, consider the tour available
  if (!fromDate || !toDate) return true

  // Clone dates to avoid modifying originals
  const startDate = new Date(fromDate)
  const endDate = new Date(toDate)

  // Set times to start and end of day
  startDate.setHours(0, 0, 0, 0)
  endDate.setHours(23, 59, 59, 999)

  // For each day in the range, check if at least one is available
  const currentDate = new Date(startDate)

  while (currentDate <= endDate) {
    const isAvailable = isDateAvailableForTour(currentDate, tour)

    if (isAvailable) {
      return true
    }
    // Move to next day
    currentDate.setDate(currentDate.getDate() + 1)
  }

  // No available days found in the range
  return false
}

/**
 * Checks if a tour serves a specific city
 */
const isTourAvailableInCity = (
  tour: { cities?: { name: string }[] },
  selectedCities?: string[]
): boolean => {
  // If no cities are selected, consider all cities available
  if (!selectedCities || selectedCities.length === 0) return true

  // If the tour has no cities, it's not available anywhere
  if (!tour.cities || tour.cities.length === 0) return false

  // Check if any of the tour's cities match any of the selected cities
  return tour.cities.some((city) =>
    selectedCities.some(
      (selectedCity) => city.name.toLowerCase() === selectedCity.toLowerCase()
    )
  )
}

/**
 * Filters product categories to only include those with at least one tour
 * that's available during the specified date range and in the selected cities.
 */
export const filterCategoriesWithAvailableTours = (
  categories: HttpTypes.StoreProductCategory[],
  fromDate?: string,
  toDate?: string,
  selectedCities?: string[]
): HttpTypes.StoreProductCategory[] => {
  // Process and validate dates
  const [fromDateObj, toDateObj, isValid] = normalizeDateInputs(
    fromDate,
    toDate
  )

  const filteredCategories = categories.filter((category) => {
    // Check if any product in the category is available
    const hasAvailableProducts =
      category.products?.some((product) => {
        const tour = (product as unknown as { tour: AttractionDetails })?.tour
        if (!tour) return false

        // Always check city availability if cities are selected
        if (!isTourAvailableInCity(tour, selectedCities)) {
          return false
        }

        // Check date availability only if valid dates are provided

        if (isValid && fromDateObj && toDateObj) {
          // TIMEZONE FIX: Add 2 hours to account for server timezone difference
          const adjustedFromDate = new Date(
            fromDateObj.getTime() + 2 * 60 * 60 * 1000
          )
          const adjustedToDate = new Date(
            toDateObj.getTime() + 2 * 60 * 60 * 1000
          )

          const isAvailable = isTourAvailableBetweenDates(
            adjustedFromDate,
            adjustedToDate,
            {
              blocked_dates: tour.blocked_dates,
              blocked_dates_by_month: tour.blocked_dates_by_month,
            }
          )

          return isAvailable
        }

        // If no dates provided or invalid dates, but city matches, return true
        return true
      }) ?? false

    return hasAvailableProducts
  })

  return filteredCategories
}

/**
 * Filters attractions to only include those available during the specified date range
 * and in the selected cities.
 */
export const filterAvailableToursAcrossCategories = (
  attractions: AttractionShort[],
  fromDate?: string,
  toDate?: string,
  selectedCities?: string[]
): AttractionShort[] => {
  // Process and validate dates using the same logic as filterCategoriesWithAvailableTours
  const [fromDateObj, toDateObj, isValid] = normalizeDateInputs(
    fromDate,
    toDate
  )

  // If dates not provided or invalid, return all attractions
  if (!isValid || !fromDateObj || !toDateObj) return attractions

  // Filter out null attractions first
  const validAttractions = attractions.filter((a) => a !== null)

  return validAttractions.filter((attraction) => {
    return (
      isTourAvailableBetweenDates(fromDateObj, toDateObj, {
        blocked_dates: attraction.blocked_dates,
        blocked_dates_by_month: attraction.blocked_dates_by_month,
      }) &&
      isTourAvailableInCity(
        {
          cities: attraction.cities,
        },
        selectedCities
      )
    )
  })
}
