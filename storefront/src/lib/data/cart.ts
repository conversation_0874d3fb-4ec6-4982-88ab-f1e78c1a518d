import { sdk } from "@lib/config"
import { SelectParams } from "@medusajs/types"
import { ExchangeRate } from "types/cart"

export const retrieveCart = async (id?: string, query?: SelectParams) => {
  if (!id) {
    return null
  }

  const { cart } = await sdk.store.cart.retrieve(id, query)

  if (!cart) {
    return "Cart not found"
  }

  return cart
}

export const getExchangeRate = async () => {
  const { exchangeRate } = await sdk.client.fetch<{
    exchangeRate: ExchangeRate
  }>(`/store/exchange-rate`)

  return exchangeRate
}
