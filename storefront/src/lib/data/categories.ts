"use server"

import { sdk } from "@lib/config"
import { HttpTypes } from "@medusajs/types"
import { filterCategoriesWithAvailableTours } from "./categories-filter-service"

export const listCategories = async (query?: Record<string, any>) => {
  const cities_query = query?.c
  const from_query = query?.f
  const to_query = query?.t

  const lowerCaseCitiesQuery = cities_query?.map((city: string) =>
    city.toLowerCase()
  )

  const result = await sdk.client
    .fetch<{ product_categories: HttpTypes.StoreProductCategory[] }>(
      "/store/product-categories",
      {
        query: {
          fields:
            "id, name, rank, metadata, *products.tour.cities, products.tour.is_active, *products.tour.blocked_dates, *products.tour.blocked_dates_by_month",
        },
        cache: "no-store",
      }
    )
    .then(({ product_categories }) => {
      return product_categories.sort((a, b) => (a.rank ?? 0) - (b.rank ?? 0))
    })

  const onlyActiveTours = result.map((category) => {
    return {
      ...category,
      products: category.products?.filter((product) => {
        const tour = (product as any)?.tour
        return tour?.is_active
      }),
    }
  })

  const filteredResult = filterCategoriesWithAvailableTours(
    onlyActiveTours,
    from_query,
    to_query,
    lowerCaseCitiesQuery
  )

  return filteredResult
}

export const getCategoryBySlug = async (slug: string) => {
  const result = await sdk.client
    .fetch<{
      product_categories:
        | {
            id: string
            name: string
            metadata: {
              slug: string
              seo_description?: string
              seo_keywords?: string[]
              seo_title?: string
              image: string
            }
          }[]
        | null
    }>(`/store/product-categories`, {
      query: {
        fields: "name, metadata",
      },
      cache: "no-store",
    })
    .then(({ product_categories }) => product_categories)

  const filteredResult = result?.filter((category) => {
    return category.metadata?.slug === slug
  })

  if (!filteredResult || filteredResult.length === 0) {
    return null
  }

  return filteredResult[0]
}
