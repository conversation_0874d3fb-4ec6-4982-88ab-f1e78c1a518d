import { sdk } from "@lib/config"
import { GetGuideDetailsResult, GetGuidesResult } from "types/global"

export const listBlogPosts = async (q: string, limit?: number) => {
  return sdk.client.fetch<GetGuidesResult>("/guides", {
    query: {
      q,
      ...(limit && { limit: limit.toString() }),
    },
  })
}

export const getBlogPost = async (slug: string) => {
  try {
    const response = await sdk.client.fetch<GetGuideDetailsResult>(
      `/guides/by-slug/${slug}`
    )

    if (!response) {
      return null
    }

    return response
  } catch (error) {
    return null
  }
}

export const getRelatedBlogPosts = async (id: string) => {
  return sdk.client.fetch<GetGuidesResult>(`/guides/related/${id}`)
}

export const getFiveFirstRelatedGuidesByTag = async (tags: string[]) => {
  return sdk.client.fetch<GetGuidesResult>(
    `/guides/by-tags/${tags.join(",")}`,
    {
      query: {
        limit: 5,
      },
    }
  )
}
