import { sdk } from "@lib/config"

export const getCities = async () => {
  return sdk.client.fetch<{
    cities: {
      id: string
      name: string
      slug: string
    }[]
  }>(`/store/all-available-cities`)
}

export const getCitiesByCategorySlug = async (slug: string) => {
  return sdk.client.fetch<{
    cities: {
      id: string
      name: string
      slug: string
    }[]
  }>(`/store/cities-by-selected-category/${slug}`)
}
