import { sdk } from "@lib/config"
import {
  AttractionDetails,
  AttractionsByCategoryResult,
  AttractionShort,
} from "types/global"
import { filterAvailableToursAcrossCategories } from "./categories-filter-service"

export const getAttractionDetails = async (id: string) => {
  return sdk.client.fetch<AttractionDetails>(`/store/attractions/${id}`)
}

export const getRecommendedAttractions = async (ids: string[]) => {
  return sdk.client.fetch<{ attractions: AttractionDetails[] }>(
    `/store/attractions/recommended`,
    {
      query: { ids },
    }
  )
}

export const getAttractionBySlug = async (slug: string) => {
  return sdk.client.fetch<AttractionDetails>(
    `/store/attractions/by-slug/${slug}`
  )
}

export const getAttractions = async (q: string) => {
  return sdk.client.fetch<AttractionDetails>(`/store/attractions`, {
    query: { q },
  })
}

export const searchAttractions = async (query: string) => {
  if (query.length < 2) {
    return {
      tours: [],
      count: 0,
    }
  }

  return sdk.client.fetch<{ tours: AttractionShort[]; count: number }>(
    `/store/attractions/search`,
    {
      query: { q: query },
    }
  )
}

type GetAttractionsByCategoryOptions = {
  c: string[] | null
  f: string | null
  t: string | null
}

export const getAllAvailableAttractions = async (
  options?: GetAttractionsByCategoryOptions
) => {
  let result = await sdk.client.fetch<{ attractions: AttractionShort[] }>(
    `/store/attractions/all-available`,
    {
      query: {
        ...(options?.c ? { cities: options.c } : {}),
      },
    }
  )

  if (options?.f || options?.t) {
    result = {
      ...result,
      attractions: filterAvailableToursAcrossCategories(
        result.attractions,
        options?.f ?? "",
        options?.t ?? ""
      ),
    }
  }

  return result
}

export const getAttractionsByCategory = async (
  categorySlug: string,
  options?: GetAttractionsByCategoryOptions
) => {
  let result = await sdk.client.fetch<AttractionsByCategoryResult>(
    `/store/attractions/by-category-slug`,
    {
      query: {
        category_slug: categorySlug,
        ...(options?.c ? { cities: options.c } : {}),
      },
    }
  )

  if (options?.f || options?.t) {
    result = {
      ...result,
      attractions: filterAvailableToursAcrossCategories(
        result.attractions,
        options?.f ?? "",
        options?.t ?? ""
      ),
    }
  }

  return result
}
