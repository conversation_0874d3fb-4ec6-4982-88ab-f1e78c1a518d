import { parseISO, formatISO, format } from "date-fns"
import { pl } from "date-fns/locale"

const formatStringToDate = (date: string) => {
  return parseISO(date)
}

const formatDateToString = (date: Date) => {
  return format(date, "d.MM", { locale: pl })
}

const formatDateToUrl = (date: Date) => {
  return format(date, "dd-MM-yyyy")
}

export const dateUtils = {
  formatStringToDate,
  formatDateToString,
  formatDateToUrl,
}
