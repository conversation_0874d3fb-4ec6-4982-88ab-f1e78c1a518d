export const removeDateInParentheses = (text: string | undefined): string => {
  if (!text) {
    return ""
  }

  // Define a more specific regex pattern to match date ranges like (1 wrz 2025 - 4 paź 2025)
  // This explicitly looks for digits, followed by month abbreviation, followed by year
  const datePattern =
    /\(\d{1,2} [a-zA-<PERSON>żźćńółęąś]{2,3} \d{4} - \d{1,2} [a-zA-Zżźćńółęąś]{2,3} \d{4}\)/g

  // Replace all matches of the date pattern with an empty string
  const result = text.replace(datePattern, "")

  // Remove any extra spaces left after replacement
  return result.replace(/\s+/g, " ").trim()
}
