"use client"

import React, { create<PERSON>ontext, useContext, ReactNode } from "react"
import { useAttractionsSearch } from "hooks/useAttractionsSearch"
import { AttractionShort } from "types/global"

interface AttractionsSearchContextType {
  query: string
  setQuery: (query: string) => void
  results: AttractionShort[] | null
  isLoading: boolean
  error: string | null
  clearSearch: () => void
  hasActiveSearch: boolean
  resultsCount: number
}

const AttractionsSearchContext = createContext<
  AttractionsSearchContextType | undefined
>(undefined)

interface AttractionsSearchProviderProps {
  children: ReactNode
}

export const AttractionsSearchProvider: React.FC<
  AttractionsSearchProviderProps
> = ({ children }) => {
  const searchState = useAttractionsSearch()

  return (
    <AttractionsSearchContext.Provider value={searchState}>
      {children}
    </AttractionsSearchContext.Provider>
  )
}

export const useAttractionsSearchContext = (): AttractionsSearchContextType => {
  const context = useContext(AttractionsSearchContext)
  if (context === undefined) {
    throw new Error(
      "useAttractionsSearchContext must be used within an AttractionsSearchProvider"
    )
  }
  return context
}
