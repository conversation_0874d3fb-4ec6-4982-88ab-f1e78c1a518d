{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": "./src", "paths": {"@lib/*": ["lib/*"], "@modules/*": ["modules/*"], "@pages/*": ["pages/*"], "@icons/*": ["../public/icons/*"], "@images/*": ["../public/images/*"]}, "plugins": [{"name": "next"}]}, "include": ["svgr.d.ts", "next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "../medusa/src/admin/lib/util/string-dates.ts"], "exclude": ["node_modules", ".next", ".nyc_output", "coverage", "jest-coverage"]}