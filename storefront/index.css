@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

@layer base {
  html {
    scroll-behavior: smooth;
  }
}

@layer utilities {
  /* Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .wrap-long-links {
    word-break: break-word;
    hyphens: auto;
  }

  .no_selection {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .no-scrollbar::-webkit-scrollbar-track {
    background-color: transparent;
  }

  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  input:focus ~ label,
  input:not(:placeholder-shown) ~ label {
    @apply -translate-y-2 text-xsmall-regular;
  }

  input:focus ~ label {
    @apply left-0;
  }

  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  textarea:-webkit-autofill,
  textarea:-webkit-autofill:hover,
  textarea:-webkit-autofill:focus,
  select:-webkit-autofill,
  select:-webkit-autofill:hover,
  select:-webkit-autofill:focus {
    border: 1px solid #212121;
    -webkit-text-fill-color: #212121;
    -webkit-box-shadow: 0 0 0px 1000px #fff inset;
    transition: background-color 5000s ease-in-out 0s;
  }

  input[type="search"]::-webkit-search-decoration,
  input[type="search"]::-webkit-search-cancel-button,
  input[type="search"]::-webkit-search-results-button,
  input[type="search"]::-webkit-search-results-decoration {
    -webkit-appearance: none;
  }

  .animation-slide-up {
    bottom: 0;
  }

  /* Skeleton loader animation */
  .skeleton-shine {
    background: linear-gradient(
      to right,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.3) 25%,
      rgba(255, 255, 255, 0.3) 50%,
      rgba(255, 255, 255, 0) 100%
    );
    background-size: 200% 100%;
    background-position: -100% 0;
    animation: shine 1.5s infinite linear;
  }

  @keyframes shine {
    to {
      background-position: 200% 0;
    }
  }
}

@layer components {
  .container {
    @apply max-w-[1440px] w-full mx-auto px-4 md:px-8 lg:px-12 2xl:px-0;
  }

  .static-pages-typography {
    @apply [&_h1]:text-heading_secondary [&_h1]:lg:text-heading_main [&_h1]:leading-normal;
    @apply [&_h2]:text-heading_tertiary [&_h2]:lg:text-heading_secondary [&_h2]:leading-normal;
    @apply [&_h3]:text-heading_quaternary [&_h3]:lg:text-heading_tertiary [&_h3]:leading-normal;
    @apply [&_h4]:text-heading_quinary [&_h4]:lg:text-heading_quaternary [&_h4]:leading-normal;

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      @apply font-bold;
    }
  }

  .contrast-btn {
    @apply px-4 py-2 border border-black rounded-full hover:bg-black hover:text-white transition-colors duration-200 ease-in;
  }

  .text-xsmall-regular {
    @apply text-[10px] leading-4 font-normal;
  }

  .text-xsmall-light {
    @apply text-[10px] leading-4 font-light;
  }

  .text-small-regular {
    @apply text-xs leading-5 font-normal;
  }

  .text-small-semi {
    @apply text-xs leading-5 font-semibold;
  }

  .text-small-bold {
    @apply text-xs leading-5 font-bold;
  }

  .text-base-regular {
    @apply text-sm leading-6 font-normal;
  }

  .text-base-semi {
    @apply text-sm leading-6 font-semibold;
  }

  .text-base-bold {
    @apply text-sm leading-6 font-bold;
  }

  .text-large-light {
    @apply text-base leading-6 font-light;
  }

  .text-large-regular {
    @apply text-base leading-6 font-normal;
  }

  .text-large-semi {
    @apply text-base leading-6 font-semibold;
  }

  .text-xl-regular {
    @apply text-2xl leading-[36px] font-normal;
  }

  .text-xl-semi {
    @apply text-2xl leading-[36px] font-semibold;
  }

  .text-xl-bold {
    @apply text-2xl leading-[36px] font-bold;
  }

  .text-xl-estrabold {
    @apply text-2xl leading-[36px] font-extrabold;
  }

  .text-2xl-regular {
    @apply text-[30px] leading-[48px] font-normal;
  }

  .text-2xl-semi {
    @apply text-[30px] leading-[48px] font-semibold;
  }

  .text-3xl-regular {
    @apply text-[32px] leading-[44px] font-normal;
  }

  .text-3xl-semi {
    @apply text-[32px] leading-[44px] font-semibold;
  }

  .text-3xl-bold {
    @apply text-[32px] leading-[44px] font-bold;
  }

  .text-4xl-regular {
    @apply text-[36px] leading-[48px] font-normal;
  }

  .text-4xl-semibold {
    @apply text-[36px] leading-[48px] font-semibold;
  }

  .text-4xl-bold {
    @apply text-[36px] leading-[48px] font-bold;
  }

  .text-5xl-regular {
    @apply text-[48px] leading-[48px] font-normal;
  }

  .text-5xl-semibold {
    @apply text-[48px] leading-[48px] font-semibold;
  }

  .text-5xl-bold {
    @apply text-[48px] leading-[48px] font-bold;
  }

  .text-6xl-regular {
    @apply text-[52px] leading-[48px] font-regular;
  }

  .text-6xl-semibold {
    @apply text-[52px] leading-[48px] font-semibold;
  }

  .text-6xl-bold {
    @apply text-[52px] leading-[48px] font-bold;
  }

  .text-7xl-regular {
    @apply text-[64px] leading-[48px] font-regular;
  }

  .text-7xl-semibold {
    @apply text-[64px] leading-[48px] font-semibold;
  }

  .text-7xl-bold {
    @apply text-[64px] leading-[48px] font-bold;
  }
}

@layer base {
  :root {
    --primary: #000;
    --secondary: #f6b332;
    --background: #f8f8f8;
    --destructive: #d23232;
    --green: #7dcf56;
    --border: #222;
    --gray-50: #7e7e7e;
    --gray-20: #e8e8e8;
    --gray-10: #fbfbfb;
  }

  ::-webkit-scrollbar {
    width: 0.25rem;
  }

  ::-webkit-scrollbar-track {
    background: var(--background);
  }

  ::-webkit-scrollbar-thumb {
    background: var(--secondary);
    border-radius: 0.25rem;
  }

  .ProseMirror ul {
    list-style: disc;
    padding-left: 1.25em;
    margin: 0 0 1em 0;
  }

  .ProseMirror a {
    color: #007bff;
    text-decoration: underline;
  }

  .ProseMirror img {
    max-width: 100%;
    height: auto;
    margin: 3.7em 0;
  }

  .ProseMirror p {
    margin: 0 0 1em 0;
    min-height: 1.5em;

    @apply text-gray-50 font-light;
  }

  .ProseMirror h1,
  .ProseMirror h2,
  .ProseMirror h3,
  .ProseMirror h4,
  .ProseMirror h5,
  .ProseMirror h6 {
    font-weight: 400;
    margin: 1.5rem 0;
    @apply text-primary;
  }

  .ProseMirror strong {
    font-weight: 600;
  }

  .ProseMirror hr {
    width: 50%;
    max-width: 300px;
    @apply border-b-2 border-secondary;
    margin-top: 1em;
    margin-bottom: 1em;
  }

  .ProseMirror h1 {
    font-size: 2.25rem;
  }

  @media (max-width: 768px) {
    .ProseMirror h1 {
      font-size: 2rem;
    }
  }

  .ProseMirror h2 {
    font-size: 1.75rem;
  }

  @media (max-width: 768px) {
    .ProseMirror h2 {
      font-size: 1.5rem;
    }
  }

  .ProseMirror h3 {
    font-size: 1.375rem;
    position: relative;
    padding-bottom: 1.5rem;
  }

  .ProseMirror h3::before {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30%;
    min-width: 150px;
    height: 2px;
    border-radius: 99px;
    background-color: #f6b332;
  }

  @media (max-width: 768px) {
    .ProseMirror h3 {
      font-size: 1.5rem;
    }
  }

  .ProseMirror h4 {
    font-size: 1.125rem;
  }

  @media (max-width: 768px) {
    .ProseMirror h4 {
      font-size: 1.25rem;
    }
  }

  .ProseMirror h5 {
    font-size: 1rem;
  }

  .ProseMirror h6 {
    font-size: 0.875rem;
  }

  .blog {
    @apply max-w-screen-lg mx-auto px-4 md:px-8 mt-20 pb-16;

    img {
      clip-path: inset(6px round 0.5rem);
      max-width: 640px;
      margin-left: auto;
      margin-right: auto;
      width: 100%;
      height: auto;
    }
  }

  .mobile-filter-results-margin {
    @apply max-md:mt-32;
  }

  .inner-container {
    @apply max-w-screen-lg mx-auto px-4 md:px-8 mt-20 pb-16;
  }

  .inner-container-max-w {
    @apply max-w-screen-xl mx-auto px-4 md:px-8;
  }

  .animate-scaleIn {
    animation: scaleIn 200ms ease-out forwards;
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95) translateY(-5px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }
}
