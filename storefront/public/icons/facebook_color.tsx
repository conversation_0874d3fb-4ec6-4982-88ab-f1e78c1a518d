import { SVGProps } from "react"

interface IProps extends SVGProps<SVGSVGElement> {
  fill: string
}

export const FacebookColor = (props: IProps) => {
  return (
    <svg
      width="20"
      height="41"
      viewBox="0 0 20 41"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_847_45)">
        <path
          d="M4.42544 40.856H12.9357V21.8037H18.8732L19.5074 14.6694H12.9357V10.606C12.9357 8.92547 13.2725 8.25899 14.9029 8.25899H19.511V0.855957H13.6165C7.2813 0.855957 4.42544 3.64374 4.42544 8.9828V14.6694H-0.003479V21.8933H4.42544V40.856Z"
          fill={props.fill}
        />
      </g>
      <defs>
        <clipPath id="clip0_847_45">
          <rect
            width="19.5109"
            height="40"
            fill="white"
            transform="translate(-0.003479 0.855957)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}
