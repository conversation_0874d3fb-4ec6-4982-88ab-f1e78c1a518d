import { SVGProps } from "react"

interface IProps extends SVGProps<SVGSVGElement> {
  fill: string
}

export const YoutubeColor = (props: IProps) => {
  return (
    <svg
      width="26"
      height="19"
      viewBox="0 0 26 19"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_553_44)">
        <path
          d="M24.6039 3.01692C24.3161 1.92021 23.4698 1.05529 22.3949 0.761263C20.444 0.229004 12.63 0.229004 12.63 0.229004C12.63 0.229004 4.81391 0.229004 2.86517 0.761263C1.79029 1.05529 0.943933 1.92021 0.656172 3.01692C0.133545 5.00646 0.133545 9.15507 0.133545 9.15507C0.133545 9.15507 0.133545 13.3058 0.656172 15.2932C0.943933 16.3899 1.79029 17.2548 2.86517 17.5489C4.81391 18.0811 12.63 18.0811 12.63 18.0811C12.63 18.0811 20.4462 18.0811 22.3949 17.5489C23.4698 17.2548 24.3182 16.3921 24.6039 15.2932C25.1265 13.3037 25.1265 9.15507 25.1265 9.15507C25.1265 9.15507 25.1265 5.00431 24.6039 3.01692ZM10.074 12.9238V5.38848L16.6058 9.15722L10.074 12.9238Z"
          fill={props.fill}
        />
      </g>
      <defs>
        <clipPath id="clip0_553_44">
          <rect
            width="24.993"
            height="17.8521"
            fill="white"
            transform="translate(0.133667 0.229004)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}
