import { logger } from "@medusajs/framework";
import { MedusaContainer } from "@medusajs/framework/types";
import { ContainerRegistrationKeys, Modules } from "@medusajs/framework/utils";
import { ExtendedNotificationDTO } from "src/modules/smtp/types";

export default async function SendReminderAboutComingAttractionJob(
  container: MedusaContainer
) {
  logger.info("Sending reminder about coming attraction");

  const notificationModuleService = container.resolve(Modules.NOTIFICATION);

  const orderService = container.resolve(Modules.ORDER);

  const query = container.resolve(ContainerRegistrationKeys.QUERY);

  const { data } = await query.graph({
    entity: "order",
    fields: [
      "display_id",
      "metadata",
      "customer.email",
      "items.product.title",
      "ticket.selected_date",
      "ticket.date_from",
      "ticket.attraction_name",
      "created_at",
    ],
    filters: {
      $and: [
        {
          $or: [
            { metadata: { reminder_sent: false } },
            { metadata: { reminder_sent: { $exists: false } } },
          ],
        },
      ],
    },
  });

  const ordersThatNeedReminder = data.filter((order) => {
    const isOrderInRealization =
      order.metadata?.status === "during_realization";

    if (!isOrderInRealization) return false;

    const selectedDate =
      order.ticket?.selected_date || order.ticket?.date_from || "";

    const checkIfSelectedDateIsValid = (date: string | undefined): boolean => {
      if (!date) return false;
      const parsedDate = new Date(date);
      return !isNaN(parsedDate.getTime());
    };

    const isOrderAtLeast2DaysOld = (): boolean => {
      const orderDate = new Date(order.created_at);

      const twoDaysAgo = new Date();
      twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);

      const diffInHours = Math.abs(
        (orderDate.getTime() - twoDaysAgo.getTime()) / (1000 * 60 * 60)
      );

      return diffInHours >= 48;
    };

    const isSameDayOrOneDayBeforeSelectedDate = (
      date: string | undefined
    ): boolean => {
      if (!date) return false;

      const selectedDate = new Date(date);
      // Add 2 hours to the selected date - because the date is in UTC and we need to compare it to the local time
      selectedDate.setHours(selectedDate.getHours() + 2);
      const today = new Date();

      // Get only the date part (year, month, day)
      const selectedDateString = selectedDate.toISOString().split("T")[0];
      const todayString = today.toISOString().split("T")[0];

      // Create new Date objects with just the date part to compare calendar days
      const selectedDateOnly = new Date(selectedDateString);
      const todayDateOnly = new Date(todayString);

      // Calculate difference in days
      const oneDayInMs = 24 * 60 * 60 * 1000;
      const diffInMs = selectedDateOnly.getTime() - todayDateOnly.getTime();
      const diffInDays = diffInMs / oneDayInMs;
      const diffInHours = diffInMs / (1000 * 60 * 60);

      if (diffInHours <= 12 || diffInDays < 1) {
        return false;
      }

      return diffInDays === 1; // next day
    };

    return (
      checkIfSelectedDateIsValid(selectedDate) &&
      isSameDayOrOneDayBeforeSelectedDate(selectedDate) &&
      isOrderAtLeast2DaysOld()
    );
  });

  for (const order of ordersThatNeedReminder) {
    try {
      await notificationModuleService.createNotifications({
        to: order.customer?.email as string,
        channel: "email",
        template: "attraction-tomorrow-reminder",
        data: {
          order_id: order.id,
          customer_id: order.customer_id!,
          reservation_number: order.display_id.toString(),
          attraction_name: order.ticket?.attraction_name || "",
        },
      } satisfies ExtendedNotificationDTO);
    } catch (error) {
      logger.error(
        `Error sending reminder about coming attraction for order ${order.id}`,
        error
      );
    }
  }

  await orderService.updateOrders(
    ordersThatNeedReminder.map((order) => ({
      id: order.id,
      metadata: {
        ...order.metadata,
        reminder_sent: true,
      },
    }))
  );

  logger.info(
    `Found ${ordersThatNeedReminder.length} orders that need reminders`
  );
}

export const config = {
  name: "send-reminder-about-coming-attraction",
  // everyday at 8:00
  schedule: "0 6 * * *",
  // every 1 minute
  // schedule: "*/1 * * * *",
};
