import { logger } from "@medusajs/framework";
import { MedusaContainer } from "@medusajs/framework/types";
import { EXCHANGE_RATE_MODULE } from "src/modules/exchange-rate";
import ExchangeRateService from "src/modules/exchange-rate/service";

export default async function UpdateEurExchangeRateJob(
  container: MedusaContainer
) {
  logger.info("Updating EUR exchange rate");
  const exchangeRateService: ExchangeRateService =
    container.resolve(EXCHANGE_RATE_MODULE);

  logger.info("Fetching exchange rate");

  await exchangeRateService.fetchExchangeRate();

  logger.info("Exchange rate updated");
}

export const config = {
  name: "update-eur-exchange-rate",
  // once per 4 hours
  schedule: "0 */4 * * *",
};
