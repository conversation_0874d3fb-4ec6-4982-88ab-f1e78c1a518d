import { logger } from "@medusajs/framework";
import { MedusaContainer } from "@medusajs/framework/types";
import { Modules } from "@medusajs/framework/utils";

export default async function ClearDeactivatedCartsJob(
  container: MedusaContainer
) {
  const cartModuleService = container.resolve(Modules.CART);

  try {
    const [deactivatedCarts, count] = await cartModuleService.listAndCountCarts(
      {
        metadata: {
          deactivated_at: {
            $ne: null,
          },
        },
      }
    );

    const cartsToDelete = deactivatedCarts.map((cart) => cart.id);

    await cartModuleService.deleteCarts(cartsToDelete);

    logger.info(`Deleted ${cartsToDelete.length} deactivated carts`);
  } catch (error) {
    console.error(error);
  }
}

export const config = {
  name: "clear-deactivated-carts",
  // once at 3:00
  schedule: "0 3 * * *",
};
