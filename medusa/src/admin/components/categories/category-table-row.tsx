import {
  Badge,
  Dropdown<PERSON><PERSON>u,
  <PERSON><PERSON><PERSON><PERSON>on,
  Prompt,
  Table,
  toast,
} from "@medusajs/ui";
import { memo, useState } from "react";
import { TExtendedCategory } from "../../types";
import { DotsSix, EllipsisVertical } from "@medusajs/icons";
import { useMutation } from "@tanstack/react-query";
import { deleteExtendedCategory } from "../../lib/api/categories";
import CreatedAtBadge from "../reusable/created-at-badge";
import ImagePreview from "../reusable/image-preview";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

type Props = {
  category: TExtendedCategory;
  refetch: () => void;
};

const CategoryTableRow = memo(({ category, refetch }: Props) => {
  const [openPrompt, setOpenPrompt] = useState(false);
  const { isPending, mutate } = useMutation({
    mutationFn: deleteExtendedCategory,
    onSuccess: () => {
      toast.success(`Kategoria - ${category.name} została usunięta`);
      refetch();
    },
  });

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: category.id,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <>
      <Table.Row key={category.id} ref={setNodeRef} style={style}>
        <Table.Cell className="w-0">
          <Badge {...attributes} {...listeners} className="cursor-grab">
            <DotsSix className="text-ui-fg-subtle" />
            {category.rank}
          </Badge>
        </Table.Cell>
        <Table.Cell>
          <ImagePreview image={category.metadata?.image} alt={category.name} />
        </Table.Cell>
        <Table.Cell>{category.metadata?.slug}</Table.Cell>
        <Table.Cell>{category.is_active ? "Tak" : "Nie"}</Table.Cell>
        <Table.Cell className="w-1/3">{category.name}</Table.Cell>
        <Table.Cell>
          <CreatedAtBadge createdAt={category.created_at} />
        </Table.Cell>
        <Table.Cell>
          <DropdownMenu>
            <DropdownMenu.Trigger>
              <IconButton>
                <EllipsisVertical />
              </IconButton>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content>
              <DropdownMenu.Item asChild>
                <a href={`/app/kategorie/${category.id}`}>Edytuj</a>
              </DropdownMenu.Item>
              <DropdownMenu.Item
                disabled={isPending}
                onClick={() => {
                  setOpenPrompt(true);
                }}
                className="text-red-600"
              >
                Usuń
              </DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu>
        </Table.Cell>
      </Table.Row>

      <Prompt open={openPrompt} onOpenChange={setOpenPrompt}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>
              Czy na pewno chcesz usunąć tą kategorię?
            </Prompt.Title>
            <Prompt.Description>
              Po usunięciu kategorii nie będzie możliwości jej przywrócenia.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel>Anuluj</Prompt.Cancel>
            <Prompt.Action onClick={() => mutate(category.id)}>
              Usuń
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>
    </>
  );
});

export default CategoryTableRow;
