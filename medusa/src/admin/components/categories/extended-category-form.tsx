import { useMutation } from "@tanstack/react-query";
import {
  <PERSON><PERSON>,
  Container,
  FocusModal,
  Input,
  Kbd,
  Switch,
  Textarea,
  toast,
  Toaster,
} from "@medusajs/ui";
import { FieldErrors, useForm, UseFormReturn, useWatch } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Spinner } from "@medusajs/icons";
import ImageDropzone from "../reusable/image_dropzone";
import FormField from "../guides/form-field";
import {
  TCreateExtendedCategoryFormBody,
  toursSchemas,
} from "../tours/schemas";
import {
  createExtendedCategory,
  updateExtendedCategory,
} from "../../lib/api/categories";
import TagsInput from "../guides/tags-input";

type Props = {
  initialValues?: TCreateExtendedCategoryFormBody;
  editMode?: boolean;
  idToEdit?: string;
  onSuccessfulSubmit?: () => void;
};

const ExtendedCategoryForm = ({
  initialValues,
  onSuccessfulSubmit,
  editMode,
  idToEdit,
}: Props) => {
  const defaultValues: TCreateExtendedCategoryFormBody = initialValues ?? {
    name: "",
    description: "",
    is_active: true,
    metadata: {
      image: "",
      slug: "",
      seo_title: "",
      seo_description: "",
      seo_keywords: [],
    },
  };

  const form = useForm<typeof defaultValues>({
    defaultValues: defaultValues,
    resolver: zodResolver(toursSchemas.createExtendedCategoryFormBody),
  });

  const formValues = useWatch({ control: form.control });

  const { mutate: triggerExtendedCategoryCreationSubmit, isPending } =
    useMutation({
      mutationFn: async (data: TCreateExtendedCategoryFormBody) => {
        const augmentedPayload = {
          ...data,
          handle: data.metadata.slug,
          is_active: data.is_active,
        };

        if (editMode) {
          return await updateExtendedCategory(idToEdit!, augmentedPayload);
        }

        return await createExtendedCategory(augmentedPayload);
      },
      onSuccess: () => {
        onSuccessfulSubmit?.();

        if (editMode) {
          return toast.success("Kategoria zaktualizowana");
        } else {
          toast.success("Kategoria utworzona");
          form.reset(defaultValues);
        }
      },
      onError: (error: unknown) => {
        if (editMode) {
          return toast.error(
            `Wystąpił błąd podczas aktualizacji kategorii - ${JSON.stringify(
              error
            )}`
          );
        } else {
          toast.error(
            `Wystąpił błąd podczas tworzenia kategorii - ${JSON.stringify(
              error
            )}`
          );
        }
        console.error(error);
      },
    });

  const onSubmit = (data: TCreateExtendedCategoryFormBody) => {
    triggerExtendedCategoryCreationSubmit(data);
  };

  const onError = (error: FieldErrors<TCreateExtendedCategoryFormBody>) => {
    toast.error(`Wystąpił błąd podczas tworzenia kategorii - ${error}`);
    console.error(error);
  };

  const generateSlugFromName = () => {
    if (!formValues.name) {
      return toast.error("Aby wygenerować slug, najpierw wprowadź tytuł");
    }

    const name = formValues.name as string;
    const slug = name
      .toLowerCase()
      .replace(/ /g, "-")
      .replace(/[^a-zA-Z0-9-]/g, "");

    form.setValue("metadata.slug", slug, {
      shouldValidate: true,
    });
  };

  return (
    <>
      <Toaster />

      <Container
        className={`flex flex-col gap-4 rounded-none max-w-screen-lg mx-auto ${
          editMode ? "rounded-md" : ""
        }`}
      >
        <FormField control={form.control} name="is_active">
          <FormField.Label>Opublikuj kategorię</FormField.Label>
          <Switch
            checked={formValues.is_active}
            onCheckedChange={(changed) => form.setValue("is_active", changed)}
          />
        </FormField>
        <FormField control={form.control} name="name">
          <FormField.Label>Tytuł</FormField.Label>
          <Input {...form.register("name")} />
        </FormField>

        <FormField control={form.control} name="metadata.slug">
          <FormField.Label>Slug</FormField.Label>
          <div className="flex gap-2 ">
            <Button onClick={generateSlugFromName}>Wygeneruj slug</Button>
            <div className="grow">
              <Input {...form.register("metadata.slug")} />
            </div>
          </div>
        </FormField>

        <FormField control={form.control} name="metadata.seo_title">
          <FormField.Label>Meta tytuł</FormField.Label>
          <Input {...form.register("metadata.seo_title")} />
        </FormField>

        <FormField control={form.control} name="metadata.seo_description">
          <FormField.Label>Meta opis</FormField.Label>
          <Textarea
            {...form.register("metadata.seo_description")}
            placeholder="Opis meta dla SEO - powinien zawierać od 50 do 160 znaków"
          />
        </FormField>

        <FormField control={form.control} name="metadata.seo_keywords">
          <FormField.Label>Słowa kluczowe - Tagi</FormField.Label>
          <FormField.Note>
            Wpisz słowo kluczowe a następnie zatwierdź je przez <Kbd>Enter</Kbd>{" "}
            lub <Kbd>,</Kbd>
          </FormField.Note>
          <TagsInput
            field_key="metadata.seo_keywords"
            form={form as unknown as UseFormReturn}
            formValues={formValues}
          />
        </FormField>

        <FormField control={form.control} name="metadata.image">
          <FormField.Label>Zdjęcie</FormField.Label>
          <FormField.Note>
            Zdjęcie powinno mieć proporcje horyzontalne
          </FormField.Note>
          <FormField.Note>
            Zdjęcie używane będzie zarówno jako zdjęcie dla SEO i mediów
            społecznościowych
          </FormField.Note>
          <ImageDropzone
            syncedWithForm
            form={form}
            field_key="metadata.image"
          />
        </FormField>

        <FormField control={form.control} name="description">
          <FormField.Label>Opis</FormField.Label>
          <Textarea
            {...form.register("description")}
            placeholder="Opis kategorii"
          />
        </FormField>

        <div className="h-[30vh]" />
      </Container>

      {editMode ? (
        <div className="flex p-4 sticky bottom-0 bg-ui-bg-base border w-full justify-end  rounded-md ">
          <Button
            className="self-end"
            onClick={form.handleSubmit(onSubmit, onError)}
          >
            {isPending ? (
              <Spinner className="animate-spin" />
            ) : (
              "Aktualizuj kategorię"
            )}
          </Button>
        </div>
      ) : (
        <FocusModal.Footer className="sticky bottom-0 bg-ui-bg-base">
          <Button
            className="self-end"
            onClick={form.handleSubmit(onSubmit, onError)}
          >
            {isPending ? (
              <Spinner className="animate-spin" />
            ) : (
              "Stwórz nową kategorię"
            )}
          </Button>
        </FocusModal.Footer>
      )}
    </>
  );
};

export default ExtendedCategoryForm;
