import { DragEndEvent } from "@dnd-kit/core";
import { useMutation } from "@tanstack/react-query";
import { TExtendedCategory } from "../../../types";

type Props = {
  categories: TExtendedCategory[];
  refetch: () => void;
};

const useUpdateCategoryPosition = ({ categories, refetch }: Props) => {
  const { mutate: updatePositions } = useMutation({
    mutationFn: async (updates: { id: string; rank: number }[]) => {
      const response = await fetch("/admin/categories/positions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ updates }),
      });

      if (!response.ok) {
        throw new Error("Failed to update positions");
      }
    },
    onSuccess: () => {
      refetch();
    },
  });

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over || active.id === over.id) {
      return;
    }

    const oldIndex = categories.findIndex(
      (category) => category.id === active.id
    );
    const newIndex = categories.findIndex(
      (category) => category.id === over.id
    );

    const newCategories = [...categories];
    const [movedCategory] = newCategories.splice(oldIndex, 1);
    newCategories.splice(newIndex, 0, movedCategory);

    const updates = newCategories.map((category, index) => ({
      id: category.id,
      rank: index,
    }));

    updatePositions(updates);
  };

  return { handleDragEnd };
};

export default useUpdateCategoryPosition;
