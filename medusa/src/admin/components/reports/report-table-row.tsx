import { Badge, Table } from "@medusajs/ui";
import { TReport } from "../../types";
import { OrderStatusColorMap } from "../../widgets/order-status/types";

interface IProps {
  report: TReport;
}

const ReportTableRow = ({ report }: IProps) => {
  const formatToTwoDecimals = (value: number | undefined) =>
    typeof value === "number" && !isNaN(value) ? value.toFixed(2) : "0.00";

  return (
    <>
      <Table.Row>
        <Table.Cell className="w-2/3">Przychód</Table.Cell>
        <Table.Cell>{formatToTwoDecimals(report.income)} (EUR)</Table.Cell>
      </Table.Row>
      <Table.Row>
        <Table.Cell className="w-2/3">Ilość sprzedaży / zamówień</Table.Cell>
        <Table.Cell>{report.sales_quantity}</Table.Cell>
      </Table.Row>
      <Table.Row>
        <Table.Cell className="w-2/3">Kwota zwrotów</Table.Cell>
        <Table.Cell>
          {formatToTwoDecimals(report.refund_amount)} (EUR)
        </Table.Cell>
      </Table.Row>
      <Table.Row>
        <Table.Cell className="w-2/3">Saldo</Table.Cell>
        <Table.Cell>{formatToTwoDecimals(report.balance)} (EUR)</Table.Cell>
      </Table.Row>
      <div className="p-4 block text-xs">
        * Przychód oraz ilość sprzedaży obejmuje tylko zamówienia w statusie:
        <Badge
          size="small"
          color={OrderStatusColorMap["during_realization"]}
          className="mx-1"
        >
          W realizacji
        </Badge>
        ,
        <Badge
          size="small"
          color={OrderStatusColorMap["feedback_requested"]}
          className="mx-1"
        >
          Prośba o opinię
        </Badge>
        ,
        <Badge
          size="small"
          color={OrderStatusColorMap["new_order"]}
          className="mx-1"
        >
          Nowe zamówienie
        </Badge>
      </div>
    </>
  );
};

export default ReportTableRow;
