import { Badge } from "@medusajs/ui";
import { formatDate } from "../../lib/util/string-dates";

type Props = {
  createdAt: string | Date;
  withTime?: boolean;
};

const CreatedAtBadge = ({ createdAt, withTime = false }: Props) => {
  return (
    <Badge color="orange" className="whitespace-nowrap">
      {formatDate(createdAt?.toString(), withTime)}
    </Badge>
  );
};

export default CreatedAtBadge;
