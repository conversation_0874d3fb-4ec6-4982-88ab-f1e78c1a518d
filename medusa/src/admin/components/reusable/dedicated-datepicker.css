input[type="date"]::-webkit-inner-spin-button,
input[type="date"]::-webkit-calendar-picker-indicator {
  display: none;
  -webkit-appearance: none;
}

.rdp-root {
  --rdp-accent-color: #303030; /* Change the accent color to indigo. */
  --rdp-accent-background-color: #f0f0f0; /* Change the accent background color. */
  /* Add more CSS variables here. */
}

.rdp-selected .rdp-day_button {
  @apply bg-ui-bg-subtle rounded-md border shadow-sm border-ui-bg-base-pressed font-semibold;
}

.rdp-caption_label {
  @apply text-ui-fg-base font-medium;
}

.rdp-button_previous,
.rdp-button_next {
  @apply text-ui-fg-base bg-ui-bg-subtle border border-ui-border-danger shadow-sm mx-1 rounded-md;
}

.rdp-button_next:hover,
.rdp-button_previous:hover {
  @apply bg-ui-bg-base-pressed;
}

.rdp-month_grid {
  @apply min-h-[300px];
}

.rdp-day.rdp-today .rdp-day_button {
  @apply !text-ui-bg-interactive;
}
