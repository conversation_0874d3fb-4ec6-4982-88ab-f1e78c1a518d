import { useState } from "react";
import { useDropzone } from "react-dropzone";
import { <PERSON><PERSON>, Container, toast, Toaster } from "@medusajs/ui";
import { HttpTypes } from "@medusajs/types";
import { FieldValues, Path, PathValue, UseFormReturn } from "react-hook-form";
import { sdk } from "../../../lib/config";
import { DndContext, closestCenter, DragEndEvent } from "@dnd-kit/core";
import {
  rectSortingStrategy,
  SortableContext,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { DotsSix } from "@medusajs/icons";
import DeleteWithConfirmation from "../delete-with-confirmation";

type BaseProps = {
  getUploadedImageResult?: (
    image: HttpTypes.AdminFile | HttpTypes.AdminFile[]
  ) => void;
  isTargetingMultiple?: boolean;
};

type SyncedWithFormProps<T extends FieldValues> =
  | {
      syncedWithForm: true;
      form: UseFormReturn<T>;
      field_key: Path<T>;
    }
  | {
      syncedWithForm?: false;
      form?: never;
      field_key?: never;
    };

type Props<T extends FieldValues> = BaseProps & SyncedWithFormProps<T>;

const SortableImage = ({
  image,
  index,
  onDelete,
}: {
  image: string | HttpTypes.AdminFile;
  index: number;
  onDelete: (index: number) => void;
}) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } =
    useSortable({ id: index.toString() });

  const style = {
    transform: CSS.Transform.toString(transform),
    zIndex: isDragging ? 1 : 0,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`relative group min-h-20 ${isDragging ? "opacity-50" : ""}`}
    >
      <img
        className="object-contain  border  bg-ui-bg-base  rounded-md"
        src={typeof image === "string" ? image : image.url}
        alt={`Uploaded image ${index + 1}`}
      />
      <DeleteWithConfirmation onDelete={() => onDelete(index)} />
      <button
        {...attributes}
        {...listeners}
        className="absolute top-2 left-2 p-1 rounded bg-white/80 cursor-grab active:cursor-grabbing"
      >
        <DotsSix />
      </button>
    </div>
  );
};

const ImageDropzone = <T extends FieldValues>({
  getUploadedImageResult,
  field_key,
  form,
  syncedWithForm,
  isTargetingMultiple = false,
}: Props<T>) => {
  const [selectedImages, setSelectedImages] = useState<HttpTypes.AdminFile[]>(
    []
  );
  const [localAttachments, setLocalAttachments] = useState<File[]>([]);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop: (acceptedFiles) => {
      setLocalAttachments([...localAttachments, ...acceptedFiles]);

      sdk.admin.upload
        .create({ files: acceptedFiles })
        .then((res) => {
          toast.success("Image uploaded successfully");

          if (isTargetingMultiple) {
            const currentImages = syncedWithForm
              ? ((form.watch(field_key) || []) as string[])
              : selectedImages;

            const newImages = [
              ...currentImages,
              ...res.files.map((file) => file.url),
            ];

            if (syncedWithForm) {
              form.setValue(field_key, newImages as PathValue<T, Path<T>>, {
                shouldValidate: true,
              });
            } else {
              setSelectedImages([...selectedImages, ...res.files]);
              getUploadedImageResult?.([...selectedImages, ...res.files]);
            }
          } else {
            if (syncedWithForm) {
              form.setValue(
                field_key,
                res.files[0].url as PathValue<T, Path<T>>,
                {
                  shouldValidate: true,
                }
              );
            } else {
              setSelectedImages([res.files[0]]);
              getUploadedImageResult?.(res.files[0]);
            }
          }
        })
        .catch((err) => {
          console.error(err);
          toast.error(`Failed to upload image - ${JSON.stringify(err)}`);
        });
    },
  });

  const handleDelete = (index?: number) => {
    if (isTargetingMultiple && typeof index === "number") {
      if (syncedWithForm) {
        const currentImages = (form.watch(field_key) || []) as string[];
        const newImages = currentImages.filter((_, i) => i !== index);
        form.setValue(field_key, newImages as PathValue<T, Path<T>>, {
          shouldValidate: true,
        });
      } else {
        const newImages = selectedImages.filter((_, i) => i !== index);
        setSelectedImages(newImages);
      }
    } else {
      if (syncedWithForm) {
        form.setValue(
          field_key,
          (isTargetingMultiple ? [] : null) as PathValue<T, Path<T>>,
          {
            shouldValidate: true,
          }
        );
      } else {
        setSelectedImages([]);
        setLocalAttachments([]);
      }
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;

    const oldIndex = parseInt(active.id.toString());
    const newIndex = parseInt(over.id.toString());

    if (syncedWithForm) {
      const currentImages = (form.watch(field_key) || []) as string[];
      const newImages = [...currentImages];
      const [movedImage] = newImages.splice(oldIndex, 1);
      newImages.splice(newIndex, 0, movedImage);

      form.setValue(field_key, newImages as PathValue<T, Path<T>>, {
        shouldValidate: true,
      });
    } else {
      const newImages = [...selectedImages];
      const [movedImage] = newImages.splice(oldIndex, 1);
      newImages.splice(newIndex, 0, movedImage);
      setSelectedImages(newImages);
      getUploadedImageResult?.(newImages);
    }
  };

  const conditionalImages = syncedWithForm
    ? (form.watch(field_key) as string | string[])
    : selectedImages;

  const renderImages = () => {
    if (
      !conditionalImages ||
      (Array.isArray(conditionalImages) && conditionalImages.length === 0)
    ) {
      return null;
    }

    if (isTargetingMultiple) {
      const images = Array.isArray(conditionalImages)
        ? conditionalImages
        : [conditionalImages];

      return (
        <DndContext
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <div className="grid sm:grid-cols-3 gap-2 bg-ui-bg-base-hover p-2">
            <SortableContext
              items={images.map((_, index) => index.toString())}
              strategy={rectSortingStrategy}
            >
              {images.map((image, index) => (
                <SortableImage
                  key={index}
                  image={image}
                  index={index}
                  onDelete={handleDelete}
                />
              ))}
            </SortableContext>
          </div>
        </DndContext>
      );
    }

    // Single image display (backwards compatibility)
    const imageUrl =
      typeof conditionalImages === "string"
        ? conditionalImages
        : Array.isArray(conditionalImages)
        ? conditionalImages[0]?.url
        : conditionalImages[0]?.url;

    return imageUrl ? (
      <div className="relative group">
        <img
          className="object-contain max-h-64 rounded-sm"
          src={imageUrl}
          alt="Selected image"
        />
        <div className="group-hover:visible invisible absolute inset-0 bg-black/10 z-0"></div>
        <DeleteWithConfirmation onDelete={() => handleDelete()} />
      </div>
    ) : null;
  };

  return (
    <Container className="h-fit relative">
      <Toaster />

      {renderImages()}

      {(!conditionalImages ||
        (isTargetingMultiple && Array.isArray(conditionalImages)) ||
        (!isTargetingMultiple &&
          (!conditionalImages || conditionalImages.length === 0))) && (
        <div {...getRootProps()}>
          <input {...getInputProps()} />
          <div className="flex items-center justify-center">
            <div className="text-center flex flex-col gap-2 items-center p-4">
              <p className="text-ui-fg-muted">Przeciągnij i upuść zdjęcie</p>
              <p>lub</p>
              <Button>Wybierz z dysku</Button>
            </div>
          </div>
        </div>
      )}
    </Container>
  );
};

export default ImageDropzone;
