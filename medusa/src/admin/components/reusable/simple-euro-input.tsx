import { Input } from "@medusajs/ui";
import {
  FieldValues,
  Path,
  UseFormRegister,
  UseFormReturn,
} from "react-hook-form";

type Props<T extends FieldValues> = {
  name: Path<T>;
  register: UseFormRegister<T>;
  form: UseFormReturn<T>;
  percentage?: boolean;
};

export const SimpleEuroInput = <T extends FieldValues>({
  name,
  register,
  form,
  percentage = false,
}: Props<T>) => {
  return (
    <div className="relative flex bg-ui-bg-subtle w-full">
      <div className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-ui-fg-subtle border-l pl-2">
        {percentage ? "%" : "€"}
      </div>
      <div className="w-full min-w-0">
        <Input
          className="bg-transparent hover:bg-transparent min-w-0 [&>input]:min-w-0"
          inputMode="numeric"
          placeholder="Cena"
          {...register(name, {
            valueAsNumber: true,
            onChange(e) {
              const value = e.target.value.replace(/[^0-9.,]/g, "");
              const replace = value.replace(",", ".");
              form.setValue(name, replace);
            },
          })}
        />
      </div>
    </div>
  );
};

export const ControlledSimpleEuroInput = ({
  value,
  onChange,
  placeholder = "Kwota",
}: {
  value: string | undefined;
  onChange: (value: string | undefined) => void;
  placeholder?: string;
}) => {
  const displayValue = value !== undefined ? value.toString() : "";

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let newValue = e.target.value;

    newValue = newValue.replace(/,/g, ".");

    newValue = newValue.replace(/[^0-9.]/g, "");
    const parts = newValue.split(".");
    if (parts.length > 2) {
      newValue = `${parts[0]}.${parts.slice(1).join("")}`;
    }
    onChange(newValue || undefined);
  };
  return (
    <div className="relative flex bg-ui-bg-subtle w-full">
      <div className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-ui-fg-subtle border-l pl-2">
        {"€"}
      </div>
      <div className="w-full min-w-0">
        <Input
          aria-invalid={false}
          className="bg-transparent hover:bg-transparent min-w-0 [&>input]:min-w-0"
          inputMode="numeric"
          placeholder={placeholder}
          value={displayValue}
          onChange={handleChange}
        />
      </div>
    </div>
  );
};
