import { Popover } from "@medusajs/ui";
import { DayPicker } from "react-day-picker";
import "react-day-picker/style.css";
import "./dedicated-datepicker.css";
import { pl } from "react-day-picker/locale";
import { Calendar, XMark } from "@medusajs/icons";

type Props = {
  value: Date | undefined | null;
  setValue: (value: Date | undefined) => void;
};

const DedicatedDatePicker = ({ value, setValue }: Props) => {
  // Function to strip time from date and ensure it's in local timezone
  const stripTime = (date: Date | undefined): Date | undefined => {
    if (!date) return undefined;
    const d = new Date(date);
    d.setHours(0, 0, 0, 0);
    return d;
  };

  // Format date to YYYY-MM-DD for input value
  const formatDateForInput = (date: Date | undefined): string => {
    if (!date) return "";
    const d = new Date(date);
    return d.toLocaleDateString("en-CA"); // This formats as YYYY-MM-DD
  };

  // Handle focus - set today's date if no date is selected
  const handleFocus = () => {
    if (!value) {
      setValue(stripTime(new Date()));
    }
  };

  return (
    <Popover>
      <Popover.Trigger className="w-full bg-ui-bg-component text-left flex px-2 border shadow-sm rounded-md items-center gap-1 h-8">
        <Calendar className="text-ui-fg-muted" />
        <div className="h-4 w-px bg-ui-fg-disabled" />

        <input
          type="date"
          className="outline-none text-sm border-none bg-inherit focus:shadow-sm focus:ring-1  focus:shadow-ui-fg-interactive focus:ring-ui-fg-interactive focus:ring-opacity-20 rounded-md"
          value={formatDateForInput(value ?? undefined)}
          onChange={(e) => {
            const date = e.target.valueAsDate;
            setValue(stripTime(date ?? undefined));
          }}
          onFocus={handleFocus}
        />
        <button
          className="p-1 ml-auto rounded-full hover:bg-ui-bg-component"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            setValue(undefined);
          }}
        >
          <XMark className="text-ui-fg-muted" />
        </button>
      </Popover.Trigger>

      <Popover.Content className="p-4 shadow-sm border rounded-md">
        <DayPicker
          locale={pl}
          mode="single"
          selected={value ?? undefined}
          onSelect={(date) => setValue(stripTime(date))}
        />
      </Popover.Content>
    </Popover>
  );
};

export default DedicatedDatePicker;
