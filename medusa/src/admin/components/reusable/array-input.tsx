import { Button, Input } from "@medusajs/ui";
import { Plus, Trash } from "@medusajs/icons";
import { Control, UseFormRegister, useFieldArray } from "react-hook-form";
import FormField from "../guides/form-field";
import DeleteWithConfirmation from "./delete-with-confirmation";

type Props = {
  name: string;
  control: Control<any>;
  register: UseFormRegister<any>;
  label: string;
  placeholder?: string;
  actionLabel?: string;
  inputType?: "text" | "time" | "label_value";
};

const ArrayInput = ({
  name,
  control,
  register,
  label,
  actionLabel = "Dodaj",
  placeholder = "",
  inputType = "text",
}: Props) => {
  const { fields, append, remove } = useFieldArray({
    control,
    name,
  });

  return (
    <div className="flex flex-col">
      <div className="flex items-center justify-between">
        <FormField.Label>{label}</FormField.Label>
      </div>
      <div className="flex flex-wrap gap-x-8 items-center gap-4 mt-4">
        {fields.map((field, index) => (
          <div key={field.id} className="flex items-center gap-2">
            <Input
              type={inputType}
              {...register(`${name}.${index}`)}
              placeholder={placeholder}
            />
            <DeleteWithConfirmation onDelete={() => remove(index)}>
              <Button
                variant="danger"
                size="small"
                className="h-8 w-8 shadow-md hover:shadow-lg"
              >
                <Trash className="w-4 h-4" />
              </Button>
            </DeleteWithConfirmation>
          </div>
        ))}
      </div>
      <Button
        variant="primary"
        className={`self-end transition-[width] ease-out mt-4 ${
          fields.length > 0 ? "auto-24" : "w-full h-8"
        }`}
        onClick={() =>
          append("", {
            shouldFocus: true,
          })
        }
      >
        <Plus className="w-4 h-4" />
        {actionLabel}
      </Button>
    </div>
  );
};

export default ArrayInput;
