type Props = {
  image: string | null | undefined;
  alt: string;
};

const ImagePreview = ({ image, alt }: Props) => {
  return (
    <div className="aspect-video h-11 shadow-sm border my-1 rounded-md overflow-hidden">
      <img
        src={image || "https://i.imgur.com/2jnaeM9.jpeg"}
        alt={alt}
        className="w-full h-full object-cover"
      />
    </div>
  );
};

export default ImagePreview;
