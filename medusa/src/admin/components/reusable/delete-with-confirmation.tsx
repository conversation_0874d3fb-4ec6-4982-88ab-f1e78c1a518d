import { Button, Prompt } from "@medusajs/ui";
import React, { useState } from "react";

type Props = {
  onDelete: () => void;
  triggerClassName?: string;
  children?: React.ReactNode;
  customPromptContent?: {
    title: string;
    description: string;
  };
};

const DeleteWithConfirmation = ({
  onDelete,
  children,
  triggerClassName,
  customPromptContent,
}: Props) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <Prompt open={isOpen} onOpenChange={setIsOpen} variant="danger">
      <Prompt.Trigger asChild>
        {children ? (
          children
        ) : (
          <Button
            onClick={() => setIsOpen(true)}
            className={`absolute top-2 right-2 z-2 ${triggerClassName}`}
            variant="danger"
          >
            Usuń
          </Button>
        )}
      </Prompt.Trigger>
      <Prompt.Content>
        <Prompt.Header>
          <Prompt.Title>{customPromptContent?.title || "Usuń"}</Prompt.Title>
          <Prompt.Description>
            {customPromptContent?.description ||
              "<PERSON><PERSON> j<PERSON> pew<PERSON>, że chcesz usunąć dany element?"}
          </Prompt.Description>
        </Prompt.Header>
        <Prompt.Footer>
          <Prompt.Cancel>Anuluj</Prompt.Cancel>
          <Prompt.Action
            onClick={() => {
              onDelete();
              setIsOpen(false);
            }}
          >
            Usuń
          </Prompt.Action>
        </Prompt.Footer>
      </Prompt.Content>
    </Prompt>
  );
};

export default DeleteWithConfirmation;
