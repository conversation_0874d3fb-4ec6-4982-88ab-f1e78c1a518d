import { Trash } from "@medusajs/icons";
import { toast } from "@medusajs/ui";
import { KeyboardEvent, useRef } from "react";
import { UseFormReturn, useWatch } from "react-hook-form";

type Props = {
  formValues: ReturnType<typeof useWatch>;
  form: UseFormReturn;
  field_key?: string;
};

const getNestedValue = (obj: any, path: string, defaultValue: any = []) => {
  const value = path.split(".").reduce((acc, part) => acc?.[part], obj);

  return [null, undefined].includes(value) ? defaultValue : value;
};

const TagsInput = ({ formValues, form, field_key = "tags" }: Props) => {
  const tagsRef = useRef<HTMLDivElement>(null);
  if (!form) {
    return null;
  }

  const handleAppendingTags = (e: KeyboardEvent<HTMLDivElement>) => {
    if (!tagsRef.current) {
      return;
    }

    if (tagsRef.current.innerText.length === 0) {
      if (e.key === "Backspace") {
        const currentTags = getNestedValue(formValues, field_key);
        form.setValue(field_key, [...currentTags.slice(0, -1)]);
      }
      return;
    }

    const currentTags = getNestedValue(formValues, field_key);
    if (currentTags.includes(tagsRef.current.innerText)) {
      toast.error("Tag o tej nazwie już istnieje");
      return;
    }

    if (e.key === "," || e.key === "Enter") {
      e.preventDefault();
      form.setValue(field_key, [...currentTags, tagsRef.current.innerText]);
      tagsRef.current.innerHTML = "";
    }
  };

  const handleRemoveTag = (tag: string) => {
    const currentTags = getNestedValue(formValues, field_key);
    form.setValue(
      field_key,
      currentTags.filter((t: string) => t !== tag)
    );
  };

  const currentTags = getNestedValue(formValues, field_key);

  return (
    <div className="flex flex-wrap border-border rounded-md border p-1 gap-1">
      {currentTags?.map((tag: string) => (
        <div
          className="border bg-tag text-tag-blue-text rounded-md px-2 min-w-max flex items-center gap-1"
          key={tag}
        >
          {tag}
          <button onClick={() => handleRemoveTag(tag)}>
            <Trash className="size-4" />
          </button>
        </div>
      ))}
      <div
        className="grow outline-none"
        contentEditable
        onKeyDown={(e) => handleAppendingTags(e)}
        ref={tagsRef}
      ></div>
    </div>
  );
};

export default TagsInput;
