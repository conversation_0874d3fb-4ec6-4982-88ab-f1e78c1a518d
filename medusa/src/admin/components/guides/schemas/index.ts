import { z } from "zod";

const createGuideFormBody = z.object({
  name: z.string().min(3, "Za krótki tytuł"),
  content: z.string().min(3, "Za krótki opis"),
  position: z.number().optional().default(999),
  featured_image: z.string().min(3, "Zdjęcie wyróżniające jest wymagane"),
  tags: z.array(z.string()),
  slug: z.string().min(3, "Za krótki slug"),
  is_published: z.boolean(),
  related_guides: z.array(z.string()),
  seo_title: z.string().min(3, "Za krótki tytuł SEO"),
  og_title: z.string().min(3, "Za krótki tytuł w mediach społecznościowych"),
  preview_image: z.string().min(3, "Miniaturka jest wymagana"),
  meta_description: z
    .string()
    .min(50, "Meta description powinien zawierać minimum 50 znaków"),
});

const createGuidePayload = createGuideFormBody.extend({});

export const guidesSchemas = {
  createGuideFormBody,
  createGuidePayload,
};

export type TCreateGuideFormBody = z.infer<typeof createGuideFormBody>;
export type TCreateGuidePayload = z.infer<typeof createGuidePayload>;
