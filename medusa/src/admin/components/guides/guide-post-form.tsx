import { useMutation } from "@tanstack/react-query";
import {
  <PERSON><PERSON>,
  Container,
  FocusModal,
  Input,
  Kbd,
  Switch,
  Textarea,
  toast,
  Toaster,
} from "@medusajs/ui";
import { useForm, UseFormReturn, useWatch } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowUpRightOnBox, Spinner } from "@medusajs/icons";
import ImageDropzone from "../reusable/image_dropzone";
import RichTextEditor from "../rich-text-editor";
import {
  guidesSchemas,
  TCreateGuideFormBody,
  TCreateGuidePayload,
} from "./schemas";
import TagsInput from "./tags-input";
import FormField from "./form-field";
import { createGuide, updateGuide } from "../../lib/api/guides";
import RelatedGuidesInput from "./related-guides-input";
import { config } from "../../../config";
import LinkButton from "../reusable/link-button";

type Props = {
  initialValues?: TCreateGuideFormBody;
  editMode?: boolean;
  idToEdit?: string;
  onSuccessfulSubmit?: () => void;
};

const GuidePostForm = ({
  initialValues,
  onSuccessfulSubmit,
  editMode,
  idToEdit,
}: Props) => {
  const defaultValues: TCreateGuideFormBody = initialValues ?? {
    name: "",
    slug: "",
    content: "",
    featured_image: "",
    meta_description: "",
    tags: [],
    seo_title: "",
    og_title: "",
    is_published: true,
    preview_image: "",
    position: 999,
    related_guides: [],
  };

  const form = useForm<typeof defaultValues>({
    defaultValues: defaultValues,
    resolver: zodResolver(guidesSchemas.createGuideFormBody),
  });

  const formValues = useWatch({ control: form.control });

  const { mutate: triggerGuideCreationSubmit, isPending } = useMutation({
    mutationFn: async (data: TCreateGuidePayload) => {
      if (editMode) {
        return await updateGuide(idToEdit!, data);
      }

      return await createGuide(data);
    },
    onSuccess: () => {
      onSuccessfulSubmit?.();

      if (editMode) {
        return toast.success("Poradnik zaktualizowany");
      } else {
        toast.success("Poradnik utworzony");
        form.reset(defaultValues);
      }
    },
    onError: (error: unknown) => {
      if (editMode) {
        return toast.error(
          `Wystąpił błąd podczas aktualizacji poradnika - ${JSON.stringify(
            error
          )}`
        );
      } else {
        toast.error(
          `Wystąpił błąd podczas tworzenia poradnika - ${JSON.stringify(error)}`
        );
      }
      console.error(error);
    },
  });

  const onSubmit = (data: TCreateGuideFormBody) => {
    const augmentedData = {
      ...data,
      ...(!editMode && { position: 999 }),
    };
    triggerGuideCreationSubmit(augmentedData);
  };

  const createSeoTitleBasedOnName = () => {
    if (!formValues.name) {
      return toast.error("Aby wygenerować tytuł SEO, najpierw wprowadź tytuł");
    }

    form.setValue("seo_title", formValues.name);
  };

  const createOgTitleBasedOnName = () => {
    if (!formValues.name) {
      return toast.error(
        "Aby wygenerować tytuł w mediach społecznościowych, najpierw wprowadź tytuł"
      );
    }

    form.setValue("og_title", formValues.name);
  };

  const generateSlugFromName = () => {
    if (!formValues.name) {
      return toast.error("Aby wygenerować slug, najpierw wprowadź tytuł");
    }

    const name = formValues.name as string;
    const slug = name
      .toLowerCase()
      .replace(/ /g, "-")
      .replace(/[^a-zA-Z0-9-]/g, "");

    form.setValue("slug", slug);
  };

  return (
    <>
      <Toaster />

      <Container
        className={`flex flex-col gap-4 rounded-none max-w-screen-lg mx-auto ${
          editMode ? "rounded-md" : ""
        }`}
      >
        <FormField control={form.control} name="name">
          <FormField.Label>Tytuł</FormField.Label>
          <Input {...form.register("name")} />
        </FormField>

        <FormField control={form.control} name="slug">
          <FormField.Label>Slug</FormField.Label>
          <div className="flex gap-2 ">
            <Button onClick={generateSlugFromName}>Wygeneruj slug</Button>
            <div className="grow">
              <Input {...form.register("slug")} />
            </div>
          </div>
        </FormField>

        <FormField control={form.control} name="seo_title">
          <FormField.Label>Tytuł SEO</FormField.Label>
          <div className="flex gap-2">
            <Button onClick={createSeoTitleBasedOnName}>Skopiuj tytuł</Button>
            <div className="grow">
              <Input {...form.register("seo_title")} />
            </div>
          </div>
        </FormField>

        <FormField control={form.control} name="og_title">
          <FormField.Label>Tytuł w mediach społecznościowych</FormField.Label>
          <div className="flex gap-2">
            <Button onClick={createOgTitleBasedOnName}>Skopiuj tytuł</Button>
            <div className="grow">
              <Input {...form.register("og_title")} />
            </div>
          </div>
        </FormField>

        <FormField control={form.control} name="related_guides">
          <FormField.Label>Powiązane poradniki</FormField.Label>
          <RelatedGuidesInput
            value={formValues.related_guides ?? []}
            onChange={(value) => form.setValue("related_guides", value)}
          />
        </FormField>

        <FormField control={form.control} name="preview_image">
          <FormField.Label>Miniaturka</FormField.Label>
          <FormField.Note>
            Przesłany plik powinien mieć proporcje zbliżone do kwadratu
          </FormField.Note>
          <ImageDropzone syncedWithForm form={form} field_key="preview_image" />
        </FormField>

        <FormField control={form.control} name="meta_description">
          <FormField.Label>Opis SEO</FormField.Label>

          <Textarea
            {...form.register("meta_description")}
            placeholder="Opis meta dla SEO - powinien zawierać od 50 do 160 znaków"
          />
        </FormField>

        <FormField control={form.control} name="tags">
          <FormField.Label>Słowa kluczowe - Tagi</FormField.Label>
          <FormField.Note>
            Wpisz słowo kluczowe a następnie zatwierdź je przez <Kbd>Enter</Kbd>{" "}
            lub <Kbd>,</Kbd>
          </FormField.Note>
          <TagsInput
            field_key="tags"
            form={form as unknown as UseFormReturn}
            formValues={formValues}
          />
        </FormField>

        <FormField control={form.control} name="featured_image">
          <FormField.Label>Zdjęcie wyróżniające</FormField.Label>
          <FormField.Note>
            Zdjęcie powinno mieć proporcje horyzontalne ponieważ jest skalowane
            do całej szerokości ekranu
          </FormField.Note>
          <ImageDropzone
            syncedWithForm
            form={form}
            field_key="featured_image"
          />
        </FormField>

        <FormField control={form.control} name="is_published">
          <FormField.Label>Opublikuj</FormField.Label>
          <Switch
            checked={formValues.is_published}
            onCheckedChange={(changed) =>
              form.setValue("is_published", changed)
            }
          />
        </FormField>

        <FormField control={form.control} name="content">
          <FormField.Label>Treść</FormField.Label>
          <RichTextEditor
            content={form.watch("content")}
            onChange={(content) => form.setValue("content", content)}
          />
        </FormField>
      </Container>

      {editMode ? (
        <div className="flex p-4 sticky bottom-0 bg-ui-bg-base border w-full justify-end gap-4 rounded-md ">
          <LinkButton
            variant="secondary"
            href={`${config.MEDUSA_STOREFRONT_URL}/${initialValues?.slug}`}
            target="_blank"
          >
            Podgląd poradnika
            <ArrowUpRightOnBox className="w-4 h-4" />
          </LinkButton>

          <Button className="self-end" onClick={form.handleSubmit(onSubmit)}>
            {isPending ? (
              <Spinner className="animate-spin" />
            ) : (
              "Aktualizuj poradnik"
            )}
          </Button>
        </div>
      ) : (
        <FocusModal.Footer className="sticky bottom-0 bg-ui-bg-base">
          <Button className="self-end" onClick={form.handleSubmit(onSubmit)}>
            {isPending ? (
              <Spinner className="animate-spin" />
            ) : (
              "Stwórz nowy poradnik"
            )}
          </Button>
        </FocusModal.Footer>
      )}
    </>
  );
};

export default GuidePostForm;
