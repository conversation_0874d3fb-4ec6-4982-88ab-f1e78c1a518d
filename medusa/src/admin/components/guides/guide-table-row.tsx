import {
  Badge,
  clx,
  Dropdown<PERSON>enu,
  Icon<PERSON>utton,
  Prompt,
  Table,
  toast,
} from "@medusajs/ui";
import { memo, ReactNode, useCallback, useState } from "react";
import { TGuidePost } from "../../types";
import { ArrowUpRightOnBox, DotsSix, EllipsisVertical } from "@medusajs/icons";
import { useMutation } from "@tanstack/react-query";
import { deleteGuide } from "../../lib/api/guides";
import CreatedAtBadge from "../reusable/created-at-badge";
import ImagePreview from "../reusable/image-preview";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { config } from "../../../config";
import { Link } from "react-router-dom";

type Props = {
  guide: TGuidePost;
  refetch: () => void;
};

const GuideTableRow = memo(({ guide, refetch }: Props) => {
  const [openPrompt, setOpenPrompt] = useState(false);
  const { isPending, mutate } = useMutation({
    mutationFn: deleteGuide,
    onSuccess: () => {
      refetch();
      toast.success("Poradnik usunięty");
    },
    onError: () => {
      toast.error("Wystąpił błąd podczas usuwania poradnika");
    },
  });

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: guide.id,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const getCell = useCallback((content: ReactNode, className?: string) => {
    return (
      <Table.Cell className={clx("h-full  pr-0 relative group", className)}>
        <Link
          to={`/poradniki/${guide.id}`}
          className="size-full h-[inherit] max-h-[inherit] min-h-[inherit]  block pr-4 hover:underline"
        >
          {content}
        </Link>
      </Table.Cell>
    );
  }, []);

  return (
    <>
      <Table.Row key={guide.id} ref={setNodeRef} style={style}>
        <Table.Cell className="w-0 ">
          <Badge {...attributes} {...listeners} className="cursor-grab">
            <DotsSix className="text-ui-fg-subtle" />
            {guide.position}
          </Badge>
        </Table.Cell>
        {getCell(
          <ImagePreview image={guide.preview_image} alt={guide.name} />,
          "w-24"
        )}
        {getCell(guide.slug, "w-3/12")}
        {getCell(guide.is_published ? "Tak" : "Nie", "w-1/12")}
        {getCell(guide.name, "w-4/12")}
        {getCell(<CreatedAtBadge createdAt={guide.created_at} />, "w-2/12")}
        <Table.Cell>
          <DropdownMenu>
            <DropdownMenu.Trigger>
              <IconButton>
                <EllipsisVertical />
              </IconButton>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content>
              <DropdownMenu.Item asChild>
                <a
                  href={`${config.MEDUSA_STOREFRONT_URL}/${guide.slug}`}
                  target="_blank"
                >
                  Podgląd poradnika
                  <ArrowUpRightOnBox className="size-4 ml-2" />
                </a>
              </DropdownMenu.Item>
              <DropdownMenu.Item asChild>
                <a href={`/app/poradniki/${guide.id}`}>Edytuj</a>
              </DropdownMenu.Item>
              <DropdownMenu.Item
                disabled={isPending}
                onClick={() => {
                  setOpenPrompt(true);
                }}
                className="text-red-600"
              >
                Usuń
              </DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu>
        </Table.Cell>
      </Table.Row>

      <Prompt open={openPrompt} onOpenChange={setOpenPrompt}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>
              Czy na pewno chcesz usunąć ten poradnik?
            </Prompt.Title>
            <Prompt.Description>
              Po usunięciu poradnika nie będzie możliwości jego przywrócenia.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel>Anuluj</Prompt.Cancel>
            <Prompt.Action onClick={() => mutate(guide.id)}>Usuń</Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>
    </>
  );
});

export default GuideTableRow;
