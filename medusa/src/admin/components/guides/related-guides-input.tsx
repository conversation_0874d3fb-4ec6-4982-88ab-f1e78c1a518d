import { useState, useRef, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { QUERY_KEYS } from "../../lib/constants/query-keys";
import { getAllGuides } from "../../lib/api/guides";

type RelatedGuidesInputProps = {
  value: string[];
  onChange: (value: string[]) => void;
};

const RelatedGuidesInput = ({ value, onChange }: RelatedGuidesInputProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isOpen, setIsOpen] = useState(false);

  const { data: guides, isLoading } = useQuery({
    queryKey: [QUERY_KEYS.ALL_GUIDES],
    queryFn: getAllGuides,
  });

  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Filter guides based on search and already selected items
  const filteredGuides =
    guides?.filter(
      (guide) =>
        !value.includes(guide.id) &&
        guide.name.toLowerCase().includes(searchTerm.toLowerCase())
    ) || [];

  const handleSelect = (guideId: string) => {
    if (!value.includes(guideId)) {
      onChange([...value, guideId]);
      setIsOpen(false);
      setSearchTerm("");
    }
  };

  const handleRemove = (guideId: string) => {
    onChange(value.filter((id) => id !== guideId));
  };

  const renderDropdownContent = () => {
    if (isLoading) {
      return <div className="p-2 text-sm">Pobieranie poradników...</div>;
    }

    if (!filteredGuides.length) {
      return <div className="p-2 text-sm">Brak dostępnych poradników</div>;
    }

    return filteredGuides.map((guide) => (
      <button
        key={guide.id}
        onClick={() => handleSelect(guide.id)}
        className="w-full px-3 py-2 text-left hover:bg-ui-bg-base-hover flex items-center gap-x-2"
      >
        <img
          src={guide.preview_image}
          alt={guide.name}
          className="size-4 rounded-full"
        />
        <span className="text-sm block">{guide.name}</span>
      </button>
    ));
  };

  return (
    <div className="flex flex-col gap-y-2">
      <div className="flex flex-col gap-y-1">
        <div className="relative" ref={dropdownRef}>
          <input
            type="text"
            placeholder="Wyszukaj powiązane przewodniki spośród dotychczas dodanych"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onFocus={() => setIsOpen(true)}
            className="w-full px-3 py-2 bg-ui-bg-field border border-ui-border-base shadow-sm rounded-md text-sm"
          />
          {isOpen && (
            <div className="absolute z-50 w-full mt-1 bg-ui-bg-base border rounded-md shadow-lg max-h-[300px] overflow-y-auto">
              {renderDropdownContent()}
            </div>
          )}
        </div>
      </div>

      {/* Display selected guides */}
      {value.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-2">
          {value.map((guideId) => {
            const guide = guides?.find((g) => g.id === guideId);
            return guide ? (
              <div
                key={guideId}
                className="flex items-center gap-x-2 bg-ui-bg-base border border-ui-border-base px-3 py-1.5 rounded-lg"
              >
                <img
                  src={guide.preview_image}
                  alt={guide.name}
                  className="size-4 rounded-full"
                />
                <span className="text-sm">{guide.name}</span>
                <button
                  type="button"
                  onClick={() => handleRemove(guideId)}
                  className="text-ui-text-muted hover:text-ui-text-muted ml-1"
                >
                  ×
                </button>
              </div>
            ) : null;
          })}
        </div>
      )}
    </div>
  );
};

export default RelatedGuidesInput;
