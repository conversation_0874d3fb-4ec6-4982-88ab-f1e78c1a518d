import { InformationCircle } from "@medusajs/icons";
import { Heading, Label as U<PERSON>abel } from "@medusajs/ui";
import { PropsWithChildren, ReactNode } from "react";
import {
  Control,
  FieldValues,
  Path,
  useFormState,
  FieldError,
} from "react-hook-form";

type NestedObject = {
  [key: string]: NestedObject | string | undefined;
};

// Improved type for nested paths
type NestedPath<T> = T extends object
  ? {
      [K in keyof T]: K extends string
        ? T[K] extends object
          ? `${K}.${NestedPath<T[K]>}`
          : K
        : never;
    }[keyof T]
  : never;

type FormFieldProps<T extends FieldValues> = {
  control: Control<T>;
  name: Path<T>;
  className?: string;
};

// Helper function to safely get nested value
const getNestedValue = (
  obj: NestedObject,
  path: string
): string | undefined => {
  const parts = path.split(".");
  let current: any = obj;

  for (const part of parts) {
    if (current === undefined) return undefined;
    current = current[part];
  }

  return typeof current === "string" ? current : undefined;
};

// Helper to determine if an object is a FieldError
const isFieldError = (value: any): value is FieldError => {
  return value && typeof value === "object" && "message" in value;
};

const FormField = <T extends FieldValues>({
  control,
  name,
  children,
  className = "",
}: PropsWithChildren<FormFieldProps<T>>) => {
  const { errors } = useFormState({ control });

  const defaultClassName = "grid grid-rows-[1fr_auto] gap-2 [&>label:grow]";

  const getErrorMessage = (): string | null => {
    const pathParts = name.toString().split(".");
    let currentErrors: any = errors;

    // Handle deeply nested errors
    for (let i = 0; i < pathParts.length; i++) {
      const part = pathParts[i];
      if (!currentErrors || typeof currentErrors !== "object") return null;

      if (isFieldError(currentErrors[part])) {
        return currentErrors[part].message || null;
      }

      currentErrors = currentErrors[part];
    }

    // Handle case where error might be directly on the path
    if (isFieldError(currentErrors)) {
      return currentErrors.message || null;
    }

    return null;
  };

  const errorMessage = getErrorMessage();
  const hasError = Boolean(errorMessage);

  return (
    <div
      className={`
        ${defaultClassName}
        ${className}
        ${hasError ? "[&_h2]:text-red-600" : ""}
      `.trim()}
    >
      {children}
      {errorMessage && (
        <UILabel className="block mt-1 text-red-600">{errorMessage}</UILabel>
      )}
    </div>
  );
};

interface LabelProps {
  children: ReactNode;
  className?: string;
  required?: boolean;
}

const Label = ({ children, className = "", required = false }: LabelProps) => {
  return (
    <Heading level="h2" className={`${className} mb-2`.trim()}>
      {children}
      {required && <span className="text-red-600"> * </span>}
    </Heading>
  );
};

interface NoteProps {
  children: ReactNode;
  className?: string;
}

const Note = ({ children, className = "" }: NoteProps) => {
  return (
    <p className={`text-sm mb-2 text-gray-500 ${className}`.trim()}>
      <InformationCircle className="inline-block mr-2 mb-[3px]" />
      {children}
    </p>
  );
};

FormField.Label = Label;
FormField.Note = Note;

export default FormField;
