import {
  DndContext,
  KeyboardSensor,
  PointerSensor,
  rectIntersection,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { Table } from "@medusajs/ui";
import GuideTableRow from "../guide-table-row";
import { TGuidePost } from "../../../types";
import useUpdateGuidePosition from "./update-guide-position";
import { memo, useMemo } from "react";

type Props = {
  guides: TGuidePost[];
  refetch: () => void;
};

const GuidesTable = memo(({ guides, refetch }: Props) => {
  const { handleDragEnd } = useUpdateGuidePosition({
    guides,
    refetch,
  });

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const items = useMemo(() => guides.map((guide) => guide.id), [guides]);

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={rectIntersection}
      onDragEnd={handleDragEnd}
      autoScroll={{
        interval: 25,
        acceleration: 50,
        threshold: {
          y: 0.2,
          x: 0,
        },
      }}
    >
      <div className="overflow-x-auto w-full">
        <Table className="w-full">
          <Table.Header>
            <Table.Row>
              <Table.HeaderCell>Pozycja</Table.HeaderCell>
              <Table.HeaderCell>Miniaturka</Table.HeaderCell>
              <Table.HeaderCell>Slug</Table.HeaderCell>
              <Table.HeaderCell>Opublikowany</Table.HeaderCell>
              <Table.HeaderCell>Tytuł</Table.HeaderCell>
              <Table.HeaderCell>Stworzono</Table.HeaderCell>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            <SortableContext
              items={items}
              strategy={verticalListSortingStrategy}
            >
              {guides.map((guide) => (
                <GuideTableRow guide={guide} key={guide.id} refetch={refetch} />
              ))}
            </SortableContext>
          </Table.Body>
        </Table>
      </div>
    </DndContext>
  );
});

export default GuidesTable;
