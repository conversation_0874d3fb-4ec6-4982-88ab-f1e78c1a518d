import { TGuidePost } from "../../../types";
import { sdk } from "../../../lib/config";
import { DragEndEvent } from "@dnd-kit/core";
import { toast } from "@medusajs/ui";

type Props = {
  guides: TGuidePost[];
  refetch: () => void;
};

type UpdatePositionPayload = {
  id: string;
  position: number;
};

const useUpdateGuidePosition = ({ guides, refetch }: Props) => {
  const updatePositions = async (
    guides: TGuidePost[],
    activeId: string,
    overId: string
  ) => {
    try {
      // Find the items that are being reordered
      const oldIndex = guides.findIndex((guide) => guide.id === activeId);
      const newIndex = guides.findIndex((guide) => guide.id === overId);

      if (oldIndex === -1 || newIndex === -1) return;

      // Create a new array with the reordered items
      const newGuides = [...guides];
      const [reorderedItem] = newGuides.splice(oldIndex, 1);
      newGuides.splice(newIndex, 0, reorderedItem);

      // Update positions for all affected items
      const updatedPositions: UpdatePositionPayload[] = newGuides.map(
        (guide, index) => ({
          id: guide.id,
          position: index,
        })
      );

      // Make the API call to update positions
      await sdk.client.fetch("/admin/guides/positions", {
        method: "PUT",
        body: {
          positions: updatedPositions,
        },
      });

      return newGuides;
    } catch (error) {
      console.error("Error updating guide positions:", error);
      throw error;
    }
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;

    try {
      const newGuides = await updatePositions(
        guides,
        active.id as string,
        over.id as string
      );
      if (newGuides) {
        refetch();
        toast.success("Pomyślnie zaktualizowano kolejność poradników");
      }
    } catch (error) {
      console.error("Error reordering guides:", error);
      toast.error("Wystąpił błąd podczas aktualizacji kolejności poradników");
    }
  };

  return { updatePositions, handleDragEnd };
};

export default useUpdateGuidePosition;
