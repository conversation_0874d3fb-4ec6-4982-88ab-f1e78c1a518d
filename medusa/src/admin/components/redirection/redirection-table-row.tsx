import {
  QueryObserverResult,
  RefetchOptions,
  useMutation,
} from "@tanstack/react-query";
import { TRedirection } from "../../types";
import { useState } from "react";
import { deleteRedirection } from "../../lib/api/redirection";
import { DropdownMenu, IconButton, Prompt, Table, toast } from "@medusajs/ui";
import { EllipsisVertical } from "@medusajs/icons";
import CreatedAtBadge from "../reusable/created-at-badge";

interface IProps {
  redirection: TRedirection;
  refetch: (
    options?: RefetchOptions
  ) => Promise<QueryObserverResult<TRedirection[], Error>>;
}

const RedirectionTableRow = ({ redirection, refetch }: IProps) => {
  const [openPrompt, setOpenPrompt] = useState(false);
  const { isPending, mutate } = useMutation({
    mutationFn: deleteRedirection,
    onSuccess: () => {
      toast.success(
        `Przekierowanie - ${redirection.from_path} zostało usunięte`
      );
      refetch();
    },
  });
  return (
    <>
      <Table.Row key={redirection.id}>
        <Table.Cell>{redirection.from_path}</Table.Cell>
        <Table.Cell>{redirection.to_path}</Table.Cell>
        <Table.Cell>{redirection.status_code}</Table.Cell>
        <Table.Cell>
          <CreatedAtBadge createdAt={redirection.created_at} />
        </Table.Cell>
        <Table.Cell>
          <DropdownMenu>
            <DropdownMenu.Trigger>
              <IconButton>
                <EllipsisVertical />
              </IconButton>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content>
              <DropdownMenu.Item asChild>
                <a href={`/app/przekierowania/${redirection.id}`}>Edytuj</a>
              </DropdownMenu.Item>
              <DropdownMenu.Item
                disabled={isPending}
                onClick={() => {
                  setOpenPrompt(true);
                }}
                className="text-red-600"
              >
                Usuń
              </DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu>
        </Table.Cell>
      </Table.Row>

      <Prompt open={openPrompt} onOpenChange={setOpenPrompt}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>
              Czy na pewno chcesz usunąć to przekierowanie?
            </Prompt.Title>
            <Prompt.Description>
              Po usunięciu przekierowania nie będzie możliwości jego
              przywrócenia.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel>Anuluj</Prompt.Cancel>
            <Prompt.Action onClick={() => mutate(redirection.id)}>
              Usuń
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>
    </>
  );
};

export default RedirectionTableRow;
