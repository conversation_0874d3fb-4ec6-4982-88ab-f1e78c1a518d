import { FieldErrors, useForm } from "react-hook-form";
import { redirectionSchemas, TCreateRedirectionFormBody } from "./schemas";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import {
  createRedirection,
  updateRedirection,
} from "../../lib/api/redirection";
import {
  Button,
  Container,
  FocusModal,
  Input,
  toast,
  Toaster,
} from "@medusajs/ui";
import FormField from "../guides/form-field";
import { Spinner } from "@medusajs/icons";
import { MedusaError } from "@medusajs/framework/utils";

interface IProps {
  initialValues?: TCreateRedirectionFormBody;
  editMode?: boolean;
  idToEdit?: string;
  onSuccessfulSubmit?: () => void;
}

export const DEFAULT_REDIRECTION_VALUES: TCreateRedirectionFormBody = {
  from_path: "",
  status_code: 301,
  to_path: "",
};

const RedirectionForm = ({
  editMode,
  idToEdit,
  initialValues,
  onSuccessfulSubmit,
}: IProps) => {
  const defaultValues: TCreateRedirectionFormBody =
    initialValues ?? DEFAULT_REDIRECTION_VALUES;

  const form = useForm<typeof defaultValues>({
    defaultValues: defaultValues,
    resolver: zodResolver(redirectionSchemas.createRedirectionFormBody),
  });

  const { mutate: triggerRedirectionCreationSubmit, isPending } = useMutation({
    mutationFn: async (data: TCreateRedirectionFormBody) => {
      if (editMode) {
        return await updateRedirection(idToEdit!, {
          ...data,
          from_path: data?.from_path?.trim(),
          to_path: data?.to_path?.trim(),
        });
      }

      return await createRedirection(data);
    },
    onSuccess: () => {
      onSuccessfulSubmit?.();

      if (editMode) {
        window.location.href = "/app/przekierowania?successfully_edited=true";
      } else {
        toast.success("Przekierowanie utworzona");
        form.reset(defaultValues);
      }
    },
    onError: (error: MedusaError) => {
      console.error(error);
      if (editMode) {
        return toast.error(
          `Wystąpił błąd podczas aktualizacji przekierowania - ${error.message}`
        );
      } else {
        return toast.error(
          `Wystąpił błąd podczas tworzenia przekierowania - ${error.message}`
        );
      }
    },
  });

  const onSubmit = (data: TCreateRedirectionFormBody) => {
    triggerRedirectionCreationSubmit(data);
  };

  const onError = (errors: FieldErrors<TCreateRedirectionFormBody>) => {
    const fieldTranslations: Record<string, string> = {
      from_path: "Z adresu",
      to_path: "Na adres",
      status_code: "Kod przekierowania",
    };

    const errorFields = Object.keys(errors)
      .map((field) => fieldTranslations[field] || field)
      .join(", ");

    toast.error(
      `Wystąpiły błędy w polach: ${errorFields}. Sprawdź i spróbuj ponownie.`
    );
    console.error(errors);
  };

  return (
    <>
      <Toaster />

      <Container
        className={`flex flex-col gap-4 rounded-none max-w-screen-lg mx-auto ${
          editMode ? "rounded-md" : ""
        }`}
      >
        <FormField control={form.control} name="from_path">
          <FormField.Label>Z adresu</FormField.Label>
          <Input {...form.register("from_path")} />
        </FormField>

        <FormField control={form.control} name="to_path">
          <FormField.Label>Na adres</FormField.Label>
          <Input {...form.register("to_path")} />
        </FormField>

        <FormField control={form.control} name="status_code">
          <FormField.Label>Kod przekierowania</FormField.Label>
          <Input
            type="number"
            {...form.register("status_code", { valueAsNumber: true })}
          />
        </FormField>

        <div className="h-[80vh]" />
      </Container>

      {editMode ? (
        <div className="flex p-4 sticky bottom-0 bg-ui-bg-base border w-full justify-end  rounded-md ">
          <Button
            className="self-end"
            onClick={form.handleSubmit(onSubmit, onError)}
          >
            {isPending ? (
              <Spinner className="animate-spin" />
            ) : (
              "Aktualizuj przekierowanie"
            )}
          </Button>
        </div>
      ) : (
        <FocusModal.Footer className="sticky bottom-0 bg-ui-bg-base">
          <Button
            className="self-end"
            onClick={form.handleSubmit(onSubmit, onError)}
            disabled={isPending}
          >
            {isPending ? (
              <Spinner className="animate-spin" />
            ) : (
              "Stwórz nowe przekierowanie"
            )}
          </Button>
        </FocusModal.Footer>
      )}
    </>
  );
};

export default RedirectionForm;
