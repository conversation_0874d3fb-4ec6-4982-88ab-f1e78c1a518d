import { Text, clx } from "@medusajs/ui";

export type SectionRowProps = {
  title: string;
  distinct?: boolean;
  value?: React.ReactNode | string | null;
  actions?: React.ReactNode;
};

export const SectionRow = ({
  title,
  value,
  actions,
  distinct,
}: SectionRowProps) => {
  const isValueString = typeof value === "string" || !value;

  return (
    <div
      className={clx(
        `text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4`,
        {
          "grid-cols-[1fr_1fr_28px]": !!actions,
        }
      )}
    >
      <Text
        size="small"
        weight="plus"
        leading="compact"
        className={distinct ? "text-ui-fg-base" : ""}
      >
        {title}
      </Text>

      {isValueString ? (
        <Text
          size="small"
          leading="compact"
          className={
            distinct
              ? "text-ui-fg-base font-medium"
              : "whitespace-pre-line text-pretty"
          }
        >
          {value ?? "-"}
        </Text>
      ) : (
        <div
          className={clx("flex flex-wrap gap-1 text-sm", {
            "text-ui-fg-base font-medium": distinct,
          })}
        >
          {value}
        </div>
      )}

      {actions && <div>{actions}</div>}
    </div>
  );
};
