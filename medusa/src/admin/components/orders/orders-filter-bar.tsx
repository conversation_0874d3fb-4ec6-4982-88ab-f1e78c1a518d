import { Button, Input, Select, DatePicker } from "@medusajs/ui";
import { OrderFilters } from "../../hooks/use-order-filtering";
import { CustomColumn } from "./types";
import {
  OrderStatus,
  OrderStatusEnum,
  OrderStatusTranslationMap,
} from "../../widgets/order-status/types";
import { MagnifyingGlass } from "@medusajs/icons";
import { formatDate } from "../../lib/util/string-dates";

type OrdersFilterBarProps = {
  filters: OrderFilters;
  activeFilters: string[];
  orderNumberSearch: string;
  dateFrom: Date | null;
  dateTo: Date | null;
  setOrderNumberSearch: (value: string) => void;
  handleFilterChange: (key: string, value: any) => void;
  clearFilter: (key: string) => void;
  clearAllFilters: () => void;
  handleOrderNumberSearch: () => void;
  handleDateRangeChange: (fromDate: Date | null, toDate: Date | null) => void;
  columns: CustomColumn[];
};

export const OrdersFilterBar = ({
  filters,
  activeFilters,
  orderNumberSearch,
  dateFrom,
  dateTo,
  setOrderNumberSearch,
  handleFilterChange,
  clearFilter,
  clearAllFilters,
  handleOrderNumberSearch,
  handleDateRangeChange,
}: OrdersFilterBarProps) => {
  return (
    <div className="mb-4 p-4 bg-ui-bg-base rounded-lg shadow-elevation-card-rest">
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <h3 className="text-ui-fg-base font-medium">Filtry</h3>
          {activeFilters.length > 0 && (
            <Button variant="secondary" size="small" onClick={clearAllFilters}>
              Wyczyść wszystkie
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Order number search */}
          <div className="w-full grow">
            <label className="text-ui-fg-subtle text-xs mb-1 block">
              Numer zamówienia
            </label>
            <div className="flex gap-2 grow w-full [&>div]:w-full">
              <Input
                placeholder="Wyszukaj numer zamówienia"
                value={orderNumberSearch}
                onChange={(e) => setOrderNumberSearch(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleOrderNumberSearch();
                  }
                }}
              />
              <Button variant="secondary" onClick={handleOrderNumberSearch}>
                <MagnifyingGlass />
              </Button>
            </div>
          </div>

          {/* Status filter */}
          <div>
            <label className="text-ui-fg-subtle text-xs mb-1 block">
              Status
            </label>
            <Select
              value={filters["metadata.status"] || ""}
              onValueChange={(value) => {
                if (value === "all") {
                  clearFilter("metadata.status");
                } else {
                  if (Object.values(OrderStatusEnum).includes(value as any)) {
                    handleFilterChange("metadata.status", value as OrderStatus);
                  }
                }
              }}
            >
              <Select.Trigger className="w-full">
                <Select.Value placeholder="Wybierz status" />
              </Select.Trigger>
              <Select.Content className="max-h-[300px] overflow-y-scroll">
                <Select.Item value="all">Wszystkie</Select.Item>
                {Object.entries(OrderStatusTranslationMap).map(
                  ([value, label]) => (
                    <Select.Item key={value} value={value}>
                      {label}
                    </Select.Item>
                  )
                )}
              </Select.Content>
            </Select>
          </div>

          {/* Attraction name filter */}
          <div>
            <label className="text-ui-fg-subtle text-xs mb-1 block">
              Nazwa atrakcji
            </label>
            <Input
              placeholder="Szukaj według nazwy atrakcji"
              value={filters["ticket.attraction_name"] || ""}
              onChange={(e) =>
                handleFilterChange("ticket.attraction_name", e.target.value)
              }
            />
          </div>

          {/* Customer name filter */}
          <div>
            <label className="text-ui-fg-subtle text-xs mb-1 block">
              Klient
            </label>
            <Input
              placeholder="Szukaj według imienia lub nazwiska klienta"
              value={filters["customer.name"] || ""}
              onChange={(e) =>
                handleFilterChange("customer.name", e.target.value)
              }
            />
          </div>

          {/* Date range for attraction date */}
          <div className="col-span-1 md:col-span-2 flex gap-4">
            <div className="grow">
              <label className="text-ui-fg-subtle text-xs mb-1 block">
                Data atrakcji od
              </label>

              <div className="flex-1">
                <label className="sr-only">Data od</label>
                <DatePicker
                  onChange={(date) => handleDateRangeChange(date, dateTo)}
                  value={dateFrom ? new Date(formatDate(dateFrom)) : null}
                />
              </div>
            </div>

            <div className="grow">
              <label className="text-ui-fg-subtle text-xs mb-1 block">
                Data atrakcji do
              </label>
              <div className="flex-1">
                <label className="sr-only">Data do</label>
                <DatePicker
                  onChange={(date) => handleDateRangeChange(dateFrom, date)}
                  value={dateTo ? new Date(formatDate(dateTo)) : null}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
