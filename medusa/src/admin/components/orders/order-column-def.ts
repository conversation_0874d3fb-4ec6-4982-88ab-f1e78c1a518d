import {
  OrderStatusTranslationMap,
  OrderStatus,
} from "../../widgets/order-status/types";
import { CustomColumn } from "./types";

export const getOrderColumns = (): CustomColumn[] => {
  return [
    {
      key: "display_id",
      label: "Numer",
      filterable: true,
      filterType: "text",
    },
    {
      key: "customer.name",
      label: "Klient",
      filterable: true,
      filterType: "text",
    },
    {
      key: "created_at",
      label: "Data utworzenia",
      filterable: false,
    },
    {
      key: "metadata.status",
      label: "Status",
      filterable: true,
      filterType: "select",
      filterOptions: Object.keys(OrderStatusTranslationMap).map((status) => ({
        value: status,
        label: OrderStatusTranslationMap[status as OrderStatus],
      })),
    },
    {
      key: "calculated_pln_amount",
      label: "Wartość zaliczki",
      filterable: false,
    },
    {
      key: "ticket.attraction_name",
      label: "Nazwa atrakcji",
      filterable: true,
      filterType: "text",
    },
    {
      key: "ticket.selected_date",
      label: "Data atrakcji",
      filterable: true,
      filterType: "text",
    },
    {
      key: "items.quantity",
      label: "Liczba osób",
      filterable: false,
    },
  ];
};
