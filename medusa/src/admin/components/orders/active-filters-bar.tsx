import { <PERSON><PERSON>, <PERSON><PERSON> } from "@medusajs/ui";
import { OrderFilters } from "../../hooks/use-order-filtering";
import { CustomColumn } from "./types";
import { formatDate } from "../../lib/util/string-dates";
import { XMark } from "@medusajs/icons";

type ActiveFiltersBarProps = {
  activeFilters: string[];
  filters: OrderFilters;
  columns: CustomColumn[];
  clearFilter: (key: string) => void;
  clearAllFilters: () => void;
};

export const ActiveFiltersBar = ({
  activeFilters,
  filters,
  columns,
  clearFilter,
  clearAllFilters,
}: ActiveFiltersBarProps) => {
  if (activeFilters.length === 0) return null;

  return (
    <div className="mb-4 p-4 bg-ui-bg-base rounded-lg shadow-elevation-card-rest">
      <div className="flex items-center justify-between">
        <div className="text-ui-fg-base font-medium">
          Aktywne filtry: {activeFilters.length}
        </div>
        <Button variant="secondary" size="small" onClick={clearAllFilters}>
          W<PERSON><PERSON><PERSON>ść wszystkie
        </Button>
      </div>
      <div className="flex flex-wrap gap-2 mt-2">
        {activeFilters.map((filterKey) => {
          const column = columns.find((col) => col.key === filterKey);
          let displayLabel = column?.label || filterKey;
          let value = filters[filterKey as keyof OrderFilters];
          let displayValue = value;

          // Special handling for date ranges
          if (filterKey === "ticket.selected_date_from") {
            displayLabel = "Data atrakcji od";

            // add one day to the date - Offset for the date picker and time zone differences
            const date = new Date(value || "");
            date.setDate(date.getDate() + 1);
            displayValue = formatDate(date.toISOString().split("T")[0]);
          } else if (filterKey === "ticket.selected_date_to") {
            displayLabel = "Data atrakcji do";

            // add one day to the date - Offset for the date picker and time zone differences
            const date = new Date(value || "");
            date.setDate(date.getDate() + 1);
            displayValue = formatDate(date.toISOString().split("T")[0]);
          } else if (filterKey === "customer.name") {
            displayLabel = "Klient";
            displayValue = value;
          } else if (column?.filterType === "select" && column.filterOptions) {
            displayValue =
              column.filterOptions.find((opt) => opt.value === value)?.label ||
              value;
          }

          return (
            <Badge
              key={filterKey}
              className="px-2 py-1 flex items-center gap-1"
            >
              <span>
                {displayLabel}: {displayValue}
              </span>
              <button onClick={() => clearFilter(filterKey)}>
                <XMark className="h-3 w-3" />
              </button>
            </Badge>
          );
        })}
      </div>
    </div>
  );
};
