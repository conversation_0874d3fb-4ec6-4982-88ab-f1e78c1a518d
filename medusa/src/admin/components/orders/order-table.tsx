import { Table as UiTable } from "@medusajs/ui";
import { OrderWithRelations } from "../../types";
import { OrderDTO } from "@medusajs/types";
import { Link } from "react-router-dom";
import { ReactNode, useCallback } from "react";
import { CloneDashed } from "@medusajs/icons";
import CreatedAtBadge from "../../components/reusable/created-at-badge";
import { Badge } from "@medusajs/ui";
import {
  OrderStatusColorMap,
  OrderStatusTranslationMap,
} from "../../widgets/order-status/types";
import { formatDate } from "../../lib/util/string-dates";
import { CustomColumn } from "./types";
import { ColumnHeader } from "./column-header";
import { OrderFilters } from "../../hooks/use-order-filtering";
import { Table } from "../table";

type OrderTableProps = {
  data: OrderWithRelations[];
  columns: CustomColumn[];
  pageSize: number;
  count: number;
  currentPage: number;
  setCurrentPage: (page: number) => void;
  filters: OrderFilters;
  activeFilters: string[];
  orderBy: string;
  orderDirection: "ASC" | "DESC";
  handleSort: (key: string) => void;
  handleFilterChange: (key: string, value: any) => void;
  clearFilter: (key: string) => void;
  isLoading?: boolean;
};

export const OrderTable = ({
  data,
  columns,
  pageSize,
  count,
  currentPage,
  setCurrentPage,
  filters,
  activeFilters,
  orderBy,
  orderDirection,
  handleSort,
  handleFilterChange,
  clearFilter,
  isLoading = false,
}: OrderTableProps) => {
  const copyToClipboard = useCallback((text: string) => {
    try {
      navigator.clipboard.writeText(text);
      // Use a toast notification here if you have one
      alert("Skopiowano do schowka - " + text);
    } catch (error) {
      alert("Nie udało się skopiować do schowka");
    }
  }, []);

  const getCell = useCallback(
    (
      content: ReactNode,
      item: OrderDTO,
      copyOnClick?: boolean,
      copyContent?: string
    ) => {
      const linkClassName = "w-full h-full flex items-center pr-4";

      return (
        <Link to={`${item.id}`} className={linkClassName}>
          {content}
          {copyOnClick && (
            <button
              className="underline ml-2 group"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                copyToClipboard(copyContent || item.id);
              }}
            >
              <CloneDashed className="group-hover:text-ui-fg-base group-hover:-translate-y-px transition-transform" />
            </button>
          )}
        </Link>
      );
    },
    [copyToClipboard]
  );

  const getPLNTransactionAmount = useCallback((item: OrderWithRelations) => {
    // Use the server-calculated value if available
    if (
      item.calculated_pln_amount !== undefined &&
      item.calculated_pln_amount !== null
    ) {
      return `${item.calculated_pln_amount} PLN`;
    }

    // Fallback to client-side calculation if server value is not available
    if (!item.summary?.transaction_total || !item.metadata?.euro_rate)
      return "-";

    return (
      (item.summary.transaction_total * item.metadata?.euro_rate).toFixed(2) +
      " PLN"
    );
  }, []);

  // Transform the columns for the Table component
  const tableColumns = columns.map((column) => ({
    key: column.key,
    label: column.label,
    headerRender: () => (
      <ColumnHeader
        column={column}
        orderBy={orderBy}
        orderDirection={orderDirection}
        filters={filters}
        activeFilters={activeFilters}
        handleSort={handleSort}
        handleFilterChange={handleFilterChange}
        clearFilter={clearFilter}
      />
    ),
  }));

  if (isLoading) {
    // Generate a skeleton table UI while loading
    return (
      <div className="flex flex-col !border-t-0">
        <div className="overflow-x-auto w-full">
          <UiTable>
            <UiTable.Header>
              <UiTable.Row>
                {columns.map((column, index) => (
                  <UiTable.HeaderCell key={index}>
                    {column.label || column.key}
                  </UiTable.HeaderCell>
                ))}
              </UiTable.Row>
            </UiTable.Header>
            <UiTable.Body>
              {Array.from({ length: 5 }).map((_, rowIndex) => (
                <UiTable.Row key={`skeleton-row-${rowIndex}`}>
                  {columns.map((_, cellIndex) => (
                    <UiTable.Cell
                      key={`skeleton-cell-${rowIndex}-${cellIndex}`}
                    >
                      <div className="animate-pulse flex items-center">
                        <div className="h-4 bg-ui-bg-subtle rounded w-3/4"></div>
                      </div>
                    </UiTable.Cell>
                  ))}
                </UiTable.Row>
              ))}
            </UiTable.Body>
          </UiTable>
        </div>
        <div className="flex justify-between items-center mt-4 px-4">
          <div className="animate-pulse h-8 bg-ui-bg-subtle rounded w-32"></div>
          <div className="flex gap-2">
            <div className="animate-pulse h-8 bg-ui-bg-subtle rounded w-8"></div>
            <div className="animate-pulse h-8 bg-ui-bg-subtle rounded w-8"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return <div className="flex justify-center p-6">Brak danych</div>;
  }

  return (
    <Table<OrderWithRelations>
      columns={tableColumns}
      data={data}
      pageSize={pageSize}
      count={count}
      currentPage={currentPage}
      setCurrentPage={setCurrentPage}
      linkHref={(item) => `${item.id}`}
      rowComponent={(item) => (
        <UiTable.Row>
          <UiTable.Cell className="w-auto pr-0">
            {getCell(item.display_id, item)}
          </UiTable.Cell>

          <UiTable.Cell className="pr-0">
            {getCell(
              <div className="flex items-center gap-2">
                <span>{item.customer?.first_name || "-"}</span>
                <span>{item.customer?.last_name || "-"}</span>
              </div>,
              item
            )}
          </UiTable.Cell>

          <UiTable.Cell className="pr-0">
            {getCell(
              <CreatedAtBadge createdAt={item.created_at as string} withTime />,
              item
            )}
          </UiTable.Cell>

          <UiTable.Cell className="pr-0">
            {getCell(
              item.metadata?.status ? (
                <Badge
                  color={OrderStatusColorMap[item.metadata?.status]}
                  className="whitespace-nowrap"
                >
                  {OrderStatusTranslationMap[item.metadata?.status]}
                </Badge>
              ) : (
                "-"
              ),
              item
            )}
          </UiTable.Cell>

          <UiTable.Cell className="pr-0">
            {getCell(
              <div className="flex items-center gap-2">
                <span className="whitespace-nowrap">
                  {getPLNTransactionAmount(item) || "-"}
                </span>
              </div>,
              item
            )}
          </UiTable.Cell>

          <UiTable.Cell className="w-4/12 pr-0">
            {getCell(item?.ticket?.attraction_name || "-", item)}
          </UiTable.Cell>
          <UiTable.Cell className="whitespace-nowrap pr-0">
            {getCell(
              item?.ticket?.selected_date
                ? formatDate(item?.ticket?.selected_date)
                : "-",
              item
            )}
          </UiTable.Cell>
          <UiTable.Cell className="whitespace-nowrap ">
            {getCell(
              item?.items?.reduce(
                (acc, orderItem) => acc + (orderItem?.quantity || 0),
                0
              ) || "-",
              item
            )}
          </UiTable.Cell>
        </UiTable.Row>
      )}
    />
  );
};
