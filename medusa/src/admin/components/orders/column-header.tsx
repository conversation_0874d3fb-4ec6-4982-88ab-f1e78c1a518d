import { ReactNode } from "react";
import { CustomColumn } from "./types";
import { OrderFilters } from "../../hooks/use-order-filtering";
import { ArrowDown } from "@medusajs/icons";

type ColumnHeaderProps = {
  column: CustomColumn;
  orderBy: string;
  orderDirection: "ASC" | "DESC";
  filters: OrderFilters;
  activeFilters: string[];
  handleSort: (key: string) => void;
  handleFilterChange: (key: string, value: any) => void;
  clearFilter: (key: string) => void;
};

export const ColumnHeader = ({
  column,
  orderBy,
  orderDirection,
  handleSort,
}: ColumnHeaderProps) => {
  const renderSortIcon = (): ReactNode => {
    if (orderBy !== column.key) return null;
    return orderDirection === "ASC" ? (
      <ArrowDown className="rotate-180" />
    ) : (
      <ArrowDown />
    );
  };

  return (
    <div className="flex items-center">
      <button
        className="flex items-center  cursor-pointer"
        onClick={() => handleSort(column.key)}
      >
        {column.label}
        {renderSortIcon()}
      </button>
    </div>
  );
};
