import { useMemo } from "react";
import { Table as UiTable } from "@medusajs/ui";
import { Link } from "react-router-dom";

export type TableProps<T> = {
  columns: {
    key: string;
    label?: string;
    render?: (value: unknown) => React.ReactNode;
    headerRender?: () => React.ReactNode;
  }[];
  data: T[];
  pageSize: number;
  count: number;
  currentPage: number;
  rowComponent?: (item: T) => React.ReactNode;
  setCurrentPage: (value: number) => void;
  rowProps?: (item: T) => React.HTMLAttributes<HTMLTableRowElement>;
  linkHref?: (item: T) => string;
};

export const Table = <T,>({
  columns,
  data,
  pageSize,
  count,
  linkHref,
  rowProps,
  currentPage,
  setCurrentPage,
  rowComponent,
}: TableProps<T>) => {
  const pageCount = useMemo(() => {
    return Math.ceil(count / pageSize);
  }, [data, pageSize]);

  const canNextPage = useMemo(() => {
    return currentPage < pageCount - 1;
  }, [currentPage, pageCount]);
  const canPreviousPage = useMemo(() => {
    return currentPage - 1 >= 0;
  }, [currentPage]);

  const nextPage = () => {
    if (canNextPage) {
      setCurrentPage(currentPage + 1);
    }
  };

  const previousPage = () => {
    if (canPreviousPage) {
      setCurrentPage(currentPage - 1);
    }
  };

  return (
    <div className="flex flex-col  !border-t-0">
      <div className="overflow-x-auto w-full">
        <UiTable>
          <UiTable.Header>
            <UiTable.Row>
              {columns.map((column, index) => (
                <UiTable.HeaderCell key={index}>
                  {column.headerRender
                    ? column.headerRender()
                    : column.label || column.key}
                </UiTable.HeaderCell>
              ))}
            </UiTable.Row>
          </UiTable.Header>
          <UiTable.Body>
            {data.map((item, index) => {
              const rowIndex =
                typeof item === "object" && item !== null && "id" in item
                  ? (item.id as string)
                  : index;
              return (
                <>
                  {rowComponent?.(item) ?? (
                    <UiTable.Row key={rowIndex} {...rowProps?.(item)}>
                      {columns.map((column, index) => {
                        // Retrieve value from nested path if needed
                        const getValue = (obj: any, path: string) => {
                          const keys = path.split(".");
                          let value: unknown = obj;

                          for (const key of keys) {
                            if (
                              value === null ||
                              value === undefined ||
                              typeof value !== "object"
                            ) {
                              return undefined;
                            }
                            value = (value as Record<string, unknown>)[key];
                          }

                          return value;
                        };

                        const value = getValue(item, column.key);

                        return (
                          <UiTable.Cell key={`${rowIndex}-${index}`}>
                            <Link
                              to={linkHref?.(item) || ""}
                              className="grow flex items-center w-full h-full"
                            >
                              <>
                                {column.render && column.render(value)}
                                {!column.render && (
                                  <>
                                    {value !== undefined ? String(value) : "-"}
                                  </>
                                )}
                              </>
                            </Link>
                          </UiTable.Cell>
                        );
                      })}
                    </UiTable.Row>
                  )}
                </>
              );
            })}
          </UiTable.Body>
        </UiTable>
      </div>

      <UiTable.Pagination
        count={count}
        pageSize={pageSize}
        pageIndex={currentPage}
        pageCount={pageCount}
        canPreviousPage={canPreviousPage}
        canNextPage={canNextPage}
        previousPage={previousPage}
        nextPage={nextPage}
      />
    </div>
  );
};
