import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { Button } from "@medusajs/ui";
import "./styles.css";
import Dropcursor from "@tiptap/extension-dropcursor";
import Image from "@tiptap/extension-image";
import AddImageModule from "./add-image-module";
import LinkModule, { LINK_CONFIGURE_OPTIONS } from "./link-module";
import Link from "@tiptap/extension-link";
import { useEffect, useRef, useState } from "react";
import { ChevronDown, Trash } from "@medusajs/icons";
import Color from "@tiptap/extension-color";
import TextStyle from "@tiptap/extension-text-style";
import ColorInputModule from "./color-input";
import { TabHandler } from "./tab-extension";
import DeleteWithConfirmation from "../reusable/delete-with-confirmation";

interface RichTextEditorProps {
  content: string | null;
  onChange: (content: string) => void;
  includeClearButton?: boolean;
  compressed_version?: boolean;
}

const RichTextEditor = ({
  content,
  onChange,
  includeClearButton = true,
  compressed_version = false,
}: RichTextEditorProps) => {
  const [expanded, setExpanded] = useState(false);

  const isExpanded = !compressed_version || (compressed_version && expanded);

  const editor = useEditor({
    extensions: [
      StarterKit,
      Dropcursor,
      Image,
      Color,
      TextStyle,
      Link.configure(LINK_CONFIGURE_OPTIONS),
      TabHandler,
    ],
    content,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
  });

  const editorContainerRef = useRef<HTMLDivElement>(null);
  const onceFlag = useRef(true);

  if (!editor) {
    return null;
  }

  const handleContainerClick = () => {
    if (!editor?.isFocused) {
      editor.commands.focus();
    }
  };

  const handleClearContent = () => {
    editor.commands.setContent("");
    onChange("");
  };

  useEffect(() => {
    if (content && onceFlag.current) {
      editor.commands.setContent(content);
      onceFlag.current = false;
    }
  }, [content]);

  return (
    <div className="rich-text-editor max-w-screen-lg mx-auto w-full grow">
      <div className="editor-toolbar flex flex-wrap max-sm:[&_button]:p-1">
        <Button
          variant="secondary"
          size="small"
          onClick={() => editor.chain().focus().toggleBold().run()}
          className={editor.isActive("bold") ? "is-active" : ""}
        >
          Bold
        </Button>
        <Button
          variant="secondary"
          size="small"
          onClick={() => editor.chain().focus().toggleItalic().run()}
          className={editor.isActive("italic") ? "is-active" : ""}
        >
          Italic
        </Button>
        <Button
          variant="secondary"
          size="small"
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          className={editor.isActive("bulletList") ? "is-active" : ""}
        >
          Bullet List
        </Button>
        <Button
          variant="secondary"
          size="small"
          className={
            editor.isActive("heading", { level: 1 }) ? "is-active" : ""
          }
          onClick={() =>
            editor.chain().focus().toggleHeading({ level: 1 }).run()
          }
        >
          H1
        </Button>
        <Button
          variant="secondary"
          className={
            editor.isActive("heading", { level: 2 }) ? "is-active" : ""
          }
          onClick={() =>
            editor.chain().focus().toggleHeading({ level: 2 }).run()
          }
        >
          H2
        </Button>
        <Button
          variant="secondary"
          className={
            editor.isActive("heading", { level: 3 }) ? "is-active" : ""
          }
          onClick={() =>
            editor.chain().focus().toggleHeading({ level: 3 }).run()
          }
        >
          H3
        </Button>
        {isExpanded && (
          <>
            <Button
              variant="secondary"
              className={
                editor.isActive("heading", { level: 4 }) ? "is-active" : ""
              }
              onClick={() =>
                editor.chain().focus().toggleHeading({ level: 4 }).run()
              }
            >
              H4
            </Button>
            <Button
              variant="secondary"
              className={
                editor.isActive("heading", { level: 5 }) ? "is-active" : ""
              }
              onClick={() =>
                editor.chain().focus().toggleHeading({ level: 5 }).run()
              }
            >
              H5
            </Button>
            <Button
              variant="secondary"
              className={
                editor.isActive("heading", { level: 6 }) ? "is-active" : ""
              }
              onClick={() =>
                editor.chain().focus().toggleHeading({ level: 6 }).run()
              }
            >
              H6
            </Button>
          </>
        )}

        {isExpanded && (
          <>
            {!compressed_version && (
              <div className="w-px  border-r h-4 m-auto mx-4" />
            )}
            <LinkModule editor={editor} />

            <div className="grow" />

            <Button
              onClick={() => editor.chain().focus().setHorizontalRule().run()}
            >
              Dodaj separator
            </Button>

            <AddImageModule editor={editor} />
            <ColorInputModule editor={editor} />
          </>
        )}

        {compressed_version ? (
          isExpanded ? (
            <Button
              className="ml-auto"
              size="small"
              onClick={() => setExpanded(false)}
            >
              <ChevronDown className="size-4 rotate-180" />
              Zwiń
            </Button>
          ) : (
            <Button
              className="ml-auto"
              size="small"
              onClick={() => setExpanded(true)}
            >
              <ChevronDown className="size-4" />
              Rozwiń
            </Button>
          )
        ) : null}
      </div>
      <div
        className={`max-h-[calc(100vh-200px)] blog overflow-y-auto  ${
          compressed_version ? "max-h-[150px]" : "min-h-[150px]"
        }`}
        ref={editorContainerRef}
        onClick={handleContainerClick}
      >
        <EditorContent editor={editor} />
      </div>
      {includeClearButton && (
        <DeleteWithConfirmation
          onDelete={handleClearContent}
          customPromptContent={{
            title: "Wyczyść",
            description:
              "Czy jesteś pewny, że chcesz usunąć zawartość pola tekstowego?",
          }}
        >
          <Button size="small" className="m-2" variant="secondary">
            <Trash className="size-4" />
          </Button>
        </DeleteWithConfirmation>
      )}
    </div>
  );
};

export default RichTextEditor;
