import { useCallback } from "react";
import { Button, FocusModal, useToggleState } from "@medusajs/ui";
import { Editor } from "@tiptap/react";
import ImageDropzone from "../reusable/image_dropzone";

type Props = {
  editor: Editor;
  onSuccessfulUpload?: (url: string) => void;
};

const AddImageModule = ({ onSuccessfulUpload, editor }: Props) => {
  const [isOpen, open, close, toggle] = useToggleState();

  const addImage = useCallback(
    (url: string) => {
      if (url && editor) {
        editor.chain().focus().setImage({ src: url }).run();
        onSuccessfulUpload?.(url);
        close();
      }
    },
    [editor, onSuccessfulUpload]
  );

  return (
    <FocusModal open={isOpen} onOpenChange={toggle}>
      <FocusModal.Trigger asChild>
        <Button><PERSON>daj zdj<PERSON>cie</Button>
      </FocusModal.Trigger>
      <FocusModal.Content className="max-h-fit max-w-md m-auto">
        <FocusModal.Header>Prześlij zdj<PERSON>cie</FocusModal.Header>
        <div className="p-4">
          <ImageDropzone
            getUploadedImageResult={(image) => addImage(image.url)}
          />
        </div>
      </FocusModal.Content>
    </FocusModal>
  );
};

export default AddImageModule;
