import { BroomSparkle } from "@medusajs/icons";
import { But<PERSON> } from "@medusajs/ui";
import { Editor } from "@tiptap/react";

type Props = { editor: Editor };

const ColorInputModule = ({ editor }: Props) => {
  const defaultColor = "#7e7e7e";
  return (
    <div className="flex items-center gap-2 border p-1 px-2 rounded-md shadow-sm col-span-full">
      <input
        className="p-0"
        type="color"
        onInput={(event) => {
          const color = (event.target as HTMLInputElement).value;
          editor.chain().focus().setColor(color).run();
        }}
        value={editor.getAttributes("textStyle").color || defaultColor}
      />

      <Button
        variant="secondary"
        size="small"
        onClick={() => editor.chain().focus().unsetColor().run()}
      >
        <BroomSparkle className="size-4" />
      </Button>
    </div>
  );
};

export default ColorInputModule;
