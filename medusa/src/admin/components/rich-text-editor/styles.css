@font-face {
  font-family: "Open Sans";
  font-style: normal;
  font-weight: 300 800;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/opensans/v36/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTS-mu0SC55I.woff2)
    format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA,
    U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191,
    U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: "Open Sans";
  font-style: italic;
  font-weight: 300 800;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/opensans/v36/memtYaGs126MiZpBA-UFUIcVXSCEkx2cmqvXlWqWuU6F.woff2)
    format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA,
    U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191,
    U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: "Plus Jakarta Sans";
  font-style: normal;
  font-weight: 200 800;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/plusjakartasans/v8/LDIbaomQNQcsA88c7O9yZ4KMCoOg4IA6-91aHEjcWuA_KU7NSg.woff2)
    format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA,
    U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191,
    U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: "Plus Jakarta Sans";
  font-style: italic;
  font-weight: 200 800;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/plusjakartasans/v8/LDIZaomQNQcsA88c7O9yZ4KMCoOg4KozySKCdSNG9OcqYQ2lCR7s.woff2)
    format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA,
    U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191,
    U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

.rich-text-editor {
  border: 1px solid var(--border-base);
  border-radius: 4px;
  overflow: hidden;
}
.editor-toolbar {
  padding: 8px;
  border-bottom: 1px solid var(--border-base) !important ;
  display: flex;
  gap: 8px;
}

.editor-toolbar button.is-active {
  background-color: var(--contrast-bg-base-pressed) !important;
  color: var(--fg-on-inverted);
}

.ProseMirror {
  outline: none !important;
}

.ProseMirror ul {
  list-style: disc;
  padding-left: 1em;
  margin: 0 0 1em 0;
}

.ProseMirror a {
  color: #007bff;
  text-decoration: underline;
}

.ProseMirror img {
  max-width: 100%;
  height: auto;
  margin: 3.7em 0;
}

.ProseMirror p {
  margin: 0 0 1em 0;
  min-height: 1.5em;
}

.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3,
.ProseMirror h4,
.ProseMirror h5,
.ProseMirror h6 {
  font-weight: 400;
  margin: 1.5rem 0;
}

.ProseMirror strong {
  font-weight: 600;
}

.ProseMirror hr {
  width: 50%;
  max-width: 300px;
  border-bottom: 2px solid #f6b332;
  margin-top: 1em;
  margin-bottom: 1em;
}

.ProseMirror h1 {
  font-size: 4rem;
}

@media (max-width: 768px) {
  .ProseMirror h1 {
    font-size: 3rem;
  }
}

.ProseMirror h2 {
  font-size: 3rem;
}

@media (max-width: 768px) {
  .ProseMirror h2 {
    font-size: 2.25rem;
  }
}

.ProseMirror h3 {
  font-size: 2.25rem;
  position: relative;
  padding-bottom: 1.5rem;
}

.ProseMirror h3::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 30%;
  min-width: 150px;
  height: 2px;
  border-radius: 99px;
  background-color: #f6b332;
}

@media (max-width: 768px) {
  .ProseMirror h3 {
    font-size: 1.6875rem;
  }
}

.ProseMirror h4 {
  font-size: 1.6875rem;
}

@media (max-width: 768px) {
  .ProseMirror h4 {
    font-size: 1.25rem;
  }
}

.ProseMirror h5 {
  font-size: 1.25rem;
}

.ProseMirror h6 {
  font-size: 0.9375rem;
}

.blog {
  width: 100%;
  max-width: 1024px;
  margin-left: auto;
  margin-right: auto;
  padding: 1rem;
}

@media (min-width: 768px) {
  .blog {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.blog p {
  color: #7e7e7e;
}

.blog img {
  clip-path: inset(10px round 0.5rem);
  width: 100%;
  max-width: 640px;
  margin-left: auto;
  margin-right: auto;
  height: auto;
}

/* Color input */
input[type="color"] {
  -webkit-appearance: none;
  border: none;
  cursor: pointer;
  @apply rounded-md size-6;
}
input[type="color"]::-webkit-color-swatch-wrapper {
  padding: 0;
}
input[type="color"]::-webkit-color-swatch {
  border: none;
}
