import FormField from "../guides/form-field";
import { UseFormReturn, useWatch } from "react-hook-form";
import ImageDropzone from "../reusable/image_dropzone";
import { TCreateTourFormBody } from "./schemas";

type Props = {
  form: UseFormReturn<TCreateTourFormBody>;
  formValues: ReturnType<typeof useWatch<TCreateTourFormBody>>;
};

const TourRecommendations = ({ form, formValues }: Props) => {
  return (
    <>
      <FormField control={form.control} name="featured_image">
        <FormField.Label>Miniaturka (zdjęcie wyróżniajce)</FormField.Label>
        <FormField.Note>
          Przesłany plik powinien mieć proporcje zbliżone do kwadratu
        </FormField.Note>
        <ImageDropzone syncedWithForm form={form} field_key="featured_image" />
      </FormField>

      <FormField control={form.control} name="gallery" className="mt-4">
        <FormField.Label>Galeria</FormField.Label>
        <ImageDropzone
          syncedWithForm
          form={form}
          isTargetingMultiple
          field_key="gallery"
        />
      </FormField>
    </>
  );
};

export default TourRecommendations;
