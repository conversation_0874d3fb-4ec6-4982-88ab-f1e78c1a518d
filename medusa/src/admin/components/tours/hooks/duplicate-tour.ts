import { toast } from "@medusajs/ui";
import {
  createTour,
  getTourDetailsForDuplication,
} from "../../../lib/api/tours";
import { AdminCreateProduct, AdminCreateProductVariant } from "@medusajs/types";
import { slugify } from "../../../lib/util/slugify";
import { ISODateStringToPolishDateString } from "../../../lib/util/string-dates";
import { TTourExtendedByProduct } from "../../../types";
import { TCreateTourFormBody } from "../schemas";

export const useDuplicateTour = ({ refetch }: { refetch: () => void }) => {
  const metaFields = [
    "product_id",
    "created_at",
    "updated_at",
    "deleted_at",
    "product",
    "tour_id_id",
  ];

  const sanitizePayloadsFromMetaFields = <T>(payload: T): T => {
    if (Array.isArray(payload)) {
      return payload.map((item) => sanitizePayloadsFromMetaFields(item)) as T;
    }

    if (payload !== null && typeof payload === "object") {
      const sanitizedEntries = Object.entries(payload)
        .filter(([key]) => !metaFields.includes(key))
        .map(([key, value]) => [key, sanitizePayloadsFromMetaFields(value)]);

      return Object.fromEntries(sanitizedEntries);
    }

    return payload;
  };

  const createVariants = (
    tourData: TTourExtendedByProduct
  ): AdminCreateProductVariant[] => {
    let variants: AdminCreateProductVariant[] = [];

    const productVariants = tourData.product.variants;
    const productPriceVariant = tourData.product_pricing_variant;

    switch (productPriceVariant) {
      case "dynamic_age_groups":
        variants = productVariants.map((variant) => ({
          title: variant.title || "",
          manage_inventory: false,
          prices: variant.prices.map((price) => ({
            ...price,
            currency_code: "EUR",
          })),
          metadata: variant.metadata || {},
        }));
        break;

      case "regular_variants":
        variants = productVariants.map((variant) => ({
          title: variant.title || "",
          manage_inventory: false,
          prices: variant.prices.map((price) => ({
            ...price,
            currency_code: "EUR",
          })),
        }));
        break;

      case "boat_rental":
        variants = productVariants.map((variant) => ({
          title: variant.title || "",
          manage_inventory: false,
          prices: variant.prices.map((price) => ({
            ...price,
            currency_code: "EUR",
          })),
          metadata: variant.metadata ?? undefined,
        }));
        break;
    }

    return variants;
  };

  const duplicateTour = async (tourId: string) => {
    try {
      const tour = await getTourDetailsForDuplication(tourId);
      const {
        id,
        created_at,
        updated_at,
        deleted_at,
        product,
        ...tourPayload
      } = tour.tour;

      // First convert to unknown to avoid type checking, then to TCreateTourFormBody
      const rawTourData = sanitizePayloadsFromMetaFields(
        tourPayload
      ) as unknown as TCreateTourFormBody;
      const newName = `${rawTourData.name.trim()}-kopia-${new Date().getTime()}`;

      const newSlug = slugify(newName + `-${new Date().getTime()}`);

      const sanitizedTourData: TCreateTourFormBody = {
        ...rawTourData,
        description: rawTourData.description || "",
        featured_image: rawTourData.featured_image || "",
        organizator_mail: rawTourData.organizator_mail || "",
        price_from: rawTourData?.price_from?.toString() || "",
        cities: rawTourData.cities.map((city) => city.id),
        dates_from_to: rawTourData.dates_from_to || "",
        gallery: rawTourData.gallery || [],
        category_ids: rawTourData.category_ids || [],
      };

      const payload: AdminCreateProduct & {
        additional_data: TCreateTourFormBody;
      } = {
        title: newName,
        handle: newSlug,
        description: sanitizedTourData.description,
        thumbnail: sanitizedTourData.featured_image || undefined,
        images: sanitizedTourData.gallery.map((image) => ({
          url: image,
        })),
        categories: sanitizedTourData.category_ids.map((category) => ({
          id: category,
        })),
        options: [
          {
            title: "domyślne",
            values: [],
          },
        ],
        variants: createVariants(tour.tour),
        additional_data: {
          ...sanitizedTourData,
          name: newName,
          blocked_dates_by_month: sanitizedTourData.blocked_dates_by_month.map(
            (date) => ({
              month: date.month,
              days: date.days,
            })
          ),
          slug: newSlug,
          food_options:
            sanitizedTourData.food_options
              ?.filter((food) => food !== undefined)
              .map((food) => ({
                name: food.name,
                description: food.description || "",
                price: food.price || "",
                required: food.required,
              })) || [],
        },
      };

      await createTour(payload);
      refetch();
      toast.success("Kopia atrakcji została utworzona");
    } catch (error) {
      console.error(error);
      toast.error(
        `Wystąpił błąd podczas tworzenia kopii atrakcji - ${JSON.stringify(
          error,
          null,
          2
        )}`
      );
    }
  };

  return { duplicateTour };
};
