import { FieldErrors } from "react-hook-form";
import { TCreateTourFormBody } from "../schemas";

export const useFormTabErrors = (errors: FieldErrors<TCreateTourFormBody>) => {
  const hasTabErrors = (tabFields: string[]) => {
    return tabFields.some((field) => {
      if (field.includes(".")) {
        const [parent, child] = field.split(".");
        const parentErrors = errors[parent as keyof typeof errors];
        return (
          parentErrors &&
          typeof parentErrors === "object" &&
          child in parentErrors
        );
      }
      return errors[field as keyof typeof errors];
    });
  };

  const baseTabErrors = hasTabErrors([
    "is_active",
    "name",
    "slug",
    "category_ids",
    "description",
    "organizator_mail",
    "recommended_tours",
    "food_options",
    "pricing.base_price",
    "pricing.discount_price",
    "pricing.discount_active_from",
    "pricing.discount_active_to",
    "pricing.prepaid_percentage",
    "pricing.prepaid_percentage_high_season",
    "blocked_dates",
    "blocked_dates.date_from",
    "blocked_dates.date_to",
    "blocked_dates.only_one_day",
  ]);

  const seoTabErrors = hasTabErrors([
    "tour_seo.title",
    "tour_seo.description",
    "tour_seo.keywords",
  ]);

  const contentTabErrors = hasTabErrors([
    "content_blocks.description",
    "content_blocks.calendar",
    "content_blocks.price",
    "content_blocks.how_to_book",
    "content_blocks.ticket",
    "content_blocks.specification_only_for_boats",
    "content_blocks.program",
    "content_blocks.details",
    "content_blocks.place",
    "content_blocks.faq",
  ]);

  const imagesTabErrors = hasTabErrors(["featured_image", "gallery"]);

  const ticketTabErrors = hasTabErrors([
    "ticket.time_start",
    "ticket.map",
    "ticket.meeting_time",
    "ticket.meeting_place",
    "ticket.additional_meeting_details",
    "ticket.intermediary_name",
    "ticket.organizer_name",
    "ticket.organizer_contact",
    "ticket.ship_or_transport_description",
    "ticket.what_to_bring",
    "ticket.where_to_park",
    "ticket.additional_info",
    "ticket.link_to_download",
  ]);

  return {
    baseTabErrors,
    seoTabErrors,
    contentTabErrors,
    imagesTabErrors,
    ticketTabErrors,
  };
};
