import { TTourExtendedByProduct } from "../../../types";
import { sdk } from "../../../lib/config";
import { DragEndEvent } from "@dnd-kit/core";
import { toast } from "@medusajs/ui";

type Props = {
  tours: TTourExtendedByProduct[];
  refetch: () => void;
};

type UpdatePositionPayload = {
  id: string;
  position: number;
};

const useUpdateTourPosition = ({ tours, refetch }: Props) => {
  const updatePositions = async (
    tours: TTourExtendedByProduct[],
    activeId: string,
    overId: string
  ) => {
    try {
      // Find the items that are being reordered
      const oldIndex = tours.findIndex((tour) => tour.id === activeId);
      const newIndex = tours.findIndex((tour) => tour.id === overId);

      if (oldIndex === -1 || newIndex === -1) return;

      // Create a new array with the reordered items
      const newTours = [...tours];
      const [reorderedItem] = newTours.splice(oldIndex, 1);
      newTours.splice(newIndex, 0, reorderedItem);

      // Update positions for all affected items
      const updatedPositions: UpdatePositionPayload[] = newTours.map(
        (tour, index) => ({
          id: tour.id,
          position: index,
        })
      );

      // Make the API call to update positions
      await sdk.client.fetch("/admin/tours/positions", {
        method: "PUT",
        body: {
          positions: updatedPositions,
        },
      });

      return newTours;
    } catch (error) {
      console.error("Error updating tour positions:", error);
      throw error;
    }
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;

    try {
      const newTours = await updatePositions(
        tours,
        active.id as string,
        over.id as string
      );
      if (newTours) {
        refetch();
        toast.success("Pomyślnie zaktualizowano kolejność atrakcji");
      }
    } catch (error) {
      console.error("Error reordering tours:", error);
      toast.error("Wystąpił błąd podczas aktualizacji kolejności atrakcji");
    }
  };

  return { updatePositions, handleDragEnd };
};

export default useUpdateTourPosition;
