import { useState, useRef, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { QUERY_KEYS } from "../../lib/constants/query-keys";
import { getAllCities } from "../../lib/api/cities";
import { TCity } from "../../types";

type CitiesInputProps = {
  value: string[];
  onChange: (value: string[]) => void;
};

const CitiesInput = ({ value, onChange }: CitiesInputProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isOpen, setIsOpen] = useState(false);

  const { data, isLoading } = useQuery({
    queryKey: [QUERY_KEYS.ALL_CITIES],
    queryFn: getAllCities,
  });

  const cities = data || [];

  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Filter tours based on search and already selected items
  const filteredCities = cities.filter(
    (city: TCity) =>
      !value.includes(city.id) &&
      city.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelect = (tourId: string) => {
    if (!value.includes(tourId)) {
      onChange([...value, tourId]);
      setIsOpen(false);
      setSearchTerm("");
    }
  };

  const handleRemove = (tourId: string) => {
    onChange(value.filter((id) => id !== tourId));
  };

  const renderDropdownContent = () => {
    if (isLoading) {
      return <div className="p-2 text-sm">Pobieranie atrakcji...</div>;
    }

    if (!filteredCities.length) {
      return <div className="p-2 text-sm">Brak dostępnych miast</div>;
    }

    return filteredCities.map((city: TCity) => (
      <button
        key={city.id}
        onClick={() => handleSelect(city.id)}
        className="w-full px-3 py-2 text-left hover:bg-ui-bg-base-hover flex items-center gap-x-2"
      >
        <span className="text-sm block">{city.name}</span>
      </button>
    ));
  };

  return (
    <div className="flex flex-col gap-y-2">
      <div className="flex flex-col gap-y-1">
        <div className="relative" ref={dropdownRef}>
          <input
            type="text"
            placeholder="Przypisz miasta do atrakcji"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onFocus={() => setIsOpen(true)}
            className="w-full px-3 py-2 bg-ui-bg-field border border-ui-border-base shadow-sm rounded-md text-sm"
          />
          {isOpen && (
            <div className="absolute z-50 w-full mt-1 bg-ui-bg-base border rounded-md shadow-lg max-h-[300px] overflow-y-auto">
              {renderDropdownContent()}
            </div>
          )}
        </div>
      </div>

      {/* Display selected tours */}
      {value.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-2">
          {value.map((cityId) => {
            const city = cities.find((c: TCity) => c.id === cityId);
            return city ? (
              <div
                key={cityId}
                className="flex items-center gap-x-2 bg-ui-bg-base border border-ui-border-base px-3 py-1.5 rounded-lg"
              >
                <span className="text-sm">{city.name}</span>
                <button
                  type="button"
                  onClick={() => handleRemove(cityId)}
                  className="text-ui-text-muted hover:text-ui-text-muted ml-1"
                >
                  ×
                </button>
              </div>
            ) : null;
          })}
        </div>
      )}
    </div>
  );
};

export default CitiesInput;
