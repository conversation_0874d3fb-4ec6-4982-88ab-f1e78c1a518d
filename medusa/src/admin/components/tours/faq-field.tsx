import { useFieldArray, UseFormReturn, useFormState } from "react-hook-form";
import { TCreateTourFormBody } from "./schemas";
import { Button } from "@medusajs/ui";
import { Plus } from "@medusajs/icons";
import FormField from "../guides/form-field";
import { FaqItem } from "./faq-item";

type Props = {
  form: UseFormReturn<TCreateTourFormBody>;
};

const FaqField = ({ form }: Props) => {
  const fieldArray = useFieldArray({
    name: "content_blocks.faq",
    control: form.control,
  });

  const errors = useFormState({
    control: form.control,
  });

  const faqErrors = errors.errors.content_blocks?.faq;

  return (
    <>
      <FormField.Label>Najczęściej zadawane pytania</FormField.Label>
      {faqErrors && <p className="text-ui-fg-error">{faqErrors.message}</p>}

      {fieldArray.fields.map((field, index) => (
        <FaqItem
          key={field.id}
          field={field}
          form={form}
          index={index}
          control={form.control}
          onRemove={fieldArray.remove}
        />
      ))}

      <Button
        className={`${
          fieldArray.fields.length === 0 ? "size-full h-8" : "ml-auto"
        } `}
        onClick={() =>
          fieldArray.append({
            question: "",
            answer: "",
            position: fieldArray.fields.length + 1,
          })
        }
      >
        <Plus className="w-4 h-4" />
        Dodaj zestaw
      </Button>
    </>
  );
};

export default FaqField;
