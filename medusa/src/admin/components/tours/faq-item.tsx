import { Trash } from "@medusajs/icons";
import { Button, Input } from "@medusajs/ui";
import {
  Control,
  Controller,
  FieldArrayWithId,
  UseFormReturn,
} from "react-hook-form";
import RichTextEditor from "../rich-text-editor";
import FormField from "../guides/form-field";
import { TCreateTourFormBody } from "./schemas";
import FormGrid from "../reusable/form-grid";
import DeleteWithConfirmation from "../reusable/delete-with-confirmation";

type Props = {
  field: FieldArrayWithId<TCreateTourFormBody, "content_blocks.faq", "id">;
  index: number;
  control: Control<TCreateTourFormBody>;
  form: UseFormReturn<TCreateTourFormBody>;
  onRemove: (index: number) => void;
};

export const FaqItem = ({ field, index, control, onRemove, form }: Props) => {
  return (
    <div className="grid gap-4 border rounded-md p-4">
      <FormGrid gridCols={2}>
        <FormField
          control={control}
          name={`content_blocks.faq.${index}.question`}
        >
          <FormField.Label>Pytanie</FormField.Label>
          <Input {...form.register(`content_blocks.faq.${index}.question`)} />
        </FormField>
        <FormField
          control={control}
          name={`content_blocks.faq.${index}.position`}
        >
          <FormField.Label>Pozycja</FormField.Label>

          <Input
            type="number"
            inputMode="numeric"
            {...form.register(`content_blocks.faq.${index}.position`, {
              valueAsNumber: true,
            })}
          />
        </FormField>
      </FormGrid>
      <FormField control={control} name={`content_blocks.faq.${index}.answer`}>
        <FormField.Label>Odpowiedź</FormField.Label>
        <Controller
          control={control}
          name={`content_blocks.faq.${index}.answer`}
          render={({ field }) => (
            <RichTextEditor
              content={field.value}
              onChange={(content) => field.onChange(content)}
            />
          )}
        />
      </FormField>

      <DeleteWithConfirmation onDelete={() => onRemove(index)}>
        <Button variant="danger" className="mb-3 ml-auto">
          <Trash className="w-4 h-4" />
        </Button>
      </DeleteWithConfirmation>
    </div>
  );
};
