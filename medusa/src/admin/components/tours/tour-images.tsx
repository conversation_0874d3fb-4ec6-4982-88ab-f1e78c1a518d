import FormField from "../guides/form-field";
import { UseFormReturn } from "react-hook-form";
import ImageDropzone from "../reusable/image_dropzone";
import { TCreateTourFormBody } from "./schemas";

type Props = {
  form: UseFormReturn<TCreateTourFormBody>;
};

const TourImages = ({ form }: Props) => {
  return (
    <>
      <FormField control={form.control} name="featured_image">
        <FormField.Label>Miniaturka (zdjęcie wyróżniajce)</FormField.Label>
        <FormField.Note>
          Przesłany plik powinien mieć proporcje zbliżone do kwadratu
        </FormField.Note>
        <ImageDropzone syncedWithForm form={form} field_key="featured_image" />
      </FormField>

      <FormField control={form.control} name="cart_section_image">
        <FormField.Label>Obrazek w sekcji rezerwacji</FormField.Label>
        <FormField.Note>
          Je<PERSON><PERSON> nie zostanie dodany, tło pozostanie białe
        </FormField.Note>
        <ImageDropzone
          syncedWithForm
          form={form}
          field_key="cart_section_image"
        />
      </FormField>
      <FormField control={form.control} name="gallery" className="mt-4">
        <FormField.Label>Galeria</FormField.Label>
        <ImageDropzone
          syncedWithForm
          form={form}
          isTargetingMultiple
          field_key="gallery"
        />
      </FormField>
      <div className="h-[35vh]" />
    </>
  );
};

export default TourImages;
