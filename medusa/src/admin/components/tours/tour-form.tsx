import { useMutation } from "@tanstack/react-query";
import {
  <PERSON><PERSON>,
  Container,
  FocusModal,
  Tabs,
  toast,
  Toaster,
} from "@medusajs/ui";
import { AdminCreateProduct, AdminCreateProductVariant } from "@medusajs/types";
import { ArrowUpRightOnBox, Spinner } from "@medusajs/icons";
import {
  TCreateTourFormBody,
  toursSchemas,
  TUpdateTourPayload,
} from "./schemas/index";
import { createTour, updateTour } from "../../lib/api/tours";
import { FieldErrors, useForm, useWatch } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import TourBaseDetails from "./tour-base-details";
import TourSeoDetails from "./tour-seo-details";
import TourBlocksContent from "./tour-blocks_content";
import TourImages from "./tour-images";
import TourTicket from "./tour-ticket";
import { useEffect } from "react";
import { useFormTabErrors } from "./hooks/use-form-tab-errors";
import { slugify } from "../../lib/util/slugify";
import PriceVariants from "./price-variants";
import { ISODateStringToPolishDateString } from "../../lib/util/string-dates";
import LinkButton from "../reusable/link-button";
import { config } from "../../../config";

type Props = {
  initialValues?: Partial<TCreateTourFormBody>;
  editMode?: boolean;
  productId?: string;
  tourId?: string;
  onSuccessfulSubmit?: () => void;
};

const TourForm = ({
  initialValues,
  editMode,
  productId,
  tourId,
  onSuccessfulSubmit,
}: Props) => {
  const form = useForm<TCreateTourFormBody>({
    resolver: zodResolver(toursSchemas.createTourFormBody),
  });

  const {
    formState: { errors },
  } = form;

  const {
    baseTabErrors,
    seoTabErrors,
    contentTabErrors,
    imagesTabErrors,
    ticketTabErrors,
  } = useFormTabErrors(errors);

  useEffect(() => {
    if (initialValues) {
      // Set default pricing structure based on product_pricing_variant
      const pricing = {
        prepaid_percentage: null,
        prepaid_percentage_high_season: null,
        ...initialValues.pricing,
      };

      form.reset({
        ...initialValues,
        pricing,
      });
    }
  }, [initialValues]);

  const { isPending: isCreating, mutate: triggerCreateTour } = useMutation({
    mutationFn: (
      data: AdminCreateProduct & {
        additional_data: TCreateTourFormBody;
      }
    ) => createTour(data),
    onSuccess: (response) => {
      toast.success("Atrakcja stworzona", {
        description: "Kliknij w link poniżej aby zobaczyć atrakcję w sklepie",
        duration: Infinity,
        dismissable: true,
        action: {
          label: "Podgląd atrakcji",
          variant: "default",
          onClick: () => {
            window.open(
              `${config.MEDUSA_STOREFRONT_URL}/produkt/${response.tour_slug}`,
              "_blank"
            );
          },
          altText: "Podgląd atrakcji",
        },
      });
      onSuccessfulSubmit && onSuccessfulSubmit();
    },
    onError: (error) => {
      console.error(error);
      toast.error(
        `Nie udało się stworzyć nowej atrakcji - Upewnij się że atrakcja o tej nazwie jeszcze nie istnieje ${JSON.stringify(
          error,
          null,
          2
        )}`
      );
    },
  });

  const { isPending: isUpdating, mutate: triggerUpdateTour } = useMutation({
    mutationFn: (
      data: AdminCreateProduct & {
        additional_data: TUpdateTourPayload;
      }
    ) => updateTour(data, productId!),
    onSuccess: () => {
      toast.success("Atrakcja zaktualizowana");
      onSuccessfulSubmit && onSuccessfulSubmit();
    },
    onError: (error) => {
      console.error(error);
      toast.error(
        `Nie udało się zaktualizować atrakcji - ${JSON.stringify(
          error,
          null,
          2
        )}`
      );
    },
  });

  const formValues = useWatch({ control: form.control });

  const onError = (errors: FieldErrors<TCreateTourFormBody>) => {
    toast.error(
      `Wystąpił błąd podczas walidacji formularza: ${JSON.stringify(
        errors,
        null,
        2
      )}`
    );
    console.log(errors);
  };

  const onSubmit = async (data: TCreateTourFormBody) => {
    const fallbackName = `produkt-${new Date().getTime()}`;

    let variants: AdminCreateProductVariant[] = [];

    // Handle variants based on product type
    switch (data.product_pricing_variant) {
      case "dynamic_age_groups":
        variants = [
          ...(data.pricing.age_groups?.length
            ? data.pricing.age_groups.flatMap((group, groupIndex) => {
                // Base variant for the age group
                const baseVariant: AdminCreateProductVariant = {
                  title: group.name,
                  manage_inventory: false,
                  prices: [
                    {
                      amount: group.price || 0,
                      currency_code: "EUR",
                    },
                  ],
                };

                // Date-specific variants for this age group
                const dateVariants = data.pricing.date_variants?.map(
                  (dateVariant) => ({
                    title: `${group.name} (${ISODateStringToPolishDateString(
                      dateVariant.start_date
                    )} - ${ISODateStringToPolishDateString(
                      dateVariant.end_date
                    )})`,
                    manage_inventory: false,
                    prices: [
                      {
                        amount:
                          dateVariant.age_group_prices[groupIndex]?.price || 0,
                        currency_code: "EUR",
                      },
                    ],
                    metadata: {
                      start_date: dateVariant.start_date,
                      end_date: dateVariant.end_date,
                      age_group_name: group.name,
                    },
                  })
                );

                return [baseVariant, ...(dateVariants || [])];
              })
            : []),
        ];
        break;

      case "regular_variants":
        variants =
          data.pricing.regular_variants?.map((variant) => ({
            title: variant.name,
            manage_inventory: false,
            prices: [
              {
                amount: variant.price,
                currency_code: "EUR",
              },
            ],
          })) || [];
        break;

      case "boat_rental":
        // Base price variant
        const baseVariant: AdminCreateProductVariant = {
          title: "Cena podstawowa",
          manage_inventory: false,
          prices: [
            {
              amount: data.pricing.boat_variants?.base_price || 0,
              currency_code: "EUR",
            },
          ],
        };

        // Date range variants
        const dateRangeVariants =
          data.pricing.boat_variants?.date_ranges?.map((range) => ({
            title: `Wynajem ${ISODateStringToPolishDateString(
              range.start_date
            )} - ${ISODateStringToPolishDateString(range.end_date)}`,
            manage_inventory: false,
            prices: [
              {
                amount: range.price,
                currency_code: "EUR",
              },
            ],
            metadata: {
              start_date: range.start_date,
              end_date: range.end_date,
            },
          })) || [];

        variants = [baseVariant, ...dateRangeVariants];
        break;
    }

    const payload: AdminCreateProduct & {
      additional_data: TCreateTourFormBody & {
        tour_id?: string;
      };
    } = {
      description: data.description,
      thumbnail: data.featured_image,
      images: data.gallery.map((image) => ({
        url: image,
      })),
      categories: data.category_ids.map((category) => ({
        id: category,
      })),
      options: [
        {
          title: "domyślne",
          values: [],
        },
      ],
      variants,
      title: data.name || fallbackName,
      additional_data: {
        ...data,
        name: data.name || fallbackName,
        slug: data.slug || slugify(data.name || fallbackName),
        ...(!editMode && { position: 999 }),
        food_options: data.food_options
          .filter(
            (food): food is NonNullable<typeof food> => food !== undefined
          )
          .map((food) => ({
            name: food.name,
            description: food.description || "",
            price: food.price || "",
            required: food.required,
          })),
      },
    };

    try {
      if (editMode) {
        payload.additional_data.tour_id = tourId;
        triggerUpdateTour(
          payload as AdminCreateProduct & {
            additional_data: TUpdateTourPayload;
          }
        );
      } else {
        triggerCreateTour(payload);
      }
    } catch (error) {
      console.error(error);
      toast.error(
        `Nie udało się stworzyć nowej atrakcji - ${JSON.stringify(
          error,
          null,
          2
        )}`
      );
    }
  };

  const TABS = [
    {
      label: "Podstawowe",
      value: "base",
      hasErrors: baseTabErrors,
    },
    {
      label: "Warianty cenowe",
      value: "pricing",
    },
    {
      label: "SEO",
      value: "seo",
      hasErrors: seoTabErrors,
    },
    {
      label: "Treści",
      value: "content",
      hasErrors: contentTabErrors,
    },
    {
      label: "Zdjęcia",
      value: "images",
      hasErrors: imagesTabErrors,
    },
    {
      label: "Bilet",
      value: "ticket",
      hasErrors: ticketTabErrors,
    },
  ];

  return (
    <>
      <Toaster />

      <Tabs defaultValue={TABS[0].value}>
        <Tabs.List
          className={`sticky top-0 py-5 z-50 bg-ui-bg-base px-2 sm:px-5 rounded-md border-b overflow-x-auto whitespace-nowrap ${
            editMode ? "border-b rounded-none" : ""
          }`}
        >
          {TABS.map((tab) => (
            <Tabs.Trigger
              key={tab.value}
              value={tab.value}
              className={`${tab.hasErrors ? "text-ui-fg-error" : ""}`}
            >
              {tab.label}
              {tab.hasErrors && (
                <span className="ml-1 text-ui-fg-error">•</span>
              )}
            </Tabs.Trigger>
          ))}
        </Tabs.List>
        <Container
          className={`flex flex-col max-sm:px-2 shadow-none max-w-full w-full grow h-full rounded-none ${
            editMode ? "rounded-md" : ""
          }`}
        >
          <Tabs.Content
            value={TABS[0].value}
            className="flex flex-col h-full max-w-screen-lg mx-auto grow gap-6 w-full"
          >
            <TourBaseDetails form={form} formValues={formValues} />
          </Tabs.Content>
          <Tabs.Content
            value={TABS[1].value}
            className="flex flex-col h-full mx-auto max-w-screen-xl w-full grow gap-6"
          >
            <PriceVariants form={form} formValues={formValues} />
          </Tabs.Content>
          <Tabs.Content
            value={TABS[2].value}
            className="flex flex-col h-full  grow gap-6"
          >
            <TourSeoDetails form={form} formValues={formValues} />
          </Tabs.Content>
          <Tabs.Content
            value={TABS[3].value}
            className="flex flex-col h-full max-w-screen-lg mx-auto grow gap-6"
          >
            <TourBlocksContent form={form} />
          </Tabs.Content>
          <Tabs.Content
            value={TABS[4].value}
            className="flex flex-col h-full max-w-screen-lg w-full mx-auto grow gap-6"
          >
            <TourImages form={form} />
          </Tabs.Content>
          <Tabs.Content
            value={TABS[5].value}
            className="flex flex-col h-full max-w-screen-lg mx-auto grow gap-6"
          >
            <TourTicket form={form} formValues={formValues} />
          </Tabs.Content>
        </Container>
      </Tabs>

      {editMode ? (
        <div className="flex p-4 sticky gap-4 bg-ui-bg-base h-fit bottom-0  border w-full justify-end rounded-md">
          <LinkButton
            variant="secondary"
            href={`${config.MEDUSA_STOREFRONT_URL}/produkt/${initialValues?.slug}`}
            target="_blank"
          >
            Podgląd atrakcji
            <ArrowUpRightOnBox className="w-4 h-4" />
          </LinkButton>
          <Button
            className="self-end"
            onClick={form.handleSubmit(onSubmit, onError)}
          >
            {isUpdating || isCreating ? (
              <Spinner className="animate-spin" />
            ) : (
              "Aktualizuj atrakcję"
            )}
          </Button>
        </div>
      ) : (
        <FocusModal.Footer className="sticky bg-ui-bg-base h-fit bottom-0 shrink-1">
          <Button
            className="self-end"
            onClick={form.handleSubmit(onSubmit, onError)}
          >
            {isUpdating || isCreating ? (
              <Spinner className="animate-spin" />
            ) : (
              "Stwórz nową atrakcję"
            )}
          </Button>
        </FocusModal.Footer>
      )}
    </>
  );
};

export default TourForm;
