import { Button, Input, Select } from "@medusajs/ui";
import { Plus, Trash } from "@medusajs/icons";
import {
  useFieldArray,
  UseFormReturn,
  useWatch,
  Controller,
} from "react-hook-form";
import <PERSON>Field from "../guides/form-field";
import { TCreateTourFormBody } from "./schemas";
import DeleteWithConfirmation from "../reusable/delete-with-confirmation";
import AvailableStartPlacesInput from "./available-start-places-input";

type Props = {
  form: UseFormReturn<TCreateTourFormBody>;
  formValues: ReturnType<typeof useWatch<TCreateTourFormBody>>;
};

const MONTHS = [
  { value: 1, label: "Styczeń" },
  { value: 2, label: "<PERSON><PERSON>" },
  { value: 3, label: "Marzec" },
  { value: 4, label: "Kwiecień" },
  { value: 5, label: "Maj" },
  { value: 6, label: "<PERSON><PERSON><PERSON><PERSON>" },
  { value: 7, label: "<PERSON><PERSON><PERSON>" },
  { value: 8, label: "Si<PERSON>pie<PERSON>" },
  { value: 9, label: "<PERSON><PERSON>esi<PERSON><PERSON>" },
  { value: 10, label: "Październik" },
  { value: 11, label: "Listopad" },
  { value: 12, label: "Grudzień" },
];

const ExcludedStartPlacesInput = ({ form, formValues }: Props) => {
  // This should be treated as start_palces_excluded_by_date - requirement changed but due to time constraints naming is like this
  // This should be treated as start_palces_excluded_by_date - requirement changed but due to time constraints naming is like this
  // This should be treated as start_palces_excluded_by_date - requirement changed but due to time constraints naming is like this
  // This should be treated as start_palces_excluded_by_date - requirement changed but due to time constraints naming is like this
  const startPlacesByDateArray = useFieldArray({
    name: "start_places_by_date",
    control: form.control,
  });

  const handleAddStartPlaceByDate = () => {
    startPlacesByDateArray.append({
      place: "",
      month: 1,
      days: "",
    });
  };

  return (
    <div className="space-y-6">
      <div className="border rounded-lg p-4 bg-ui-bg-subtle flex flex-col gap-4">
        <AvailableStartPlacesInput form={form} />
      </div>

      <div className="border rounded-lg p-4 bg-ui-bg-subtle flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <FormField.Label>
            Zablokowane miejsca startu w konkretnych dniach miesiąca
          </FormField.Label>
        </div>

        <div className="flex flex-col gap-4">
          {startPlacesByDateArray.fields.map((field, index) => (
            <div
              key={field.id}
              className="grid sm:grid-cols-[1fr,1fr,1fr,auto] gap-4 items-end bg-ui-bg-base p-4 rounded-lg border"
            >
              <FormField
                control={form.control}
                name={`start_places_by_date.${index}.month`}
              >
                <FormField.Label className="text-sm">Miesiąc</FormField.Label>

                <Controller
                  control={form.control}
                  name={`start_places_by_date.${index}.month`}
                  render={({ field }) => (
                    <Select
                      value={field.value.toString()}
                      onValueChange={(value) => {
                        field.onChange(Number(value));
                      }}
                    >
                      <Select.Trigger>
                        <Select.Value placeholder="Wybierz miesiąc" />
                      </Select.Trigger>
                      <Select.Content>
                        {MONTHS.map((month) => (
                          <Select.Item
                            key={month.value}
                            value={month.value.toString()}
                          >
                            {month.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                />
              </FormField>

              <FormField
                control={form.control}
                name={`start_places_by_date.${index}.days`}
              >
                <FormField.Label className="text-sm">
                  Dni (po przecinku)
                </FormField.Label>
                <Input
                  {...form.register(`start_places_by_date.${index}.days`)}
                  placeholder="np. 1,15,30"
                />
              </FormField>

              <FormField
                control={form.control}
                name={`start_places_by_date.${index}.place`}
              >
                <FormField.Label className="text-sm">
                  Miejsce startu
                </FormField.Label>
                <Controller
                  control={form.control}
                  name={`start_places_by_date.${index}.place`}
                  render={({ field }) => (
                    <Select
                      value={field.value}
                      onValueChange={(value) => {
                        field.onChange(value);
                      }}
                    >
                      <Select.Trigger>
                        <Select.Value placeholder="Wybierz miejsce" />
                      </Select.Trigger>
                      <Select.Content>
                        {formValues.available_start_places
                          ?.filter((place) => place?.place !== "")
                          .map((place) => (
                            <Select.Item
                              key={place?.place}
                              value={place?.place || ""}
                            >
                              {place?.place}
                            </Select.Item>
                          ))}
                      </Select.Content>
                    </Select>
                  )}
                />
              </FormField>

              <DeleteWithConfirmation
                onDelete={() => startPlacesByDateArray.remove(index)}
              >
                <Button variant="danger" className="h-8 w-8" size="small">
                  <Trash className="w-4 h-4" />
                </Button>
              </DeleteWithConfirmation>
            </div>
          ))}
        </div>
        <Button
          variant="primary"
          onClick={handleAddStartPlaceByDate}
          className={`h-8 ml-auto ${
            startPlacesByDateArray.fields.length > 0 ? "auto-24" : "w-full"
          }`}
        >
          <Plus className="w-4 h-4" />
          Nowy blok
        </Button>
      </div>
    </div>
  );
};

export default ExcludedStartPlacesInput;
