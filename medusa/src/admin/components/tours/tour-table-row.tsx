import {
  DropdownMenu,
  I<PERSON><PERSON><PERSON>on,
  Prompt,
  Table,
  Badge,
  toast,
  clx,
} from "@medusajs/ui";
import { ReactNode, useCallback, useState } from "react";
import { TTourExtendedByProduct } from "../../types";
import { ArrowUpRightOnBox, DotsSix, EllipsisVertical } from "@medusajs/icons";
import { useMutation } from "@tanstack/react-query";
import { deleteTour } from "../../lib/api/tours";
import { useDuplicateTour } from "./hooks/duplicate-tour";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import ImagePreview from "../reusable/image-preview";
import CreatedAtBadge from "../reusable/created-at-badge";
import { config } from "../../../config";
import { Link } from "react-router-dom";

type Props = {
  tour: TTourExtendedByProduct;
  refetch: () => void;
};

const TourTableRow = ({ tour, refetch }: Props) => {
  const [openPrompt, setOpenPrompt] = useState(false);
  const { isPending, mutate } = useMutation({
    mutationFn: deleteTour,
    onSuccess: () => {
      toast.success("Atrakcja została usunięta", {
        description: "Powiązany z nią produkt został również usunięty",
      });
      refetch();
    },
  });

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: tour.id,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const { duplicateTour } = useDuplicateTour({ refetch });

  const getCell = useCallback((content: ReactNode, className?: string) => {
    return (
      <Table.Cell className={clx("h-full pr-0 relative group", className)}>
        <Link
          to={`/atrakcje/${tour.id}`}
          className="size-full h-[inherit] max-h-[inherit] min-h-[inherit]  block pr-4 hover:underline"
        >
          {content}
        </Link>
      </Table.Cell>
    );
  }, []);

  return (
    <>
      <Table.Row key={tour.id} ref={setNodeRef} style={style}>
        <Table.Cell>
          <Badge {...attributes} {...listeners} className="cursor-grab">
            <DotsSix className="text-ui-fg-subtle" />
            {tour.position}
          </Badge>
        </Table.Cell>

        {getCell(
          <ImagePreview image={tour.featured_image} alt={tour.name} />,
          "w-24"
        )}
        {getCell(tour.slug, "w-3/12")}
        {getCell(tour.is_active ? "Tak" : "Nie", "w-1/12")}
        {getCell(tour.name, "w-4/12")}
        {getCell(<CreatedAtBadge createdAt={tour.created_at} />, "w-2/12")}

        <Table.Cell>
          <DropdownMenu>
            <DropdownMenu.Trigger>
              <IconButton>
                <EllipsisVertical />
              </IconButton>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content>
              <DropdownMenu.Item asChild>
                <a href={`/app/atrakcje/${tour.id}`}>Edytuj</a>
              </DropdownMenu.Item>
              <DropdownMenu.Item asChild>
                <a
                  href={`${config.MEDUSA_STOREFRONT_URL}/produkt/${tour.slug}`}
                  target="_blank"
                  className="flex items-center gap-x-2 "
                >
                  Podgląd atrakcji
                  <ArrowUpRightOnBox className="w-4 h-4" />
                </a>
              </DropdownMenu.Item>
              <DropdownMenu.Item onClick={() => duplicateTour(tour.id)}>
                Duplikuj jako kopię
              </DropdownMenu.Item>
              <DropdownMenu.Item
                disabled={isPending}
                onClick={() => {
                  setOpenPrompt(true);
                }}
                className="text-red-600"
              >
                Usuń
              </DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu>
        </Table.Cell>
      </Table.Row>

      <Prompt open={openPrompt} onOpenChange={setOpenPrompt}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>
              Czy na pewno chcesz usunąć tą atrakcję i powiązany z nią produkt?
            </Prompt.Title>
            <Prompt.Description>
              Po usunięciu atrakcji nie będzie możliwości jej przywrócenia.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel>Anuluj</Prompt.Cancel>
            <Prompt.Action onClick={() => mutate(tour?.product?.id)}>
              Usuń
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>
    </>
  );
};

export default TourTableRow;
