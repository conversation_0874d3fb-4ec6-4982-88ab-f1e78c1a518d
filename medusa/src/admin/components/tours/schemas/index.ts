import { z } from "zod";

export const foodOptions = z.enum([
  "wege",
  "ryba",
  "mięso",
  "kurczak",
  "ryba+kurczak",
  "wie<PERSON><PERSON><PERSON><PERSON>",
  "Plejskavica",
  "Szczegółowe lunche dla dzieci",
]);

export const productTypes = z.enum([
  "dynamic_age_groups",
  "regular_variants",
  "boat_rental",
]);

export const productTypesMap = {
  dynamic_age_groups: "Produkt z grupami wiekowymi",
  regular_variants: "Produkt uproszczony",
  boat_rental: "Wynajem łodzi",
};

export const ageGroupSchema = z.object({
  name: z.string().min(1, "Nazwa grupy wiekowej jest wymagana"),
  price: z
    .union([z.null(), z.nan(), z.number()])
    .transform((val) => (isNaN(Number(val)) ? null : val))
    .default(null),
});

export const tourValidationBody = {
  name: z.string().default(""),
  featured_image: z.string().default(""),
  is_active: z.boolean().default(false),
  product_pricing_variant: productTypes.default("dynamic_age_groups"),
  slug: z.string().default(""),
  position: z.number().default(999),
  description: z.string().default(""),
  price_from: z.string().default("").nullable(),
  dates_from_to: z.string().default("").nullable(),
  hourly_length: z.string().default("").nullable(),
  recommended_tour_ids: z.array(z.string()).optional(),
  is_recommended: z.boolean().default(false),
  is_bargain: z.boolean().default(false),
  is_promotion: z.boolean().default(false),
  promotion_price_from: z.string().nullish(),
  available_start_places: z
    .array(
      z
        .object({
          place: z.string().default("").optional().nullable(),
          position: z.number().default(0).optional().nullable(),
          rich_text_content: z.string().default("").optional().nullable(),
        })
        .optional()
        .nullable()
        .superRefine((data, ctx) => {
          if (!data?.place && !data?.rich_text_content) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: "Przynajmniej jedno z pól jest wymagane",
            });
          }
        })
    )
    .optional()
    .nullable(),
  start_times: z
    .object({
      start: z.string().default(""),
      stop: z.string().default(""),
      interval: z
        .union([
          z.nan(),
          z
            .number({ invalid_type_error: "Interwał musi być liczbą" })
            .min(5)
            .max(120),
          z.null(),
        ])
        .transform((val) => (val === null ? null : val)),
      use_manual_start_times: z.boolean().default(false),
      manual_start_times: z.array(z.string()).default([]).nullable(),
    })
    .optional()
    .nullable(),
  start_places_by_date: z
    .array(
      z.object({
        place: z.string().default(""),
        month: z.number(),
        days: z.string().optional(),
      })
    )
    .optional()
    .nullable(),
  pricing: z.object({
    age_groups: z.array(ageGroupSchema).default([]).optional(),
    date_variants: z
      .array(
        z.object({
          start_date: z.string(),
          end_date: z.string(),
          age_group_prices: z.array(
            z.object({
              price: z
                .union([z.null(), z.nan(), z.number()])
                .transform((val) => (isNaN(Number(val)) ? null : val)),
            })
          ),
        })
      )
      .default([])
      .optional(),

    boat_variants: z
      .object({
        base_price: z.number().default(0),
        date_ranges: z
          .array(
            z.object({
              start_date: z.string(),
              end_date: z.string(),
              price: z.number().default(0),
            })
          )
          .default([]),
      })
      .optional(),

    regular_variants: z
      .array(
        z.object({
          name: z.string(),
          price: z.number().default(0),
        })
      )
      .default([])
      .optional(),

    prepaid_percentage: z
      .union([
        z
          .number({ invalid_type_error: "Procent musi być liczbą" })
          .min(0, "Procent zaliczki nie może być ujemny")
          .max(100, "Procent zaliczki nie może być większy niż 100"),
        z.nan(),
        z.null(),
      ])
      .transform((val) => (isNaN(Number(val)) ? null : val))
      .default(null),

    prepaid_percentage_high_season: z
      .union([
        z
          .number({ invalid_type_error: "Procent musi być liczbą" })
          .min(0, "Procent zaliczki nie może być ujemny")
          .max(100, "Procent zaliczki nie może być większy niż 100"),
        z.nan(),
        z.null(),
      ])
      .transform((val) => (isNaN(Number(val)) ? null : val))
      .default(null),
  }),
  organizator_mail: z.string().default(""),
  gallery: z.array(z.string()).default([]),
  cart_section_image: z
    .string()
    .nullable()
    .optional()
    .transform((val) => (val ? val : null)),
  tour_seo: z
    .object({
      keywords: z
        .array(z.string().optional().nullable())
        .default([])
        .nullable(),
      description: z.string().default("").nullable(),
      title: z.string().default("").nullable(),
    })
    .optional()
    .nullable(),
  at_least_one_food_option_required: z.boolean().default(false),
  food_options: z
    .array(
      z
        .object({
          name: foodOptions.optional(),
          description: z.string().default(""),
          price: z.string().default(""),
          required: z.boolean().default(false),
        })
        .optional()
    )
    .default([]),
  blocked_dates: z
    .array(
      z.object({
        date_from: z.string().default(""),
        only_one_day: z.boolean().default(false),
        date_to: z.string().default(""),
      })
    )
    .default([]),
  blocked_dates_by_month: z
    .array(
      z.object({
        month: z.number(),
        days: z.string().default(""),
      })
    )
    .default([]),
  blocked_start_times: z
    .array(
      z.object({
        month: z.number().min(1).max(12),
        days: z.string().default("").nullable(),
        blocked_times: z.array(z.string()).default([]),
      })
    )
    .default([]),
  ticket: z
    .object({
      attraction_time: z.string().default("").nullable(),
      where_to_park: z.string().default("").nullable(),
      meeting_time: z.string().default("").nullable(),
      meeting_place: z.string().default("").nullable(),
      map: z.string().default("").nullable(),
      link_to_download: z.string().default("").nullable(),
      ship_or_transport_description: z.string().default("").nullable(),
      intermediary_name: z.string().default("").nullable(),
      additional_info: z.string().default("").optional().nullable(),
      organizer_name: z.string().default(""),
      organizer_contact: z.string().default(""),
      what_to_bring: z.string().default(""),
      additional_meeting_details: z.string().default(""),
      meeting_image: z.string().default("").nullable(),
    })
    .optional()
    .default({}),
  content_blocks: z
    .object({
      calendar: z.string().optional().default(""),
      details: z.string().optional().default(""),
      description: z.string().optional().default(""),
      how_to_book: z.string().optional().default(""),
      place: z.string().optional().default(""),
      price: z.string().optional().default(""),
      program: z.string().optional().default(""),
      specification_only_for_boats: z.string().optional().default(""),
      ticket: z.string().optional().default(""),
      faq: z
        .array(
          z.object({
            question: z.string().default(""),
            answer: z.string().default(""),
            position: z
              .number({ message: "Pozycja jest wymagana i musi być liczbą" })
              .default(0),
          })
        )
        .default([]),
    })
    .optional()
    .default({}),
  category_ids: z.array(z.string()).default([]),
  cities: z.array(z.string()).min(1, "Przynajmniej jedno miasto jest wymagane"),
};

export const extendedCategoryValidationBody = {
  name: z.string().min(2, "Za króka nazwa"),
  description: z.string().min(2, "Za krótki opis"),
  is_active: z.boolean().default(true),
  metadata: z.object({
    image: z.string().min(2, "Za krótkie zdjęcie"),
    slug: z.string().min(2, "Za krótki slug"),
    seo_title: z.string().optional(),
    seo_description: z.string().optional(),
    seo_keywords: z.array(z.string()).optional(),
  }),
};

export const createTourFormBody = z.object(tourValidationBody);

export const createCityFormBody = z.object({
  name: z.string().min(3, "Za króka nazwa"),
});

export const createExtendedCategoryFormBody = z.object(
  extendedCategoryValidationBody
);

export const createExtendedCategoryPayload =
  createExtendedCategoryFormBody.extend({
    handle: z.string().min(2, "Za krótki slug"),
    is_active: z.boolean().default(true),
  });

const createTourPayload = createTourFormBody.extend({});
const updateTourPayload = createTourFormBody.extend({
  tour_id: z.string().min(3, "Identyfikator atrakcji jest wymagany"),
});

export const toursSchemas = {
  createTourFormBody,
  createTourPayload,
  updateTourPayload,
  createExtendedCategoryFormBody,
  createCityFormBody,
};

export type TCreateTourFormBody = z.infer<typeof createTourFormBody>;
export type TCreateTourPayload = z.infer<typeof createTourPayload>;
export type TUpdateTourPayload = z.infer<typeof updateTourPayload>;
export type TCreateCityFormBody = z.infer<typeof createCityFormBody>;
export type TCreateExtendedCategoryFormBody = z.infer<
  typeof createExtendedCategoryFormBody
>;
export type TCreateExtendedCategoryPayload = z.infer<
  typeof createExtendedCategoryPayload
>;

export interface LegacyTourFormBody extends TCreateTourFormBody {
  recommended_tours?: string[];
}
