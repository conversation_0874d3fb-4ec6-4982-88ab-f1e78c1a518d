import { Button, Input, Select } from "@medusajs/ui";
import { Plus, Trash } from "@medusajs/icons";
import {
  useFieldArray,
  UseFormReturn,
  useWatch,
  Controller,
} from "react-hook-form";
import FormField from "../guides/form-field";
import { TCreateTourFormBody } from "./schemas";
import DeleteWithConfirmation from "../reusable/delete-with-confirmation";

type Props = {
  form: UseFormReturn<TCreateTourFormBody>;
  formValues: ReturnType<typeof useWatch<TCreateTourFormBody>>;
};

const BlockedDatesByMonthInput = ({ form, formValues }: Props) => {
  const MONTHS = [
    { value: 1, label: "Styczeń" },
    { value: 2, label: "<PERSON><PERSON>" },
    { value: 3, label: "Marze<PERSON>" },
    { value: 4, label: "K<PERSON><PERSON>ie<PERSON>" },
    { value: 5, label: "Maj" },
    { value: 6, label: "<PERSON><PERSON>wi<PERSON>" },
    { value: 7, label: "<PERSON>pie<PERSON>" },
    { value: 8, label: "<PERSON><PERSON><PERSON><PERSON>" },
    { value: 9, label: "<PERSON><PERSON>esi<PERSON><PERSON>" },
    { value: 10, label: "Październik" },
    { value: 11, label: "Listopad" },
    { value: 12, label: "Grudzień" },
  ];

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "blocked_dates_by_month",
  });

  return (
    <div className="space-y-6">
      <div className="border rounded-lg p-4 bg-ui-bg-subtle flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <FormField.Label>
            Blokady dat w konkretnych dniach miesiąca
          </FormField.Label>
        </div>

        <div className="flex flex-col gap-4">
          {fields.map((field, index) => (
            <div
              key={field.id}
              className="grid sm:grid-cols-[1fr,1fr,1fr,auto] gap-4 items-end bg-ui-bg-base p-4 rounded-lg border"
            >
              <FormField
                control={form.control}
                name={`blocked_dates_by_month.${index}.month`}
              >
                <FormField.Label className="text-sm">Miesiąc</FormField.Label>

                <Controller
                  control={form.control}
                  name={`blocked_dates_by_month.${index}.month`}
                  render={({ field }) => (
                    <Select
                      value={field.value.toString()}
                      onValueChange={(value) => {
                        field.onChange(Number(value));
                      }}
                    >
                      <Select.Trigger>
                        <Select.Value placeholder="Wybierz miesiąc" />
                      </Select.Trigger>
                      <Select.Content>
                        {MONTHS.map((month) => (
                          <Select.Item
                            key={month.value}
                            value={month.value.toString()}
                          >
                            {month.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                />
              </FormField>

              <FormField
                control={form.control}
                name={`blocked_dates_by_month.${index}.days`}
              >
                <FormField.Label className="text-sm">
                  Dni (po przecinku)
                </FormField.Label>
                <Input
                  {...form.register(`blocked_dates_by_month.${index}.days`)}
                  placeholder="np. 1,15,30"
                />
              </FormField>

              <DeleteWithConfirmation onDelete={() => remove(index)}>
                <Button variant="danger" className="h-8 w-8" size="small">
                  <Trash className="w-4 h-4" />
                </Button>
              </DeleteWithConfirmation>
            </div>
          ))}
        </div>
        <Button
          variant="primary"
          onClick={() =>
            append({
              month: 1,
              days: "",
            })
          }
          className={`h-8 ml-auto ${fields.length > 0 ? "auto-24" : "w-full"}`}
        >
          <Plus className="w-4 h-4" />
          Nowy blok
        </Button>
      </div>
    </div>
  );
};

export default BlockedDatesByMonthInput;
