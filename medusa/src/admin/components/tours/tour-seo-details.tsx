import FormField from "../guides/form-field";
import { Input, Kbd, Textarea } from "@medusajs/ui";
import { UseFormReturn, useWatch } from "react-hook-form";
import TagsInput from "../guides/tags-input";
import { TCreateTourFormBody } from "./schemas";

type Props = {
  form: UseFormReturn<TCreateTourFormBody>;
  formValues: ReturnType<typeof useWatch<TCreateTourFormBody>>;
};

const TourSeoDetails = ({ form, formValues }: Props) => {
  return (
    <div className="grow h-full max-w-screen-lg mx-auto w-full my-4 space-y-4">
      <FormField control={form.control} name="tour_seo.title">
        <FormField.Label>Meta tytuł</FormField.Label>
        <Input {...form.register("tour_seo.title")} />
      </FormField>

      <FormField control={form.control} name="tour_seo.description">
        <FormField.Label>Meta opis</FormField.Label>
        <Textarea
          {...form.register("tour_seo.description")}
          placeholder="Opis meta dla SEO - powinien zawierać od 50 do 160 znaków"
        />
      </FormField>

      <FormField control={form.control} name="tour_seo.keywords">
        <FormField.Label>Słowa kluczowe - Tagi</FormField.Label>
        <FormField.Note>
          Wpisz słowo kluczowe a następnie zatwierdź je przez <Kbd>Enter</Kbd>{" "}
          lub <Kbd>,</Kbd>
        </FormField.Note>
        <TagsInput
          field_key="tour_seo.keywords"
          form={form as unknown as UseFormReturn}
          formValues={formValues}
        />
        <div className="h-[55vh]" />
      </FormField>
    </div>
  );
};

export default TourSeoDetails;
