import { Trash, Plus } from "@medusajs/icons";
import { <PERSON>ton, Switch } from "@medusajs/ui";
import {
  Controller,
  useWatch,
  UseFormReturn,
  useFieldArray,
} from "react-hook-form";
import {
  safeParseStringToDate,
  safeParseDateToString,
} from "../../lib/util/string-dates";
import FormField from "../guides/form-field";
import { TCreateTourFormBody } from "./schemas";
import DedicatedDatePicker from "../reusable/dedicated-datepicker";
import DeleteWithConfirmation from "../reusable/delete-with-confirmation";

type Props = {
  form: UseFormReturn<TCreateTourFormBody>;
  formValues: ReturnType<typeof useWatch<TCreateTourFormBody>>;
};

const BlockedDatesInput = ({ form, formValues }: Props) => {
  const blockedDatesArray = useFieldArray({
    control: form.control,
    name: "blocked_dates",
  });

  return (
    <div className="flex flex-col gap-4 p-4 border rounded-lg bg-ui-bg-subtle">
      <FormField.Label>Zablokowane okresy dat</FormField.Label>
      {blockedDatesArray.fields.map((field, index) => (
        <div
          key={field.id}
          className="grid sm:grid-cols-3 gap-4 border bg-ui-bg-base rounded-md p-4 items-end mb-4 relative"
        >
          <FormField
            control={form.control}
            name={`blocked_dates.${index}.date_from`}
          >
            <FormField.Label>Data od</FormField.Label>
            <Controller
              control={form.control}
              name={`blocked_dates.${index}.date_from`}
              render={({ field }) => (
                <DedicatedDatePicker
                  value={safeParseStringToDate(field.value)}
                  setValue={(date) =>
                    field.onChange(safeParseDateToString(date))
                  }
                />
              )}
            />
          </FormField>

          <FormField
            control={form.control}
            name={`blocked_dates.${index}.date_to`}
          >
            <FormField.Label>Data do</FormField.Label>
            <Controller
              control={form.control}
              name={`blocked_dates.${index}.date_to`}
              render={({ field }) => (
                <DedicatedDatePicker
                  value={safeParseStringToDate(field.value)}
                  setValue={(date) =>
                    field.onChange(safeParseDateToString(date))
                  }
                />
              )}
            />
          </FormField>

          <FormField
            control={form.control}
            name={`blocked_dates.${index}.only_one_day`}
          >
            <FormField.Label>Tylko jeden dzień</FormField.Label>
            <Switch
              checked={formValues.blocked_dates?.[index]?.only_one_day}
              onCheckedChange={(changed) =>
                form.setValue(`blocked_dates.${index}.only_one_day`, changed)
              }
            />
          </FormField>

          <DeleteWithConfirmation
            onDelete={() => blockedDatesArray.remove(index)}
          >
            <Button
              variant="danger"
              size="small"
              className="absolute -top-2 -right-2 size-8 shadow-md hover:shadow-lg"
            >
              <Trash className="w-4 h-4" />
            </Button>
          </DeleteWithConfirmation>
        </div>
      ))}

      <Button
        className={`ml-auto self-end transition-[width] ease-out ${
          blockedDatesArray.fields.length > 0 ? "w-32" : "w-full h-8"
        }`}
        onClick={() =>
          blockedDatesArray.append({
            date_from: "",
            date_to: "",
            only_one_day: false,
          })
        }
      >
        <Plus className="w-4 h-4" />
        Dodaj termin
      </Button>
    </div>
  );
};

export default BlockedDatesInput;
