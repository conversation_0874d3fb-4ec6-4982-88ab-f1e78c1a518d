import React, { useEffect, useRef, useState } from 'react'
import { QUERY_KEYS } from '../../lib/constants/query-keys';
import { useQuery } from '@tanstack/react-query';
import { getExtendedCategories } from '../../lib/api/categories';
import { TExtendedCategory } from '../../types';

type Props = {
  value: string[];
  onChange: (value: string[]) => void;
}

const CategoryInput = ({value, onChange}: Props) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  
    const { data, isFetching, refetch } = useQuery({
    queryKey: [QUERY_KEYS.ALL_CATEGORIES],
    queryFn: getExtendedCategories,
  });
    
  const categories = data || [];
  
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Filter tours based on search and already selected items
  const filteredCategories = categories.filter(
    (category: TExtendedCategory) =>
      !value.includes(category.id) &&
      category.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelect = (categoryId: string) => {
    if (!value.includes(categoryId)) {
      onChange([...value, categoryId]);
      setIsOpen(false);
      setSearchTerm("");
    }
  };

  const handleRemove = (tourId: string) => {
    onChange(value.filter((id) => id !== tourId));
  };

  const renderDropdownContent = () => {
    if (isFetching) {
      return <div className="p-2 text-sm">Pobieranie kategorii...</div>;
    }

    if (!filteredCategories.length) {
      return <div className="p-2 text-sm">Brak dostępnych kategorii</div>;
    }

    return filteredCategories.map((category: TExtendedCategory) => (
      <button
        key={category.id}
        onClick={() => handleSelect(category.id)}
        className="w-full px-3 py-2 text-left hover:bg-ui-bg-base-hover flex items-center gap-x-2"
      >
        <img
          src={category.metadata?.image}
          alt={category.name}
          className="size-4 rounded-full"
        />
        <span className="text-sm block">{category.name}</span>
      </button>
    ));
  };

  return (
    <div className="flex flex-col gap-y-2">
      <div className="flex flex-col gap-y-1">
        <div className="relative" ref={dropdownRef}>
          <input
            type="text"
            placeholder="Przypisz atrakcję do kategorii"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onFocus={() => setIsOpen(true)}
            className="w-full px-3 py-2 bg-ui-bg-field border border-ui-border-base shadow-sm rounded-md text-sm"
          />
                    
          {isOpen && (
            <div className="absolute z-50 w-full mt-1 bg-ui-bg-base border rounded-md shadow-lg max-h-[300px] overflow-y-auto">
              {renderDropdownContent()}
            </div>
          )}
        </div>
      </div>

      {/* Display selected tours */}
      {value.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-2">
          {value.map((categoryId) => {
            const category = categories.find((c: TExtendedCategory) => c.id === categoryId);
            return category ? (
              <div
                key={categoryId}
                className="flex items-center gap-x-2 bg-ui-bg-base border border-ui-border-base px-3 py-1.5 rounded-lg"
              >
                <img
                  src={category.metadata?.image}
                  alt={category.name}
                  className="size-4 rounded-full"
                />
                <span className="text-sm">{category.name}</span>
                <button
                  type="button"
                  onClick={() => handleRemove(categoryId)}
                  className="text-ui-text-muted hover:text-ui-text-muted ml-1"
                >
                  ×
                </button>
              </div>
            ) : null;
          })}
        </div>
      )}
    </div>
  );
}

export default CategoryInput