import { Input, Switch } from "@medusajs/ui";
import {
  Control,
  Controller,
  UseFormRegister,
  useWatch,
} from "react-hook-form";
import { TCreateTourFormBody } from "./schemas";
import FormField from "../guides/form-field";
import FormGrid from "../reusable/form-grid";
import ArrayInput from "../reusable/array-input";

type Props = {
  control: Control<TCreateTourFormBody>;
  register: UseFormRegister<TCreateTourFormBody>;
  name: "start_times";
  label: string;
};

const TimeIntervalInput = ({ control, register, name, label }: Props) => {
  const formValues = useWatch({
    control,
  });

  return (
    <FormField
      control={control}
      name={name}
      className="border rounded-lg p-4 bg-ui-bg-subtle flex flex-col gap-4"
    >
      <FormField.Label>{label}</FormField.Label>

      <div className="flex items-center gap-4 mb-2">
        <FormField.Label className="text-sm">
          Użyj ręcznie zdefiniowanych godzin
        </FormField.Label>
        <Controller
          control={control}
          name={`${name}.use_manual_start_times`}
          render={({ field }) => (
            <Switch
              className="mb-2"
              checked={field.value}
              onCheckedChange={field.onChange}
            />
          )}
        />
      </div>

      {!formValues.start_times?.use_manual_start_times ? (
        <FormGrid gridCols={3}>
          <div className="space-y-2">
            <FormField.Label className="text-sm">
              Start (godzina)
            </FormField.Label>
            <Input type="time" {...register(`${name}.start`)} />
          </div>
          <div className="space-y-2">
            <FormField.Label className="text-sm">
              Koniec (godzina)
            </FormField.Label>
            <Input type="time" {...register(`${name}.stop`)} />
          </div>
          <div className="space-y-2">
            <FormField.Label className="text-sm">
              Interwał (minuty)
            </FormField.Label>
            <Input
              type="number"
              min={5}
              max={300}
              step={5}
              {...register(`${name}.interval`, { valueAsNumber: true })}
            />
          </div>
        </FormGrid>
      ) : (
        <ArrayInput
          name={`${name}.manual_start_times`}
          control={control}
          register={register}
          label="Godziny rozpoczęcia"
          placeholder="np. 10:00"
          actionLabel="Dodaj godzinę"
          inputType="time"
        />
      )}
    </FormField>
  );
};

export default TimeIntervalInput;
