import { useState, useRef, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { QUERY_KEYS } from "../../lib/constants/query-keys";
import { getAllToursForRecommendation } from "../../lib/api/tours";
import { ArrowDown, XMark } from "@medusajs/icons";
import DeleteWithConfirmation from "../reusable/delete-with-confirmation";
import { Button } from "@medusajs/ui";

interface TourType {
  id: string;
  name: string;
  featured_image: string | null;
}

type RelatedToursInputProps = {
  value: string[];
  onChange: (value: string[]) => void;
};

const RelatedToursInput = ({ value, onChange }: RelatedToursInputProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isOpen, setIsOpen] = useState(false);

  const { data, isLoading } = useQuery({
    queryKey: [QUERY_KEYS.ALL_TOURS_FOR_RECOMMENDATION],
    queryFn: getAllToursForRecommendation,
  });

  const tours = data?.tours || [];

  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Filter tours based on search and already selected items
  const filteredTours = tours.filter(
    (tour: TourType) =>
      !value.includes(tour.id) &&
      tour.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelect = (tourId: string) => {
    if (!value.includes(tourId)) {
      onChange([...value, tourId]);
      setIsOpen(false);
      setSearchTerm("");
    }
  };

  const handleRemove = (tourId: string) => {
    onChange(value.filter((id) => id !== tourId));
  };

  const handleMoveUp = (index: number) => {
    if (index === 0) return;
    const newValue = [...value];
    [newValue[index - 1], newValue[index]] = [
      newValue[index],
      newValue[index - 1],
    ];
    onChange(newValue);
  };

  const handleMoveDown = (index: number) => {
    if (index === value.length - 1) return;
    const newValue = [...value];
    [newValue[index], newValue[index + 1]] = [
      newValue[index + 1],
      newValue[index],
    ];
    onChange(newValue);
  };

  const renderDropdownContent = () => {
    if (isLoading) {
      return <div className="p-2 text-sm">Pobieranie atrakcji...</div>;
    }

    if (!filteredTours.length) {
      return <div className="p-2 text-sm">Brak dostępnych atrakcji</div>;
    }

    return filteredTours.map((tour: TourType) => (
      <button
        key={tour.id}
        onClick={() => handleSelect(tour.id)}
        className="w-full px-3 py-2 text-left hover:bg-ui-bg-base-hover flex items-center gap-x-2"
      >
        <img
          src={tour.featured_image || ""}
          alt={tour.name}
          className="size-4 rounded-full"
        />
        <span className="text-sm block">{tour.name}</span>
      </button>
    ));
  };

  return (
    <div className="flex flex-col gap-y-2">
      <div className="flex flex-col gap-y-1">
        <div className="relative" ref={dropdownRef}>
          <input
            type="text"
            placeholder="Wyszukaj powiązane atrakcje spośród dotychczas dodanych"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onFocus={() => setIsOpen(true)}
            className="w-full px-3 py-2 bg-ui-bg-field border border-ui-border-base shadow-sm rounded-md text-sm"
          />
          {isOpen && (
            <div className="absolute z-50 w-full mt-1 bg-ui-bg-base border rounded-md shadow-lg max-h-[300px] overflow-y-auto">
              {renderDropdownContent()}
            </div>
          )}
        </div>
      </div>

      {/* Display selected tours */}
      {value.length > 0 && (
        <div className="flex flex-col gap-2 mt-2">
          {value.map((tourId, index) => {
            const tour = tours.find((t: TourType) => t.id === tourId);
            return tour ? (
              <div
                key={tourId}
                className="flex items-center justify-between gap-x-2 bg-ui-bg-base border border-ui-border-base px-3 py-1.5 rounded-lg"
              >
                <div className="flex items-center gap-x-2 flex-grow">
                  <div className="flex items-center gap-x-2">
                    <span className="text-sm">{index + 1}.</span>
                  </div>

                  <img
                    src={tour.featured_image || ""}
                    alt={tour.name}
                    className="h-8 aspect-video rounded-md"
                  />
                  <span className="text-sm">{tour.name}</span>
                </div>
                <div className="flex items-center gap-x-1">
                  <div className="flex flex-col gap-2 mr-6">
                    <button
                      type="button"
                      onClick={() => handleMoveUp(index)}
                      disabled={index === 0}
                      className={`text-ui-text-muted hover:text-ui-text-base p-2 border rounded-md aspect-square h-6 bg-ui-bg-component grid place-content-center focus:outline-none px-1 ${
                        index === 0 ? "opacity-30 cursor-not-allowed" : ""
                      }`}
                      title="Przesuń w górę"
                    >
                      <ArrowDown className="size-4 rotate-180" />
                    </button>
                    <button
                      type="button"
                      onClick={() => handleMoveDown(index)}
                      disabled={index === value.length - 1}
                      className={`text-ui-text-muted hover:text-ui-text-base p-2 border rounded-md aspect-square h-6 bg-ui-bg-component grid place-content-center focus:outline-none px-1 ${
                        index === value.length - 1
                          ? "opacity-30 cursor-not-allowed"
                          : ""
                      }`}
                      title="Przesuń w dół"
                    >
                      <ArrowDown className="size-4" />
                    </button>
                  </div>

                  <DeleteWithConfirmation onDelete={() => handleRemove(tourId)}>
                    <Button variant="danger" size="small">
                      <XMark />
                    </Button>
                  </DeleteWithConfirmation>
                </div>
              </div>
            ) : null;
          })}
        </div>
      )}
    </div>
  );
};

export default RelatedToursInput;
