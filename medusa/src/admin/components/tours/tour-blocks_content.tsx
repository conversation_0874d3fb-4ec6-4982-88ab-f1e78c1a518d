import FormField from "../guides/form-field";
import { UseFormReturn } from "react-hook-form";
import RichTextEditor from "../rich-text-editor";
import { TCreateTourFormBody } from "./schemas";
import FaqField from "./faq-field";
import FormGrid from "../reusable/form-grid";
import { Input, Checkbox } from "@medusajs/ui";

type Props = {
  form: UseFormReturn<TCreateTourFormBody>;
};

const TourBlocksContent = ({ form }: Props) => {
  return (
    <>
      <div className="border rounded-md p-4">
        <FormGrid>
          <FormField.Label className="col-span-full">
            Informacje wyświetlane jako szczegóły atrakcji.
          </FormField.Label>
          <FormField.Note className="col-span-full">
            Jeśli pole nie zostanie wypełnione, atrybut nie będzie wyświetlany.
          </FormField.Note>
          <FormField control={form.control} name="price_from">
            <FormField.Label>Cena od</FormField.Label>
            <Input {...form.register("price_from")} />
          </FormField>

          <FormField control={form.control} name="dates_from_to">
            <FormField.Label>Okres od - do</FormField.Label>
            <Input type="text" {...form.register("dates_from_to")} />
          </FormField>

          <FormField control={form.control} name="hourly_length">
            <FormField.Label>Czas trwania</FormField.Label>
            <Input {...form.register("hourly_length")} />
          </FormField>
        </FormGrid>
      </div>

      <div className="border rounded-md p-4">
        <FormGrid>
          <FormField.Label className="col-span-full">
            Opcje promocyjne i wyróżnienia
          </FormField.Label>
          <FormField.Note className="col-span-full">
            Zaznacz odpowiednie opcje, aby wyróżnić atrakcję na stronie głównej.
          </FormField.Note>

          <FormField control={form.control} name="is_recommended">
            <FormField.Label>Polecane</FormField.Label>
            <Checkbox
              checked={form.watch("is_recommended")}
              onCheckedChange={(checked) => {
                form.setValue("is_recommended", Boolean(checked));
              }}
            />
          </FormField>

          <FormField control={form.control} name="is_bargain">
            <FormField.Label>Okazja</FormField.Label>
            <Checkbox
              checked={form.watch("is_bargain")}
              onCheckedChange={(checked) => {
                form.setValue("is_bargain", Boolean(checked));
              }}
            />
          </FormField>

          <FormField control={form.control} name="is_promotion">
            <FormField.Label>Promocja</FormField.Label>
            <Checkbox
              checked={form.watch("is_promotion")}
              onCheckedChange={(checked) => {
                form.setValue("is_promotion", Boolean(checked));
              }}
            />
          </FormField>

          <FormField control={form.control} name="promotion_price_from">
            <FormField.Label>Cena promocyjna od</FormField.Label>
            <Input {...form.register("promotion_price_from")} />
          </FormField>
        </FormGrid>
      </div>

      <FormField control={form.control} name="content_blocks.description">
        <FormField.Label>Opis</FormField.Label>

        <RichTextEditor
          content={form.watch("content_blocks.description")}
          onChange={(content) =>
            form.setValue("content_blocks.description", content)
          }
        />
      </FormField>

      <FormField control={form.control} name="content_blocks.calendar">
        <FormField.Label>Kalendarz</FormField.Label>
        <RichTextEditor
          content={form.watch("content_blocks.calendar")}
          onChange={(content) =>
            form.setValue("content_blocks.calendar", content)
          }
        />
      </FormField>

      <FormField control={form.control} name="content_blocks.price">
        <FormField.Label>Cena</FormField.Label>
        <RichTextEditor
          content={form.watch("content_blocks.price")}
          onChange={(content) => form.setValue("content_blocks.price", content)}
        />
      </FormField>

      <FormField control={form.control} name="content_blocks.how_to_book">
        <FormField.Label>Jak zarezerwować</FormField.Label>
        <RichTextEditor
          content={form.watch("content_blocks.how_to_book")}
          onChange={(content) =>
            form.setValue("content_blocks.how_to_book", content)
          }
        />
      </FormField>

      <FormField control={form.control} name="content_blocks.ticket">
        <FormField.Label>Bilet</FormField.Label>
        <RichTextEditor
          content={form.watch("content_blocks.ticket")}
          onChange={(content) =>
            form.setValue("content_blocks.ticket", content)
          }
        />
      </FormField>

      <FormField
        control={form.control}
        name="content_blocks.specification_only_for_boats"
      >
        <FormField.Label>Specyfikacja - tylko dla łodzi</FormField.Label>
        <RichTextEditor
          content={
            form.watch("content_blocks.specification_only_for_boats") ?? ""
          }
          onChange={(content) =>
            form.setValue(
              "content_blocks.specification_only_for_boats",
              content
            )
          }
        />
      </FormField>

      <FormField control={form.control} name="content_blocks.program">
        <FormField.Label>Program</FormField.Label>
        <RichTextEditor
          content={form.watch("content_blocks.program")}
          onChange={(content) =>
            form.setValue("content_blocks.program", content)
          }
        />
      </FormField>

      <FormField control={form.control} name="content_blocks.details">
        <FormField.Label>Szczegóły</FormField.Label>
        <RichTextEditor
          content={form.watch("content_blocks.details")}
          onChange={(content) =>
            form.setValue("content_blocks.details", content)
          }
        />
      </FormField>

      <FormField control={form.control} name="content_blocks.place">
        <FormField.Label>Miejsce</FormField.Label>
        <RichTextEditor
          content={form.watch("content_blocks.place")}
          onChange={(content) => form.setValue("content_blocks.place", content)}
        />
      </FormField>

      <FaqField form={form} />
    </>
  );
};

export default TourBlocksContent;
