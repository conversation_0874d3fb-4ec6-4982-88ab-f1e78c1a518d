import FormField from "../guides/form-field";
import { UseFormReturn, useWatch } from "react-hook-form";
import RichTextEditor from "../rich-text-editor";
import { TCreateTourFormBody } from "./schemas";
import FormGrid from "../reusable/form-grid";
import ImageDropzone from "../reusable/image_dropzone";

type Props = {
  form: UseFormReturn<TCreateTourFormBody>;
  formValues: ReturnType<typeof useWatch<TCreateTourFormBody>>;
};

const TourTicket = ({ form, formValues }: Props) => {
  return (
    <>
      <FormField control={form.control} name="ticket.attraction_time">
        <FormField.Label>Zdjęcie zbiórki</FormField.Label>
        <ImageDropzone
          syncedWithForm
          form={form}
          field_key="ticket.meeting_image"
        />
      </FormField>

      <FormField control={form.control} name="ticket.attraction_time">
        <FormField.Label>Godzin<PERSON> at<PERSON></FormField.Label>
        <RichTextEditor
          content={form.watch("ticket.attraction_time")}
          onChange={(content) =>
            form.setValue("ticket.attraction_time", content)
          }
        />
      </FormField>

      <FormField control={form.control} name="ticket.meeting_place">
        <FormField.Label>Miejsce zbiórki</FormField.Label>
        <RichTextEditor
          content={form.watch("ticket.meeting_place")}
          onChange={(content) => form.setValue("ticket.meeting_place", content)}
        />
      </FormField>

      <FormField control={form.control} name="ticket.meeting_time">
        <FormField.Label>Godzina zbiórki</FormField.Label>
        <RichTextEditor
          content={form.watch("ticket.meeting_time")}
          onChange={(content) => form.setValue("ticket.meeting_time", content)}
        />
      </FormField>

      <FormField control={form.control} name="ticket.map">
        <FormField.Label>Mapa</FormField.Label>
        <RichTextEditor
          content={form.watch("ticket.map")}
          onChange={(content) => form.setValue("ticket.map", content)}
        />
      </FormField>

      <FormField
        control={form.control}
        name="ticket.additional_meeting_details"
      >
        <FormField.Label>Dodatkowe informacje o zbiórce</FormField.Label>
        <RichTextEditor
          content={form.watch("ticket.additional_meeting_details")}
          onChange={(content) =>
            form.setValue("ticket.additional_meeting_details", content)
          }
        />
      </FormField>

      <FormGrid gridCols={2}>
        <FormField control={form.control} name="ticket.intermediary_name">
          <FormField.Label>Nazwa pośrednika</FormField.Label>
          <RichTextEditor
            compressed_version
            content={form.watch("ticket.intermediary_name")}
            onChange={(content) =>
              form.setValue("ticket.intermediary_name", content)
            }
          />
        </FormField>

        <FormField control={form.control} name="ticket.organizer_name">
          <FormField.Label>Nazwa organizatora</FormField.Label>
          <RichTextEditor
            compressed_version
            content={form.watch("ticket.organizer_name")}
            onChange={(content) =>
              form.setValue("ticket.organizer_name", content)
            }
          />
        </FormField>

        <FormField control={form.control} name="ticket.organizer_contact">
          <FormField.Label>Kontakt do organizatora</FormField.Label>
          <RichTextEditor
            compressed_version
            content={form.watch("ticket.organizer_contact")}
            onChange={(content) =>
              form.setValue("ticket.organizer_contact", content)
            }
          />
        </FormField>

        <FormField control={form.control} name="ticket.link_to_download">
          <FormField.Label>Link do pobrania</FormField.Label>
          <RichTextEditor
            compressed_version
            content={form.watch("ticket.link_to_download")}
            onChange={(content) =>
              form.setValue("ticket.link_to_download", content)
            }
          />
        </FormField>

        <FormField control={form.control} name="ticket.what_to_bring">
          <FormField.Label>Co ze sobą zabrać</FormField.Label>
          <RichTextEditor
            compressed_version
            content={form.watch("ticket.what_to_bring")}
            onChange={(content) =>
              form.setValue("ticket.what_to_bring", content)
            }
          />
        </FormField>

        <FormField control={form.control} name="ticket.where_to_park">
          <FormField.Label>Gdzie zaparkować</FormField.Label>
          <RichTextEditor
            compressed_version
            content={form.watch("ticket.where_to_park")}
            onChange={(content) =>
              form.setValue("ticket.where_to_park", content)
            }
          />
        </FormField>

        <FormField
          control={form.control}
          name="ticket.ship_or_transport_description"
        >
          <FormField.Label>
            Nazwa statku lub opis środka transportu
          </FormField.Label>
          <RichTextEditor
            compressed_version
            content={form.watch("ticket.ship_or_transport_description")}
            onChange={(content) =>
              form.setValue("ticket.ship_or_transport_description", content)
            }
          />
        </FormField>
      </FormGrid>

      <FormField control={form.control} name="ticket.additional_info">
        <FormField.Label>Dodatkowe informacje</FormField.Label>
        <RichTextEditor
          content={form.watch("ticket.additional_info") ?? ""}
          onChange={(content) =>
            form.setValue("ticket.additional_info", content)
          }
        />
      </FormField>
    </>
  );
};

export default TourTicket;
