import { Button, Input, Select } from "@medusajs/ui";
import {
  Controller,
  useFieldArray,
  UseFormReturn,
  useWatch,
} from "react-hook-form";
import { productTypesMap, TCreateTourFormBody } from "./schemas";
import FormField from "../guides/form-field";
import { Plus, Trash } from "@medusajs/icons";
import { safeParseDateToString } from "../../lib/util/string-dates";
import { safeParseStringToDate } from "../../lib/util/string-dates";
import { SimpleEuroInput } from "../reusable/simple-euro-input";
import DedicatedDatePicker from "../reusable/dedicated-datepicker";
import DeleteWithConfirmation from "../reusable/delete-with-confirmation";

type Props = {
  form: UseFormReturn<TCreateTourFormBody>;
  formValues: ReturnType<typeof useWatch<TCreateTourFormBody>>;
};

const PriceVariants = ({ form, formValues }: Props) => {
  // Main array for age groups
  const ageGroupsArray = useFieldArray({
    name: "pricing.age_groups",
    control: form.control,
  });

  // Array for date-specific pricing
  const dateVariantsArray = useFieldArray({
    name: "pricing.date_variants",
    control: form.control,
  });

  const addNewAgeGroup = () => {
    ageGroupsArray.append({
      name: "",
      price: null,
    });
  };

  const addNewDateVariant = () => {
    dateVariantsArray.append({
      start_date: "",
      end_date: "",
      age_group_prices: ageGroupsArray.fields.map(() => ({
        price: null,
        discount_price: null,
      })),
    });
  };

  const {
    fields: basicVariantsArray,
    append: appendBasicVariant,
    remove: removeBasicVariant,
  } = useFieldArray({
    name: "pricing.regular_variants",
    control: form.control,
  });

  const {
    fields: boatVariantsArray,
    append: appendBoatVariant,
    remove: removeBoatVariant,
  } = useFieldArray({
    name: "pricing.boat_variants.date_ranges",
    control: form.control,
  });

  const renderConditionalFields = () => {
    switch (formValues.product_pricing_variant) {
      case "dynamic_age_groups":
        return (
          <>
            <div className="border rounded-md p-4 bg-ui-bg-component grid sm:grid-cols-3 gap-4">
              <div className="flex justify-between items-center mb-3 col-span-full">
                <FormField.Label>Grupy wiekowe (warianty)</FormField.Label>
              </div>

              {/* Base age groups */}
              {ageGroupsArray.fields.map((field, index) => (
                <div
                  key={field.id}
                  className="grid grid-cols-[1fr,1fr] bg-ui-bg-base gap-4 relative border rounded-md p-4 pt-6 items-end mb-4"
                >
                  <FormField
                    control={form.control}
                    name={`pricing.age_groups.${index}.name`}
                  >
                    <FormField.Label className="text-sm">
                      Nazwa wariantu
                    </FormField.Label>
                    <Input
                      {...form.register(`pricing.age_groups.${index}.name`)}
                    />
                  </FormField>

                  <FormField
                    control={form.control}
                    name={`pricing.age_groups.${index}.price`}
                  >
                    <FormField.Label className="text-sm">
                      Cena bazowa (EUR)
                    </FormField.Label>
                    <SimpleEuroInput
                      name={`pricing.age_groups.${index}.price`}
                      register={form.register}
                      form={form}
                    />
                  </FormField>

                  <DeleteWithConfirmation
                    onDelete={() => ageGroupsArray.remove(index)}
                  >
                    <Button
                      variant="danger"
                      type="button"
                      size="small"
                      className="h-8 absolute -top-2 -right-2 shadow-md hover:shadow-lg"
                    >
                      <Trash className="w-4 h-4" />
                    </Button>
                  </DeleteWithConfirmation>
                </div>
              ))}

              <Button
                variant="primary"
                onClick={addNewAgeGroup}
                type="button"
                className={`mb-2 ml-auto col-span-full self-end transition-[width] ease-out ${
                  ageGroupsArray.fields.length > 0 ? "w-auto" : "w-full h-8"
                }`}
              >
                <Plus className="w-4 h-4" />
                Dodaj wariant
              </Button>
            </div>

            {/* Date-specific pricing section */}
            <div className="border rounded-md p-3 bg-ui-bg-component flex flex-col w-full">
              <div className="flex justify-between items-center mb-3 ">
                <FormField.Label>Warianty cenowe dla dat</FormField.Label>
              </div>

              <div className="flex flex-wrap gap-3">
                {dateVariantsArray.fields.map((dateVariant, dateIndex) => (
                  <div
                    key={dateVariant.id}
                    className="border rounded-md p-3 bg-ui-bg-base hover:bg-ui-bg-base-hover relative"
                  >
                    <DeleteWithConfirmation
                      onDelete={() => dateVariantsArray.remove(dateIndex)}
                    >
                      <Button
                        variant="danger"
                        type="button"
                        size="small"
                        className="h-8 w-8 absolute -top-2 -right-2 shadow-md hover:shadow-lg"
                      >
                        <Trash className="w-4 h-4" />
                      </Button>
                    </DeleteWithConfirmation>

                    <div className="w-full">
                      <div className="grid sm:grid-cols-2 gap-2 w-full">
                        <FormField
                          control={form.control}
                          name={`pricing.date_variants.${dateIndex}.start_date`}
                        >
                          <FormField.Label className="text-xs text-ui-fg-subtle">
                            Od
                          </FormField.Label>
                          <Controller
                            control={form.control}
                            name={`pricing.date_variants.${dateIndex}.start_date`}
                            render={({ field }) => (
                              <DedicatedDatePicker
                                value={safeParseStringToDate(field.value)}
                                setValue={(date) =>
                                  field.onChange(safeParseDateToString(date))
                                }
                              />
                            )}
                          />
                        </FormField>

                        <FormField
                          control={form.control}
                          name={`pricing.date_variants.${dateIndex}.end_date`}
                        >
                          <FormField.Label className="text-xs text-ui-fg-subtle">
                            Do
                          </FormField.Label>
                          <Controller
                            control={form.control}
                            name={`pricing.date_variants.${dateIndex}.end_date`}
                            render={({ field }) => (
                              <DedicatedDatePicker
                                value={safeParseStringToDate(field.value)}
                                setValue={(date) =>
                                  field.onChange(safeParseDateToString(date))
                                }
                              />
                            )}
                          />
                        </FormField>
                      </div>

                      {/* Pricing for each age group in this date range */}
                      <div className="mt-2">
                        {ageGroupsArray.fields.map((ageGroup, ageIndex) => (
                          <div
                            key={`${dateVariant.id}-${ageGroup.id}`}
                            className="flex items-center justify-between gap-2 border-t pt-2 max-sm:mt-4 max-sm:pt-4  "
                          >
                            <div className="text-xs  font-medium text-ui-fg-subtle min-w-[100px]">
                              {form.watch(
                                `pricing.age_groups.${ageIndex}.name`
                              ) || "Nowa grupa"}
                            </div>
                            <FormField
                              control={form.control}
                              name={`pricing.date_variants.${dateIndex}.age_group_prices.${ageIndex}.price`}
                            >
                              <SimpleEuroInput
                                name={`pricing.date_variants.${dateIndex}.age_group_prices.${ageIndex}.price`}
                                register={form.register}
                                form={form}
                              />
                            </FormField>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <Button
                variant="primary"
                onClick={addNewDateVariant}
                type="button"
                size="small"
                className={`h-8 mt-4 ml-auto transition-[width] ease-out ${
                  dateVariantsArray.fields.length > 0 ? "w-auto" : "w-full h-8"
                }`}
              >
                <Plus className="w-4 h-4" />
                Dodaj wariant cenowy
              </Button>
            </div>
          </>
        );
      case "regular_variants":
        return (
          <>
            <div className="grid sm:grid-cols-2 gap-4 ">
              {basicVariantsArray.map((variant, index) => (
                <div
                  key={variant.id}
                  className="flex items-center gap-4 border p-4 rounded-md relative"
                >
                  <FormField
                    control={form.control}
                    className="w-full"
                    name={`pricing.regular_variants.${index}.name`}
                  >
                    <FormField.Label>Nazwa wariantu</FormField.Label>
                    <Input
                      type="text"
                      className="w-full grow"
                      placeholder="np. Wypożyczenie na 180 minut"
                      {...form.register(
                        `pricing.regular_variants.${index}.name`
                      )}
                    />
                  </FormField>
                  <FormField
                    control={form.control}
                    name={`pricing.regular_variants.${index}.price`}
                  >
                    <FormField.Label>Cena (EUR)</FormField.Label>

                    <SimpleEuroInput
                      name={`pricing.regular_variants.${index}.price`}
                      register={form.register}
                      form={form}
                    />
                  </FormField>
                  <DeleteWithConfirmation
                    onDelete={() => removeBasicVariant(index)}
                  >
                    <Button
                      variant="danger"
                      type="button"
                      size="small"
                      className="absolute size-8 -top-2 -right-2 shadow-md hover:shadow-lg"
                    >
                      <Trash className="w-4 h-4" />
                    </Button>
                  </DeleteWithConfirmation>
                </div>
              ))}

              <Button
                variant="primary"
                onClick={() => appendBasicVariant({ name: "", price: 0 })}
                type="button"
                className={`self-end ml-auto mt-4 col-span-full transition-[width] ease-out ${
                  basicVariantsArray.length > 0 ? "w-auto" : "w-full h-8"
                }`}
              >
                <Plus className="w-4 h-4" />
                Dodaj wariant cenowy
              </Button>
            </div>
          </>
        );
      case "boat_rental":
        return (
          <>
            <div>
              <FormField
                control={form.control}
                name="pricing.boat_variants.base_price"
              >
                <FormField.Label>Cena bazowa (EUR)</FormField.Label>
                <Input
                  type="number"
                  {...form.register("pricing.boat_variants.base_price", {
                    valueAsNumber: true,
                  })}
                />
              </FormField>

              <div className="flex flex-col gap-4 mt-8">
                <div className="flex flex-col gap-2">
                  <FormField.Label>Warianty cenowe dla okresów</FormField.Label>
                  {boatVariantsArray.map((field, index) => (
                    <div
                      key={field.id}
                      className="flex gap-4 items-end flex-wrap relative border rounded-md p-4"
                    >
                      <div className="flex-1">
                        <FormField.Label>Data od</FormField.Label>
                        <DedicatedDatePicker
                          value={safeParseStringToDate(
                            form.watch(
                              `pricing.boat_variants.date_ranges.${index}.start_date`
                            ) || ""
                          )}
                          setValue={(date) =>
                            form.setValue(
                              `pricing.boat_variants.date_ranges.${index}.start_date`,
                              safeParseDateToString(date) || ""
                            )
                          }
                        />
                      </div>
                      <div className="flex-1">
                        <FormField.Label>Data do</FormField.Label>
                        <DedicatedDatePicker
                          value={safeParseStringToDate(
                            form.watch(
                              `pricing.boat_variants.date_ranges.${index}.end_date`
                            ) || ""
                          )}
                          setValue={(date) =>
                            form.setValue(
                              `pricing.boat_variants.date_ranges.${index}.end_date`,
                              safeParseDateToString(date) || ""
                            )
                          }
                        />
                      </div>
                      <div className="flex-1">
                        <FormField.Label>Cena (EUR)</FormField.Label>
                        <Input
                          type="number"
                          {...form.register(
                            `pricing.boat_variants.date_ranges.${index}.price`,
                            {
                              valueAsNumber: true,
                            }
                          )}
                        />
                      </div>
                      <DeleteWithConfirmation
                        onDelete={() => removeBoatVariant(index)}
                      >
                        <Button
                          variant="danger"
                          size="small"
                          className="absolute -top-2 -right-2 size-8 shadow-md hover:shadow-lg"
                        >
                          <Trash className="size-4" />
                        </Button>
                      </DeleteWithConfirmation>
                    </div>
                  ))}
                  <Button
                    type="button"
                    className={`
                      transition-all
                      ${
                        boatVariantsArray.length > 0
                          ? "w-auto ml-auto"
                          : "w-full h-8"
                      }`}
                    onClick={() =>
                      appendBoatVariant({
                        start_date: "",
                        end_date: "",
                        price:
                          form.watch("pricing.boat_variants.base_price") || 0,
                      })
                    }
                  >
                    <Plus className="w-4 h-4" />
                    Dodaj wariant cenowy
                  </Button>
                </div>
              </div>
            </div>
          </>
        );
    }
  };

  return (
    <div className="my-8 w-full flex flex-col gap-8">
      <FormField
        control={form.control}
        name="pricing.prepaid_percentage"
        className="max-w-sm"
      >
        <FormField.Label>Wysokość zaliczki (%)</FormField.Label>

        <SimpleEuroInput
          percentage
          name={`pricing.prepaid_percentage`}
          register={form.register}
          form={form}
        />
      </FormField>

      <Select
        value={formValues.product_pricing_variant}
        onValueChange={(key) =>
          form.setValue("product_pricing_variant", key as any)
        }
      >
        <Select.Trigger>
          <Select.Value placeholder="Wybierz typ produktu" />
        </Select.Trigger>
        <Select.Content>
          {Object.entries(productTypesMap).map(([key, value]) => (
            <Select.Item key={key} value={key}>
              {value}
            </Select.Item>
          ))}
        </Select.Content>
      </Select>
      {renderConditionalFields()}
    </div>
  );
};

export default PriceVariants;
