import FormField from "../guides/form-field";
import { Input, Switch, Button } from "@medusajs/ui";
import { Controller, UseFormReturn, useWatch } from "react-hook-form";
import { TCreateTourFormBody } from "./schemas";
import RelatedToursInput from "./related-tours-input";
import CategoryInput from "./category-input";
import CitiesInput from "./cities-input";
import { slugify } from "../../lib/util/slugify";
import TimeIntervalInput from "./time-interval-input";
import ExcludedStartPlacesInput from "./start-places-input";
import BlockedDatesInput from "./blocked-dates-input";
import BlockedDatesByMonthInput from "./blocked-dates-by-month-input";
import RichTextEditor from "../rich-text-editor";
import BlockedStartTimesInput from "./blocked-start-times-input";

type Props = {
  form: UseFormReturn<TCreateTourFormBody>;
  formValues: ReturnType<typeof useWatch<TCreateTourFormBody>>;
};

export const foodOptionsArray = [
  "wege",
  "ryba",
  "mięso",
  "kurczak",
  "ryba+kurczak",
  "wieprzowina",
  "Plejskavica",
  "Szczegółowe lunche dla dzieci",
] as const;

type FoodOption = (typeof foodOptionsArray)[number];

interface FoodOptionType {
  name: FoodOption;
  description: string;
  price: string;
  required: boolean;
}

const TourBaseDetails = ({ form, formValues }: Props) => {
  const handleFoodOptionsChange = (value: FoodOption) => {
    const currentOptions = (formValues.food_options || []) as FoodOptionType[];
    const newOptions = currentOptions.find((opt) => opt.name === value)
      ? currentOptions.filter((opt) => opt.name !== value)
      : [
          ...currentOptions,
          {
            name: value,
            description: "Standardowa opcja",
            price: "0 PLN",
            required: false,
          },
        ];
    form.setValue("food_options", newOptions as any);
  };

  const generateSlugFromName = () => {
    if (!formValues.name) {
      form.setError("slug", {
        message: "W celu wygenerowania sluga, podaj najpirw tytuł atrakcji",
      });
      return;
    }

    const slug = slugify(formValues.name);
    form.setValue("slug", slug);
  };

  return (
    <>
      <FormField control={form.control} name="is_active" className="-mb-2">
        <div className="flex items-center gap-2">
          <FormField.Label>Atrakcja aktywna</FormField.Label>
          <Switch
            className="mb-2"
            checked={formValues.is_active}
            onCheckedChange={(changed) => form.setValue("is_active", changed)}
          />
        </div>
      </FormField>

      <FormField control={form.control} name="name">
        <FormField.Label>Tytuł</FormField.Label>
        <Input {...form.register("name")} />
      </FormField>

      <FormField control={form.control} name="slug">
        <FormField.Label>Slug</FormField.Label>
        <div className="flex gap-2">
          <Button onClick={generateSlugFromName}>Wygeneruj slug</Button>
          <div className="grow">
            <Input {...form.register("slug")} />
          </div>
        </div>
      </FormField>

      <div>
        <FormField.Label required>Miasta</FormField.Label>
        <CitiesInput
          value={formValues.cities || []}
          onChange={(value) =>
            form.setValue("cities", value, { shouldValidate: true })
          }
        />
        <p className="text-red-500 text-sm">
          {form.formState.errors.cities?.message}
        </p>
      </div>

      <FormField control={form.control} name="description">
        <FormField.Label>Krótki opis atrakcji</FormField.Label>

        <Controller
          control={form.control}
          name="description"
          render={({ field }) => (
            <RichTextEditor
              content={field.value}
              onChange={(value) => field.onChange(value)}
            />
          )}
        />
      </FormField>

      <FormField control={form.control} name="organizator_mail">
        <FormField.Label>Email organizatora</FormField.Label>
        <Input type="email" {...form.register("organizator_mail")} />
      </FormField>

      <TimeIntervalInput
        name="start_times"
        control={form.control}
        register={form.register}
        label="Możliwe godziny startu"
      />

      <ExcludedStartPlacesInput form={form} formValues={formValues} />

      <BlockedStartTimesInput form={form} />

      <BlockedDatesByMonthInput form={form} formValues={formValues} />

      <BlockedDatesInput form={form} formValues={formValues} />

      <FormField control={form.control} name="category_ids">
        <FormField.Label>Kategoria</FormField.Label>
        <CategoryInput
          value={formValues.category_ids || []}
          onChange={(value) => form.setValue("category_ids", value)}
        />
      </FormField>

      <div>
        <FormField.Label>Atrakcje powiązane (rekomendowane)</FormField.Label>
        <RelatedToursInput
          value={formValues.recommended_tour_ids || []}
          onChange={(value) => form.setValue("recommended_tour_ids", value)}
        />
      </div>

      <FormField control={form.control} name="food_options" className="mb-8">
        <FormField.Label>Opcje żywieniowe</FormField.Label>

        <div className="flex items-center gap-2">
          <FormField.Label className="text-sm">
            Wymuś wybranie co najmniej jednej opcji żywieniowej
          </FormField.Label>
          <Switch
            className="mb-2"
            checked={formValues.at_least_one_food_option_required}
            onCheckedChange={(changed) =>
              form.setValue("at_least_one_food_option_required", changed)
            }
          />
        </div>
        <FormField.Note>
          Zaznacz opcje, które użytkownik będzie mógł wybrać podczas rezerwacji.
        </FormField.Note>

        {(form.watch("food_options") || []).length === 0 &&
          formValues.at_least_one_food_option_required && (
            <FormField.Note className="text-red-500 text-sm">
              Musisz wybrać co najmniej jedną opcję żywieniową jeżeli wymuszasz
              wybranie opcji żywieniowej
            </FormField.Note>
          )}

        <div className="grid grid-cols-2 gap-2 mt-2 gap-x-8">
          {foodOptionsArray.map((option) => {
            const isSelected = (
              (formValues.food_options || []) as FoodOptionType[]
            ).some((opt) => opt.name === option);

            return (
              <div key={option} className="grid gap-3">
                <Button
                  variant={isSelected ? "primary" : "secondary"}
                  onClick={() => handleFoodOptionsChange(option)}
                  type="button"
                  className="w-full justify-start"
                >
                  {option}
                </Button>
              </div>
            );
          })}
        </div>
      </FormField>
    </>
  );
};

export default TourBaseDetails;
