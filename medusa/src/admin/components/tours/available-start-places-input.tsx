import FormField from "../guides/form-field";
import { TCreateTourFormBody } from "./schemas";
import { Controller, useFieldArray, UseFormReturn } from "react-hook-form";
import { Button } from "@medusajs/ui";
import { Input } from "@medusajs/ui";
import DeleteWithConfirmation from "../reusable/delete-with-confirmation";
import { Plus } from "@medusajs/icons";
import RichTextEditor from "../rich-text-editor";
import FormGrid from "../reusable/form-grid";

type Props = {
  form: UseFormReturn<TCreateTourFormBody>;
};

const AvailableStartPlacesInput = ({ form }: Props) => {
  const { fields, append, remove } = useFieldArray<TCreateTourFormBody>({
    control: form.control,
    name: "available_start_places",
  });

  return (
    <>
      <FormField.Label>Dostępne miejsca startu</FormField.Label>
      {fields.map((field, index) => (
        <div
          key={field.id}
          className="grid  gap-4 items-end bg-ui-bg-base p-4 rounded-lg border"
        >
          <FormGrid gridCols={2}>
            <div>
              <FormField.Label className="text-sm" required>
                Miejsce startu
              </FormField.Label>
              <Input
                {...form.register(`available_start_places.${index}.place`)}
              />
            </div>
            <div>
              <FormField.Label className="text-sm" required>
                Pozycja
              </FormField.Label>
              <Input
                {...form.register(`available_start_places.${index}.position`, {
                  valueAsNumber: true,
                })}
              />
            </div>
          </FormGrid>

          <div>
            <FormField.Label className="text-sm">Opis</FormField.Label>
            <Controller
              control={form.control}
              name={`available_start_places.${index}.rich_text_content`}
              render={({ field }) => (
                <RichTextEditor
                  content={field.value || ""}
                  onChange={(content) => field.onChange(content)}
                />
              )}
            />
          </div>

          <DeleteWithConfirmation
            onDelete={() => remove(index)}
            triggerClassName="relative ml-auto mb-2"
          />
        </div>
      ))}
      <Button
        variant="primary"
        onClick={() =>
          append({
            position:
              fields.length > 0
                ? Math.max(...fields.map((field) => field.position)) + 1
                : 1,
          })
        }
        className={`self-end transition-[width] ease-out mt-4 ${
          fields.length > 0 ? "auto-24" : "w-full h-8"
        }`}
      >
        <Plus className="w-4 h-4" />
        Dodaj miejsce startu
      </Button>
    </>
  );
};

export default AvailableStartPlacesInput;
