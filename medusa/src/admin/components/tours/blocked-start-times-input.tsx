import FormField from "../guides/form-field";
import { TCreateTourFormBody } from "./schemas";
import { Controller, useFieldArray, UseFormReturn } from "react-hook-form";
import { Button, Select } from "@medusajs/ui";
import { Input } from "@medusajs/ui";
import DeleteWithConfirmation from "../reusable/delete-with-confirmation";
import { Plus, Trash } from "@medusajs/icons";
import FormGrid from "../reusable/form-grid";

type Props = {
  form: UseFormReturn<TCreateTourFormBody>;
};

const MONTHS = [
  { value: 1, label: "Styczeń" },
  { value: 2, label: "<PERSON><PERSON>" },
  { value: 3, label: "<PERSON><PERSON><PERSON>" },
  { value: 4, label: "Kwiecie<PERSON>" },
  { value: 5, label: "Maj" },
  { value: 6, label: "Czerwiec" },
  { value: 7, label: "<PERSON>piec" },
  { value: 8, label: "<PERSON><PERSON><PERSON><PERSON>" },
  { value: 9, label: "<PERSON><PERSON><PERSON><PERSON><PERSON>" },
  { value: 10, label: "<PERSON>ź<PERSON><PERSON><PERSON>" },
  { value: 11, label: "Listopad" },
  { value: 12, label: "<PERSON>rudzień" },
];

type BlockedStartTimeType = {
  month: number;
  days: string | null;
  blocked_times: string[];
};

const BlockedStartTimesInput = ({ form }: Props) => {
  const { fields, append, remove } = useFieldArray<TCreateTourFormBody>({
    control: form.control,
    name: "blocked_start_times",
  });

  return (
    <div className="mb-4 p-4 bg-ui-bg-component rounded-lg border">
      <FormField.Label>
        Zablokowane godziny startu w określone dni
      </FormField.Label>
      {fields.map((field, index) => (
        <div
          key={field.id}
          className="grid gap-4 items-end mb-2 bg-ui-bg-base p-4 rounded-lg border"
        >
          <FormGrid gridCols={2}>
            <div>
              <FormField.Label className="text-sm" required>
                Miesiąc
              </FormField.Label>
              <Controller
                control={form.control}
                name={`blocked_start_times.${index}.month`}
                render={({ field }) => (
                  <Select
                    value={field.value?.toString() || "1"}
                    onValueChange={(value) => {
                      field.onChange(Number(value));
                    }}
                  >
                    <Select.Trigger>
                      <Select.Value placeholder="Wybierz miesiąc" />
                    </Select.Trigger>
                    <Select.Content>
                      {MONTHS.map((month) => (
                        <Select.Item
                          key={month.value}
                          value={month.value.toString()}
                        >
                          {month.label}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                )}
              />
            </div>
            <div>
              <FormField.Label className="text-sm">
                Dni (np. 1,2,3,4,5)
              </FormField.Label>
              <Input
                placeholder="np. 1,2,3,4,5"
                {...form.register(`blocked_start_times.${index}.days`)}
              />
            </div>
          </FormGrid>

          <div>
            <FormField.Label className="text-sm" required>
              Zablokowane godziny
            </FormField.Label>
            <FormField.Note>
              Dodaj godziny, które mają być zablokowane w wybranych dniach.
            </FormField.Note>

            <Controller
              control={form.control}
              name={`blocked_start_times.${index}.blocked_times`}
              render={({ field }) => (
                <>
                  <div className="flex items-center gap-4">
                    {field.value && field.value.length > 0 ? (
                      field.value.map((time, timeIndex) => (
                        <div
                          key={timeIndex}
                          className="flex items-center gap-2"
                        >
                          <Input
                            type="time"
                            value={time}
                            onChange={(e) => {
                              const newTimes = [...field.value];
                              newTimes[timeIndex] = e.target.value;
                              field.onChange(newTimes);
                            }}
                            className="flex-1"
                          />

                          <DeleteWithConfirmation
                            onDelete={() => {
                              const newTimes = [...field.value];
                              newTimes.splice(timeIndex, 1);
                              field.onChange(newTimes);
                            }}
                          >
                            <Button
                              variant="danger"
                              size="small"
                              className="p-2 h-8 w-8"
                            >
                              <Trash className="w-4 h-4" />
                            </Button>
                          </DeleteWithConfirmation>
                        </div>
                      ))
                    ) : (
                      <div className="text-sm text-ui-fg-subtle">
                        Brak zablokowanych godzin. Kliknij przycisk poniżej, aby
                        dodać.
                      </div>
                    )}
                  </div>
                  <Button
                    variant="secondary"
                    size="small"
                    className="mt-4"
                    onClick={() => {
                      const newTimes = [...(field.value || []), ""];
                      field.onChange(newTimes);
                    }}
                  >
                    <Plus className="size-4" />
                    Dodaj godzinę
                  </Button>
                </>
              )}
            />
          </div>

          <DeleteWithConfirmation
            onDelete={() => remove(index)}
            triggerClassName="relative ml-auto mb-2"
          />
        </div>
      ))}
      <Button
        variant="primary"
        onClick={() => {
          const newItem = {
            month: 1,
            days: null,
            blocked_times: [],
          };
          append(newItem as any);
        }}
        className={`self-end transition-[width] ease-out mt-4 ${
          fields.length > 0 ? "auto-24" : "w-full h-8"
        }`}
      >
        <Plus className="w-4 h-4" />
        Dodaj zablokowane godziny
      </Button>
    </div>
  );
};

export default BlockedStartTimesInput;
