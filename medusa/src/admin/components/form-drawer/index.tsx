import { Button } from "@medusajs/ui";
import { Drawer } from "@medusajs/ui";
import React, { ComponentProps } from "react";

type Props = {
  children: React.ReactNode;
  title: string;
  triggerText: string;
  withoutTrigger?: boolean;
  onSubmit: (data: any) => void;
} & ComponentProps<typeof Drawer>;

const FormDrawer = (props: Props) => {
  const { children, ...rest } = props;

  const onSubmit = (data: any) => {
    props.onSubmit(data);
    props.onOpenChange?.(false);
  };

  return (
    <Drawer {...rest}>
      {!props.withoutTrigger && (
        <Drawer.Trigger asChild>
          <Button>{props.triggerText}</Button>
        </Drawer.Trigger>
      )}
      <Drawer.Content>
        <Drawer.Header>
          <Drawer.Title>{props.title}</Drawer.Title>
        </Drawer.Header>
        <Drawer.Body className="p-4">{children}</Drawer.Body>
        <Drawer.Footer>
          <Drawer.Close asChild>
            <Button variant="secondary">Anuluj</Button>
          </Drawer.Close>
          <Button onClick={onSubmit}>Zapisz</Button>
        </Drawer.Footer>
      </Drawer.Content>
    </Drawer>
  );
};

export default FormDrawer;
