import { DropdownMenu, IconButton, Prompt, Table, toast } from "@medusajs/ui";
import { useState } from "react";
import { TCity } from "../../types";
import { EllipsisVertical } from "@medusajs/icons";
import { useMutation } from "@tanstack/react-query";
import { deleteCity } from "../../lib/api/cities";
import CreatedAtBadge from "../reusable/created-at-badge";

type Props = {
  city: TCity;
  refetch: () => void;
};

const CityTableRow = ({ city, refetch }: Props) => {
  const [openPrompt, setOpenPrompt] = useState(false);
  const { isPending, mutate } = useMutation({
    mutationFn: deleteCity,
    onSuccess: () => {
      toast.success(`Miasto - ${city.name} zostało usunięte`);
      refetch();
    },
  });

  return (
    <>
      <Table.Row key={city.id}>
        <Table.Cell className="w-2/3">{city.name}</Table.Cell>
        <Table.Cell>
          <CreatedAtBadge createdAt={city.created_at} />
        </Table.Cell>
        <Table.Cell>
          <DropdownMenu>
            <DropdownMenu.Trigger>
              <IconButton>
                <EllipsisVertical />
              </IconButton>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content>
              <DropdownMenu.Item asChild>
                <a href={`/app/miasta/${city.id}`}>Edytuj</a>
              </DropdownMenu.Item>
              <DropdownMenu.Item
                disabled={isPending}
                onClick={() => {
                  setOpenPrompt(true);
                }}
                className="text-red-600"
              >
                Usuń
              </DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu>
        </Table.Cell>
      </Table.Row>

      <Prompt open={openPrompt} onOpenChange={setOpenPrompt}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Czy na pewno chcesz usunąć to miasto?</Prompt.Title>
            <Prompt.Description>
              Po usunięciu miasta nie będzie możliwości jego przywrócenia.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel>Anuluj</Prompt.Cancel>
            <Prompt.Action onClick={() => mutate(city.id)}>Usuń</Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>
    </>
  );
};

export default CityTableRow;
