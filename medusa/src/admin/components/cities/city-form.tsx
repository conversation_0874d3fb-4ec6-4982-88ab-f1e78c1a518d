import { useMutation } from "@tanstack/react-query";
import {
  <PERSON><PERSON>,
  Container,
  FocusModal,
  Input,
  toast,
  Toaster,
} from "@medusajs/ui";
import { FieldErrors, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Spinner } from "@medusajs/icons";
import FormField from "../guides/form-field";
import {
  TCreateCityFormBody,
  TCreateExtendedCategoryFormBody,
  toursSchemas,
} from "../tours/schemas";
import { createCity, updateCity } from "../../lib/api/cities";

type Props = {
  initialValues?: TCreateCityFormBody;
  editMode?: boolean;
  idToEdit?: string;
  onSuccessfulSubmit?: () => void;
};

const CityForm = ({
  initialValues,
  onSuccessfulSubmit,
  editMode,
  idToEdit,
}: Props) => {
  const defaultValues: TCreateCityFormBody = initialValues ?? {
    name: "",
  };

  const form = useForm<typeof defaultValues>({
    defaultValues: defaultValues,
    resolver: zodResolver(toursSchemas.createCityFormBody),
  });

  const { mutate: triggerCityCreationSubmit, isPending } = useMutation({
    mutationFn: async (data: TCreateCityFormBody) => {
      if (editMode) {
        return await updateCity(idToEdit!, data);
      }

      return await createCity(data);
    },
    onSuccess: () => {
      onSuccessfulSubmit?.();

      if (editMode) {
        return toast.success("Miasto zaktualizowane");
      } else {
        toast.success("Miasto utworzone");
        form.reset(defaultValues);
      }
    },
    onError: (error: unknown) => {
      if (editMode) {
        return toast.error(
          `Wystąpił błąd podczas aktualizacji miasta - ${JSON.stringify(error)}`
        );
      } else {
        toast.error(
          `Wystąpił błąd podczas tworzenia miasta - ${JSON.stringify(error)}`
        );
      }
      console.error(error);
    },
  });

  const onSubmit = (data: TCreateCityFormBody) => {
    triggerCityCreationSubmit(data);
  };

  const onError = (error: FieldErrors<TCreateExtendedCategoryFormBody>) => {
    toast.error(`Wystąpił błąd podczas tworzenia miasta - ${error}`);
    console.error(error);
  };

  return (
    <>
      <Toaster />

      <Container
        className={`flex flex-col gap-4 rounded-none max-w-screen-lg mx-auto ${
          editMode ? "rounded-md" : ""
        }`}
      >
        <FormField control={form.control} name="name">
          <FormField.Label>Nazwa miasta</FormField.Label>
          <Input {...form.register("name")} />
        </FormField>

        <div className="h-[80vh]" />
      </Container>

      {editMode ? (
        <div className="flex p-4 sticky bottom-0 bg-ui-bg-base border w-full justify-end  rounded-md ">
          <Button
            className="self-end"
            onClick={form.handleSubmit(onSubmit, onError)}
          >
            {isPending ? (
              <Spinner className="animate-spin" />
            ) : (
              "Aktualizuj miasto"
            )}
          </Button>
        </div>
      ) : (
        <FocusModal.Footer className="sticky bottom-0 bg-ui-bg-base">
          <Button
            className="self-end"
            onClick={form.handleSubmit(onSubmit, onError)}
          >
            {isPending ? (
              <Spinner className="animate-spin" />
            ) : (
              "Stwórz nowe miasto"
            )}
          </Button>
        </FocusModal.Footer>
      )}
    </>
  );
};

export default CityForm;
