export const safeParseStringToDate = (date: string | undefined | null) => {
  if (!date) {
    return null;
  }

  const dateObj = new Date(date);
  return isNaN(dateObj.getTime()) ? null : dateObj;
};

export const safeParseDateToString = (date: Date | undefined | null) => {
  if (!date) {
    return null;
  }

  return date.toISOString();
};

export const formatDate = (
  dateString: string | Date,
  withTime = false
): string => {
  try {
    const date =
      typeof dateString === "string" ? new Date(dateString) : dateString;

    if (isNaN(date.getTime())) {
      console.warn(`Invalid dateString provided to formatDate: ${dateString}`);
      return "Invalid Date";
    }

    const options: Intl.DateTimeFormatOptions = {
      timeZone: "Europe/Warsaw", // Target time zone
      year: "numeric",
      month: "numeric", // Use "long" for "lipca" if needed
      day: "numeric",
      ...(withTime && {
        hour: "2-digit",
        minute: "2-digit",
      }),
    };

    // *** USE toLocaleString instead ***
    return date.toLocaleString("pl-PL", options);
  } catch (error) {
    console.error(`Error formatting date: ${dateString}`, error);
    return "Error Formatting Date";
  }
};

export const ISODateStringToPolishDateString = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("pl-PL", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};
