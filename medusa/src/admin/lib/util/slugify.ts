export const slugify = (text: string) => {
  return (
    text
      .toLowerCase()
      // Replace special characters with hyphens
      .replace(/[^a-zA-Z0-9\s]/g, "-")
      // Replace whitespace with hyphens
      .replace(/\s+/g, "-")
      // Replace multiple consecutive hyphens with a single hyphen
      .replace(/-+/g, "-")
      // Remove hyphens from start and end
      .replace(/^-+|-+$/g, "")
  );
};
