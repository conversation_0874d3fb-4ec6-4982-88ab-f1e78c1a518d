/**
 * Fuzzy search utility with Polish and Croatian character normalization
 */

// Function to normalize text by removing diacritics (Polish and Croatian characters)
export const normalizeText = (text: string): string => {
  return (
    text
      .toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "") // Remove combining diacritical marks
      // Additional replacements for specific Polish and Croatian characters
      .replace(/ą/g, "a")
      .replace(/ć/g, "c")
      .replace(/ę/g, "e")
      .replace(/ł/g, "l")
      .replace(/ń/g, "n")
      .replace(/ó/g, "o")
      .replace(/ś/g, "s")
      .replace(/ź/g, "z")
      .replace(/ż/g, "z")
      .replace(/č/g, "c")
      .replace(/đ/g, "d")
      .replace(/š/g, "s")
      .replace(/ž/g, "z")
  );
};

// Enhanced fuzzy search function
export const createFuzzyMatcher = (searchQuery: string) => {
  if (!searchQuery || searchQuery.trim() === "") {
    return () => true;
  }

  const normalizedQuery = normalizeText(searchQuery.trim());
  const queryWords = normalizedQuery.split(/\s+/).filter(word => word.length > 0);

  return (text: string) => {
    if (!text) return false;
    
    const normalizedText = normalizeText(text);
    
    // Check if all query words have a match in the text
    return queryWords.every(queryWord => 
      normalizedText.includes(queryWord)
    );
  };
};

// Simple fuzzy search for single field
export const fuzzySearch = <T>(
  items: T[], 
  searchQuery: string, 
  getSearchText: (item: T) => string
): T[] => {
  const matcher = createFuzzyMatcher(searchQuery);
  return items.filter(item => matcher(getSearchText(item)));
};

// Multi-field fuzzy search
export const fuzzySearchMultiField = <T>(
  items: T[], 
  searchQuery: string, 
  getSearchTexts: (item: T) => string[]
): T[] => {
  const matcher = createFuzzyMatcher(searchQuery);
  return items.filter(item => 
    getSearchTexts(item).some(text => matcher(text))
  );
};