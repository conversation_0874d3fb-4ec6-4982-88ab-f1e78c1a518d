import { sdk } from "../config";
import { CustomStoreSettingsSchema } from "../../widgets/calendar-time-handicap/schema";
import { ShopEnabledSettingsSchema } from "../../widgets/shop-enabled/schema";
import { LatestBookingHourSettingsSchema } from "../../widgets/latest-booking-hour/schema";

export const getStoreGlobalAmountOfHoursCalendarHandicap = async (): Promise<
  number | null
> => {
  const { stores } = await sdk.admin.store.list();

  const currentStore = stores[0];

  return currentStore?.metadata?.global_amount_of_hours_calendar_handicap;
};

export const updateStoreGlobalAmountOfHoursCalendarHandicap = async (
  data: CustomStoreSettingsSchema
) => {
  const { stores } = await sdk.admin.store.list();

  const currentStore = stores[0];

  const response = await sdk.admin.store.update(currentStore.id, {
    metadata: {
      global_amount_of_hours_calendar_handicap:
        data.global_amount_of_hours_calendar_handicap,
    },
  });

  return response;
};

export const getStoreShopEnabled = async (): Promise<boolean | null> => {
  const { stores } = await sdk.admin.store.list();

  const currentStore = stores[0];

  return currentStore?.metadata?.is_shop_enabled ?? true;
};

export const updateStoreShopEnabled = async (
  data: ShopEnabledSettingsSchema
) => {
  const { stores } = await sdk.admin.store.list();

  const currentStore = stores[0];

  const response = await sdk.admin.store.update(currentStore.id, {
    metadata: {
      ...currentStore.metadata,
      is_shop_enabled: data.is_shop_enabled,
    },
  });

  return response;
};

export const getStoreLatestBookingHour = async (): Promise<{hour: number, minute: number}> => {
  const { stores } = await sdk.admin.store.list();

  const currentStore = stores[0];

  return {
    hour: currentStore?.metadata?.latest_booking_hour ?? 19,
    minute: currentStore?.metadata?.latest_booking_minute ?? 0,
  };
};

export const updateStoreLatestBookingHour = async (
  data: LatestBookingHourSettingsSchema
) => {
  const { stores } = await sdk.admin.store.list();

  const currentStore = stores[0];

  const response = await sdk.admin.store.update(currentStore.id, {
    metadata: {
      ...currentStore.metadata,
      latest_booking_hour: data.latest_booking_hour,
      latest_booking_minute: data.latest_booking_minute,
    },
  });

  return response;
};
