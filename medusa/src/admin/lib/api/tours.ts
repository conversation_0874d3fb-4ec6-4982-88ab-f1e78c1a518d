import { AdminCreateProduct } from "@medusajs/types";
import { TTour, TTourExtendedByProduct } from "../../types";
import { sdk } from "../config";
import {
  TCreateTourFormBody,
  TUpdateTourPayload,
} from "../../components/tours/schemas";

export const getAllTours = async () =>
  sdk.client.fetch<{ tours: TTourExtendedByProduct[] }>("/admin/tours");

export const getAllToursForRecommendation = async () =>
  sdk.client.fetch<{
    tours: Array<Pick<TTour, "id" | "name" | "featured_image">>;
  }>("/admin/tours/recommendation");

export const createTour = async (
  data: AdminCreateProduct & { additional_data: TCreateTourFormBody }
) => {
  const response = await sdk.admin.product.create({
    ...data,
  });

  return {
    ...response,
    tour_slug: data.additional_data.slug,
  };
};

export const updateTour = async (
  data: AdminCreateProduct & { additional_data: TUpdateTourPayload },
  id: TTour["id"]
) => {
  const response = await sdk.admin.product.update(id, data);

  return response;
};

export const deleteTour = async (id: TTour["id"]) =>
  sdk.admin.product.delete(id);

export const getTourDetails = async (id: TTour["id"]) =>
  sdk.client.fetch<{ tour: TTourExtendedByProduct }>(`/admin/tours/${id}`);

export const getTourDetailsForDuplication = async (id: TTour["id"]) =>
  sdk.client.fetch<{ tour: TTourExtendedByProduct }>(
    `/admin/tours/tour-for-duplication/${id}`
  );
