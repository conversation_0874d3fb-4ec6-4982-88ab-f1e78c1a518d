import { TicketDTO } from "../../../modules/ticket/types";
import { sdk } from "../config";

export const getTicketDetails = async (
  orderId: string
): Promise<{ data: { ticket: TicketDTO }[] }> => {
  return sdk.client.fetch(`/admin/order/${orderId}/ticket`);
};

export const generateTicketPDF = async (
  orderId: string,
  withPathSave: boolean = true
): Promise<{
  ticketBuffer: Uint8Array<ArrayBufferLike> & Uint8Array<ArrayBufferLike>;
}> => {
  return sdk.client.fetch(`/admin/generate-ticket-pdf/${orderId}`, {
    method: "POST",
    body: {
      withPathSave,
    },
  });
};
