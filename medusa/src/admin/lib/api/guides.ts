import { AdminCreateProduct, ProductDTO } from "@medusajs/types";
import { TCreateGuidePayload } from "../../components/guides/schemas";
import { TGuidePost } from "../../types";
import { sdk } from "../config";

export const getAllGuides = () => sdk.client.fetch<TGuidePost[]>("/all-guides");

export const getGuideDetails = (id: TGuidePost["id"]) =>
  sdk.client.fetch<TGuidePost>(`/guides/${id}`);

export const createGuide = (data: TCreateGuidePayload) =>
  sdk.client.fetch<TGuidePost>("/guides", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: data,
  });

export const updateGuide = (id: TGuidePost["id"], data: TCreateGuidePayload) =>
  sdk.client.fetch<TGuidePost>(`/guides/${id}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: data,
  });

export const deleteGuide = (id: TGuidePost["id"]) =>
  sdk.client.fetch<TGuidePost>(`/guides/${id}`, {
    method: "DELETE",
  });

export const createProduct = (
  data: AdminCreateProduct & { prepaid_percentage?: number }
) => sdk.admin.product.create(data);

export const getTours = () =>
  sdk.client.fetch("/admin/tours", {
    method: "GET",
  });
