import { sdk } from "../config";
import { TicketDTO } from "../../../modules/ticket/types";
import { OrderStatus, OrderStatusDTO } from "../../widgets/order-status/types";
import { IssueRefundDTO } from "../../../types";
import { getOrdersResult } from "../../types";

export type RefundRecord = {
  id: string;
  amount: number;
  currency_code: string;
  reason?: string;
  created_at: string;
  reference_id?: string;
  order_id: string;
};

export const getOrders = async (
  limit: number,
  offset: number,
  filters?: Record<string, any>,
  orderBy?: string,
  orderDirection?: "ASC" | "DESC"
): Promise<getOrdersResult> => {
  return sdk.client.fetch(`/admin/order`, {
    method: "GET",
    query: {
      limit,
      offset,
      filters: filters ? JSON.stringify(filters) : undefined,
      orderBy,
      orderDirection,
    },
  });
};

export const triggerOrderUpdate = async (
  id: string,
  payload?: Partial<TicketDTO>
) => {
  return sdk.client.fetch(`/admin/order/${id}/update-order`, {
    method: "POST",
    body: payload,
  });
};

export const triggerOrderRefund = async (
  id: string,
  payload: IssueRefundDTO
) => {
  return sdk.client.fetch(`/admin/order/${id}/issue-refund`, {
    method: "POST",
    body: payload,
  });
};

export const triggerSolelyTicketUpdate = async (
  id: string,
  payload?: Partial<TicketDTO>
) => {
  return sdk.client.fetch(`/admin/order/${id}/update-related-ticket`, {
    method: "POST",
    body: payload,
  });
};

export const triggerTicketRegenerationAndEmailNotification = async (
  id: string,
  updateReason?: string
) => {
  return sdk.client.fetch(
    `/admin/order/${id}/regenerate-ticket-and-send-email`,
    {
      method: "POST",
      body: {
        updateReason,
      },
    }
  );
};

export const triggerOrderStatusUpdate = async (
  id: string,
  payload?: OrderStatusDTO
) => {
  return sdk.client.fetch(`/admin/order/${id}/update-order-status`, {
    method: "POST",
    body: payload,
  });
};

export const getOrderCustomData = async (
  id: string
): Promise<{
  customStatus: OrderStatus;
  additionalCustomerNote?: string;
  euroRate?: number;
  prepaidPercentage?: number;
}> => {
  return sdk.client.fetch(`/admin/order/${id}/get-order-custom-data`);
};

export const softDeleteOrder = async (id: string) => {
  return sdk.client.fetch(`/admin/order/${id}/soft-delete`, {
    method: "POST",
  });
};

export const getOrderRefunds = async (
  orderId: string
): Promise<{
  refunds: RefundRecord[];
  total: number;
}> => {
  const response = await fetch(`/admin/order/${orderId}/refunds`);

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "Failed to fetch refunds");
  }

  return response.json();
};

export const revokeRefund = async (orderId: string, refundId: string) => {
  const response = await fetch(
    `/admin/order/${orderId}/refunds/${refundId}/revoke`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "Failed to revoke refund");
  }

  return response.json();
};
