import { TCreateCityFormBody } from "../../components/tours/schemas";
import { TCity } from "../../types";
import { sdk } from "../config";

export const getAllCities = () => {
  return sdk.client.fetch<TCity[]>("/admin/cities", {
    method: "GET",
  });
};

export const createCity = (data: TCreateCityFormBody) => {
  return sdk.client.fetch("/admin/cities", {
    method: "POST",
    body: data,
  });
};

export const getCityDetails = (id: string) => {
  return sdk.client.fetch<TCity>(`/admin/cities/${id}`, {
    method: "GET",
  });
};

export const updateCity = (id: string, data: TCreateCityFormBody) => {
  return sdk.client.fetch(`/admin/cities/${id}`, {
    method: "PUT",
    body: data,
  });
};

export const deleteCity = (id: string) => {
  return sdk.client.fetch(`/admin/cities/${id}`, {
    method: "DELETE",
  });
};
