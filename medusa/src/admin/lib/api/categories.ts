import { sdk } from "../config";
import { TCreateExtendedCategoryPayload } from "../../components/tours/schemas";
import { TExtendedCategory } from "../../types";

export const getExtendedCategories = async () => {
  const categories = await sdk.admin.productCategory.list();

  return categories.product_categories as TExtendedCategory[];
};

export const getExtendedCategoryDetails = async (id: string) => {
  const category = await sdk.admin.productCategory.retrieve(id);
  return category.product_category as TExtendedCategory;
};

export const createExtendedCategory = async (
  data: TCreateExtendedCategoryPayload
) => {
  const category = await sdk.admin.productCategory.create(data);
  return category;
};

export const updateExtendedCategory = async (
  id: string,
  data: TCreateExtendedCategoryPayload
) => {
  const category = await sdk.admin.productCategory.update(id, data);
  return category;
};

export const deleteExtendedCategory = async (id: string) => {
  const category = await sdk.admin.productCategory.delete(id);
  return category;
};
