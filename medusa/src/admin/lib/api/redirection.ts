import { TCreateRedirectionFormBody } from "../../components/redirection/schemas";
import { TRedirection } from "../../types";
import { sdk } from "../config";

export const getAllRedirection = () => {
  return sdk.client.fetch<TRedirection[]>("/admin/redirection", {
    method: "GET",
  });
};

export const createRedirection = (data: TCreateRedirectionFormBody) => {
  return sdk.client.fetch("/admin/redirection", {
    method: "POST",
    body: data,
  });
};

export const getRedirectionDetails = (id: string) => {
  return sdk.client.fetch<TRedirection>(`/admin/redirection/${id}`, {
    method: "GET",
  });
};

export const updateRedirection = (
  id: string,
  data: TCreateRedirectionFormBody
) => {
  return sdk.client.fetch(`/admin/redirection/${id}`, {
    method: "PUT",
    body: data,
  });
};

export const deleteRedirection = (id: string) => {
  return sdk.client.fetch(`/admin/redirection/${id}`, {
    method: "DELETE",
  });
};
