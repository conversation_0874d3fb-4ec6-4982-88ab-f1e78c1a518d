import { CustomerDTO, OrderDTO, InferTypeOf } from "@medusajs/framework/types";
import GuidePost from "../../modules/guides/models/guide-post";
import { Tour } from "../../modules/tours/models/tour";
import { BaseProductCategory } from "@medusajs/types/dist/http/product-category/common";
import { City } from "../../modules/tours/models/city";
import ExchangeRate from "../../modules/exchange-rate/models/exchange-rate";
import {
  BaseProduct,
  BaseProductVariant,
} from "@medusajs/types/dist/http/product/common";
import { Report } from "../../modules/report/models/report";
import { OrderStatus } from "../widgets/order-status/types";
import { Redirection } from "../../modules/redirections/models/redirection";

export type TGuidePost = InferTypeOf<typeof GuidePost>;

export type TTour = InferTypeOf<typeof Tour>;

export type TExchangeRate = InferTypeOf<typeof ExchangeRate>;

export type TTourExtendedByProduct = TTour & {
  product: TProductWithVariantWithPrices;
};

export type TProductWithVariantWithPrices = Omit<BaseProduct, "variants"> & {
  variants: (BaseProductVariant & { prices: { amount: number }[] })[];
};

export type TCustomCategoryFields = {
  slug: string;
  image: string;
};

export type TExtendedCategory = BaseProductCategory & {
  metadata: TCustomCategoryFields;
};

export type TCity = InferTypeOf<typeof City>;

export type TReport = InferTypeOf<typeof Report>;

export type OrderWithRelations = OrderDTO & {
  customer: CustomerDTO;
  metadata: {
    euro_rate: number;
    status: OrderStatus;
  };
  summary: {
    transaction_total: number;
  };
  ticket: {
    attraction_name: string;
    selected_date: string;
  };
  calculated_pln_amount?: number | null;
};

export type getOrdersResult = {
  orders: OrderWithRelations[];
  count: number;
  limit: number;
  offset: number;
};

export type OrderWithMetadata = OrderDTO & {
  metadata: {
    status: OrderStatus;
    euro_rate: number;
    additional_customer_note?: string;
    prepaid_percentage?: number;
  };
};

export type TRedirection = InferTypeOf<typeof Redirection>;
