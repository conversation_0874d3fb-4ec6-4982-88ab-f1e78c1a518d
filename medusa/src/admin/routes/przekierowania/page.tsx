import { defineRouteConfig } from "@medusajs/admin-sdk";
import { Arrow<PERSON>ongR<PERSON>, Spinner } from "@medusajs/icons";
import {
  Button,
  Container,
  Heading,
  FocusModal,
  Input,
  Table,
  Text,
  Label,
  Select,
  toast,
} from "@medusajs/ui";
import { useEffect, useMemo, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { QUERY_KEYS } from "../../lib/constants/query-keys";
import { getAllRedirection } from "../../lib/api/redirection";
import RedirectionTableRow from "../../components/redirection/redirection-table-row";
import RedirectionForm from "../../components/redirection/redirection-form";

const RedirectionPage = () => {
  const [modalOpen, setModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedStatusCodes, setSelectedStatusCodes] = useState("");
  const [isSuccessEdited, setIsSuccessEdited] = useState(false);

  const checkSuccessParam = () => {
    const searchParams = new URLSearchParams(window.location.search);
    const success = searchParams.get("successfully_edited") === "true";
    setIsSuccessEdited(success);
  };

  useEffect(() => {
    checkSuccessParam();

    const handleUrlChange = () => {
      checkSuccessParam();
    };

    window.addEventListener("popstate", handleUrlChange);

    return () => {
      window.removeEventListener("popstate", handleUrlChange);
    };
  }, []);

  useEffect(() => {
    if (isSuccessEdited) {
      toast.success("Przekierowanie zaktualizowane");
      // clear the url from the success param
      const url = new URL(window.location.href);
      url.searchParams.delete("successfully_edited");
      window.history.replaceState({}, "", url.toString());
    }
  }, [isSuccessEdited]);

  const { data, isFetching, refetch } = useQuery({
    queryKey: [QUERY_KEYS.ALL_REDIRECTIONS],
    queryFn: getAllRedirection,
  });

  const filteredRedirections = useMemo(() => {
    if (!data) return [];
    let filtered = data;

    if (searchQuery) {
      filtered = filtered.filter((redirection) =>
        redirection.from_path.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (selectedStatusCodes) {
      filtered = filtered.filter((redirection) =>
        selectedStatusCodes.includes(redirection.status_code?.toString())
      );
    }

    return filtered;
  }, [data, searchQuery, selectedStatusCodes]);

  const uniqueStatusCodes = useMemo(() => {
    if (!data) return [];
    return Array.from(
      new Set(
        data?.map((redirection) => redirection.status_code?.toString()) || []
      )
    );
  }, [data]);

  return (
    <Container className="p-0 flex flex-col overflow-hidden">
      <div className="px-4 py-6 flex items-center justify-between">
        <Heading level="h2">Przekierowania</Heading>
        {isFetching && (
          <div className="flex gap-2 items-center ml-auto mr-2">
            <div className="size-2 rounded-full bg-orange-400" />
            <Text>Synchronizowanie listy</Text>
            <Spinner className="animate-spin mx-1 size-4" />
          </div>
        )}

        <FocusModal open={modalOpen} onOpenChange={setModalOpen}>
          <FocusModal.Trigger asChild>
            <Button>Dodaj nowe przekierowanie</Button>
          </FocusModal.Trigger>
          <FocusModal.Content>
            <FocusModal.Header>
              <FocusModal.Title>Dodaj nowe przekierowanie</FocusModal.Title>
            </FocusModal.Header>
            <FocusModal.Body>
              <RedirectionForm
                onSuccessfulSubmit={() => {
                  refetch();
                  setModalOpen(false);
                }}
              />
            </FocusModal.Body>
          </FocusModal.Content>
        </FocusModal>
      </div>

      <div className="px-4 mb-4 flex items-center gap-4 flex-wrap">
        <Input
          type="search"
          placeholder="Szukaj przekierowań po 'z adresu'..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="lg:min-w-[300px]"
        />
        <Label>Kod Statusu</Label>
        <Select
          onValueChange={setSelectedStatusCodes}
          value={selectedStatusCodes}
        >
          <Select.Trigger className="max-w-fit lg:min-w-[175px]">
            <Select.Value placeholder="Wybierz kod statusu" />
          </Select.Trigger>
          <Select.Content>
            {uniqueStatusCodes?.map((code) => (
              <Select.Item key={code} value={code}>
                {code}
              </Select.Item>
            ))}
          </Select.Content>
        </Select>
      </div>

      <div className="overflow-x-auto w-full">
        <Table className="w-full">
          <Table.Header>
            <Table.Row>
              <Table.HeaderCell>Z adresu</Table.HeaderCell>
              <Table.HeaderCell>Na adres</Table.HeaderCell>
              <Table.HeaderCell>Kod przekierowania</Table.HeaderCell>
              <Table.HeaderCell>Stworzono</Table.HeaderCell>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {filteredRedirections.map((redirection) => (
              <RedirectionTableRow
                key={redirection.id}
                redirection={redirection}
                refetch={refetch}
              />
            ))}
          </Table.Body>
        </Table>
      </div>
    </Container>
  );
};

export const config = defineRouteConfig({
  label: "Przekierowania 301",
  icon: ArrowLongRight,
});

export default RedirectionPage;
