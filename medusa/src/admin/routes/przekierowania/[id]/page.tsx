import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { QUERY_KEYS } from "../../../lib/constants/query-keys";
import { getRedirectionDetails } from "../../../lib/api/redirection";
import { TCreateRedirectionFormBody } from "../../../components/redirection/schemas";
import RedirectionForm from "../../../components/redirection/redirection-form";

const EditRedirectionPage = () => {
  const redirectionId = window.location.pathname.split("/").pop() as string;

  const [initialValues, setInitialValues] =
    useState<TCreateRedirectionFormBody | null>(null);

  const { data, isFetched, refetch } = useQuery({
    queryKey: [QUERY_KEYS.REDIRECTION_DETAILS, { redirectionId }],
    queryFn: () => getRedirectionDetails(redirectionId),
    enabled: !!redirectionId,
  });

  useEffect(() => {
    if (isFetched && data) {
      setInitialValues({
        from_path: data?.from_path,
        status_code: data?.status_code,
        to_path: data?.to_path,
      });
    }
  }, [isFetched, data]);

  return (
    <>
      {initialValues && (
        <RedirectionForm
          idToEdit={data?.id}
          editMode
          onSuccessfulSubmit={() => refetch()}
          initialValues={initialValues}
        />
      )}
    </>
  );
};

export default EditRedirectionPage;
