import { <PERSON><PERSON><PERSON>, Spinner } from "@medusajs/icons";
import {
  <PERSON><PERSON>,
  Container,
  FocusModal,
  Heading,
  Table,
  Text,
  Input,
} from "@medusajs/ui";
import { useQuery } from "@tanstack/react-query";
import { QUERY_KEYS } from "../../lib/constants/query-keys";
import { useState, useMemo } from "react";
import TourForm from "../../components/tours/tour-form";
import { getAllTours } from "../../lib/api/tours";
import TourTableRow from "../../components/tours/tour-table-row";
import { defineRouteConfig } from "@medusajs/admin-sdk";
import { TTourExtendedByProduct } from "../../types";
import { fuzzySearch } from "../../lib/utils/fuzzy-search";

import "./index.css";

import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";

import useUpdateTourPosition from "../../components/tours/hooks/update-tour-position";

const ToursPage = () => {
  const [modalOpen, setModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const { data, isFetching, refetch } = useQuery<{
    tours: TTourExtendedByProduct[];
  }>({
    queryKey: [QUERY_KEYS.ALL_TOURS],
    queryFn: () => getAllTours(),
  });

  const tours = data?.tours || [];

  const filteredTours = useMemo(() => {
    return fuzzySearch(tours, searchQuery, (tour) => tour.name);
  }, [tours, searchQuery]);

  const { handleDragEnd } = useUpdateTourPosition({
    tours,
    refetch,
  });

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  return (
    <Container className="p-0 flex flex-col overflow-hidden">
      <div className="px-4 py-6 flex items-center justify-between">
        <Heading level="h2">Atrakcje</Heading>
        {isFetching && (
          <div className="flex gap-2 items-center ml-auto mr-2">
            <div className="size-2 rounded-full bg-orange-400" />
            <Text>Synchronizowanie listy</Text>
            <Spinner className="animate-spin mx-1 size-4" />
          </div>
        )}

        <FocusModal open={modalOpen} onOpenChange={setModalOpen}>
          <FocusModal.Trigger asChild>
            <Button>Dodaj nową atrakcję</Button>
          </FocusModal.Trigger>
          <FocusModal.Content>
            <FocusModal.Header>
              <FocusModal.Title>Dodaj nową atrakcję</FocusModal.Title>
            </FocusModal.Header>
            <FocusModal.Body className="overflow-y-auto">
              <TourForm
                initialValues={{
                  is_active: true,
                  product_pricing_variant: "dynamic_age_groups",
                }}
                onSuccessfulSubmit={() => {
                  refetch();
                  setModalOpen(false);
                }}
              />
            </FocusModal.Body>
          </FocusModal.Content>
        </FocusModal>
      </div>

      <div className="px-4 mb-4">
        <Input
          type="search"
          placeholder="Szukaj atrakcji po nazwie..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="max-w-xs"
        />
      </div>

      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <div className="overflow-x-auto w-full">
          <Table className="w-full">
            <Table.Header>
              <Table.Row>
                <Table.HeaderCell>Pozycja</Table.HeaderCell>
                <Table.HeaderCell>Miniaturka</Table.HeaderCell>
                <Table.HeaderCell>Slug</Table.HeaderCell>
                <Table.HeaderCell>Opublikowany</Table.HeaderCell>
                <Table.HeaderCell>Tytuł</Table.HeaderCell>
                <Table.HeaderCell>Stworzono</Table.HeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              <SortableContext
                items={filteredTours.map((tour) => tour.id)}
                strategy={verticalListSortingStrategy}
              >
                {filteredTours.map((tour) => (
                  <TourTableRow tour={tour} key={tour.id} refetch={refetch} />
                ))}
              </SortableContext>
            </Table.Body>
          </Table>
        </div>
      </DndContext>
    </Container>
  );
};

export const config = defineRouteConfig({
  label: "Atrakcje",
  icon: MapPin,
});

export default ToursPage;
