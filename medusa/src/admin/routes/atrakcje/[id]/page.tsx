import TourForm from "../../../components/tours/tour-form";
import { useQuery } from "@tanstack/react-query";
import { TCreateTourFormBody } from "../../../components/tours/schemas";
import { useEffect, useState } from "react";
import { getTourDetails } from "../../../lib/api/tours";
import { QUERY_KEYS } from "../../../lib/constants/query-keys";
import { TProductWithVariantWithPrices } from "../../../types";
import { Loader } from "@medusajs/icons";
import { Button, Text } from "@medusajs/ui";

const EditAttractionPage = () => {
  const attractionId = window.location.pathname.split("/").pop();

  const [initialValues, setInitialValues] =
    useState<TCreateTourFormBody | null>(null);

  const { data, isFetched, isLoading, refetch } = useQuery({
    queryKey: [QUERY_KEYS.TOUR_DETAILS, { attractionId }],
    queryFn: () => getTourDetails(attractionId!),
    enabled: !!attractionId,
  });

  const tourDetails = data?.tour;

  const productDetails = data?.tour?.product as TProductWithVariantWithPrices;

  useEffect(() => {
    if (!tourDetails) return;

    if (isFetched && data) {
      setInitialValues({
        ...tourDetails,
        recommended_tour_ids: tourDetails.recommended_tour_ids || [],
        category_ids:
          tourDetails.product?.categories?.map((category) => category.id) || [],
        cities: tourDetails.cities?.map((city) => city.id),
        pricing: {
          ...tourDetails.pricing,
          ...(tourDetails.product_pricing_variant === "dynamic_age_groups" && {
            age_groups:
              productDetails?.variants
                ?.filter((v) => !v.metadata?.start_date)
                .map((v) => ({
                  name: v.title || "",
                  price: v.prices[0].amount,
                })) || [],

            date_variants:
              Array.from(
                new Set(
                  productDetails?.variants
                    ?.filter((v) => v.metadata?.start_date)
                    // Only take one variant per date range and age group to avoid duplicates
                    .filter(
                      (v, index, self) =>
                        index ===
                        self.findIndex(
                          (t) =>
                            t.metadata?.start_date === v.metadata?.start_date &&
                            t.metadata?.end_date === v.metadata?.end_date
                        )
                    )
                    .map((v) => ({
                      start_date: v.metadata?.start_date,
                      end_date: v.metadata?.end_date,
                    }))
                )
              ).map((dateRange) => {
                // Get base variants (without dates) to know the age groups order
                const baseVariants =
                  productDetails?.variants?.filter(
                    (v) => !v.metadata?.start_date
                  ) || [];

                // For each base variant (age group), find its corresponding date variant
                const age_group_prices = baseVariants.map((baseVariant) => {
                  const dateVariant = productDetails?.variants?.find(
                    (v) =>
                      v.metadata?.start_date === dateRange.start_date &&
                      v.metadata?.end_date === dateRange.end_date &&
                      v.metadata?.age_group_name === baseVariant.title
                  );

                  return {
                    price: dateVariant?.prices[0]?.amount || 0,
                  };
                });

                return {
                  end_date: dateRange.end_date,
                  start_date: dateRange.start_date,
                  age_group_prices,
                };
              }) || [],
          }),
          ...(tourDetails.product_pricing_variant === "regular_variants" && {
            regular_variants:
              productDetails?.variants?.map((v) => ({
                name: v.title || "",
                price: v.prices[0]?.amount || 0,
              })) || [],
          }),
          ...(tourDetails.product_pricing_variant === "boat_rental" && {
            boat_variants: {
              base_price:
                productDetails?.variants?.find(
                  (v) => v.title === "Cena podstawowa"
                )?.prices[0]?.amount || 0,
              date_ranges:
                productDetails?.variants
                  ?.filter((v) => v.title !== "Cena podstawowa")
                  .map((v) => ({
                    start_date: v.metadata?.start_date || "",
                    end_date: v.metadata?.end_date || "",
                    price: v.prices[0]?.amount || 0,
                  })) || [],
            },
          }),
        },
      });
    }
  }, [isFetched, tourDetails]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen gap-2 text-ui-text-subtle">
        <Loader className="size-4 animate-spin" />
        <Text>Ładowanie danych...</Text>
      </div>
    );
  }

  if (!tourDetails) {
    return (
      <div className="flex items-center justify-center h-screen gap-2 text-ui-text-subtle flex-col">
        <Text className="text-ui-text-danger">
          Nie udało się załadować danych dla atrakcji
        </Text>
        <Button variant="secondary" onClick={() => refetch()}>
          Spróbuj ponownie
        </Button>
      </div>
    );
  }

  return (
    <section className="bg-ui-bg-base">
      {initialValues && (
        <TourForm
          productId={tourDetails.product.id}
          tourId={tourDetails.id}
          editMode
          onSuccessfulSubmit={() => refetch()}
          initialValues={initialValues}
        />
      )}
    </section>
  );
};

export default EditAttractionPage;
