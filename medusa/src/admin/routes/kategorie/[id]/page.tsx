import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { QUERY_KEYS } from "../../../lib/constants/query-keys";
import { getExtendedCategoryDetails } from "../../../lib/api/categories";
import { TCreateExtendedCategoryFormBody } from "../../../components/tours/schemas";
import ExtendedCategoryForm from "../../../components/categories/extended-category-form";

const EditCategoryPage = () => {
  const categoryId = window.location.pathname.split("/").pop();

  const [initialValues, setInitialValues] =
    useState<TCreateExtendedCategoryFormBody | null>(null);
  const { data, isFetched, refetch } = useQuery({
    queryKey: [QUERY_KEYS.GUIDE_DETAILS, { categoryId }],
    queryFn: () => getExtendedCategoryDetails(categoryId!),
    enabled: !!categoryId,
  });

  useEffect(() => {
    if (isFetched && data) {
      setInitialValues({
        ...data,
      });
    }
  }, [isFetched, data]);

  return (
    <>
      {initialValues && (
        <ExtendedCategoryForm
          idToEdit={data?.id!}
          editMode
          onSuccessfulSubmit={() => refetch()}
          initialValues={initialValues}
        />
      )}
    </>
  );
};

export default EditCategoryPage;
