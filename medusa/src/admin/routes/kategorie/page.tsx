import { defineRouteConfig } from "@medusajs/admin-sdk";
import { <PERSON><PERSON><PERSON><PERSON>, Spinner } from "@medusajs/icons";
import {
  Button,
  Container,
  Heading,
  FocusModal,
  Input,
  Table,
  Text,
} from "@medusajs/ui";
import { useMemo, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { QUERY_KEYS } from "../../lib/constants/query-keys";
import { getExtendedCategories } from "../../lib/api/categories";
import CategoryTableRow from "../../components/categories/category-table-row";
import ExtendedCategoryForm from "../../components/categories/extended-category-form";
import {
  DndContext,
  KeyboardSensor,
  PointerSensor,
  rectIntersection,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import useUpdateCategoryPosition from "../../components/categories/hooks/update-category-position";

const CategoriesPage = () => {
  const [modalOpen, setModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const { data, isFetching, refetch } = useQuery({
    queryKey: [QUERY_KEYS.ALL_CATEGORIES],
    queryFn: getExtendedCategories,
  });

  const { handleDragEnd } = useUpdateCategoryPosition({
    categories: data || [],
    refetch,
  });

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const orderedItems = useMemo(
    () => () => (data || []).sort((a, b) => (a.rank ?? 0) - (b.rank ?? 0)),
    [data]
  );

  const items = useMemo(
    () => orderedItems().map((category) => category.id),
    [orderedItems]
  );

  const filteredCategories = useMemo(() => {
    if (!data) return [];
    return data.filter((category) =>
      category.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [data, searchQuery]);

  return (
    <Container className="p-0 flex flex-col overflow-hidden">
      <div className="px-4 py-6 flex items-center justify-between">
        <Heading level="h2">Kategorie</Heading>
        {isFetching && (
          <div className="flex gap-2 items-center ml-auto mr-2">
            <div className="size-2 rounded-full bg-orange-400" />
            <Text>Synchronizowanie listy</Text>
            <Spinner className="animate-spin mx-1 size-4" />
          </div>
        )}

        <FocusModal open={modalOpen} onOpenChange={setModalOpen}>
          <FocusModal.Trigger asChild>
            <Button>Dodaj nową kategorię</Button>
          </FocusModal.Trigger>
          <FocusModal.Content>
            <FocusModal.Header>
              <FocusModal.Title>Dodaj nową kategorię</FocusModal.Title>
            </FocusModal.Header>
            <FocusModal.Body className="overflow-y-auto">
              <ExtendedCategoryForm
                onSuccessfulSubmit={() => {
                  refetch();
                  setModalOpen(false);
                }}
              />
            </FocusModal.Body>
          </FocusModal.Content>
        </FocusModal>
      </div>

      <div className="px-4 mb-4">
        <Input
          type="search"
          placeholder="Szukaj kategorii po nazwie..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="max-w-xs"
        />
      </div>

      <DndContext
        sensors={sensors}
        collisionDetection={rectIntersection}
        onDragEnd={handleDragEnd}
        autoScroll={{
          interval: 25,
          acceleration: 50,
          threshold: {
            y: 0.2,
            x: 0,
          },
        }}
      >
        <div className="overflow-x-auto w-full">
          <Table className="w-full">
            <Table.Header>
              <Table.Row>
                <Table.HeaderCell>Pozycja</Table.HeaderCell>
                <Table.HeaderCell>Miniaturka</Table.HeaderCell>
                <Table.HeaderCell>Slug</Table.HeaderCell>
                <Table.HeaderCell>Opublikowany</Table.HeaderCell>
                <Table.HeaderCell>Nazwa kategorii</Table.HeaderCell>
                <Table.HeaderCell>Stworzono</Table.HeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              <SortableContext
                items={items}
                strategy={verticalListSortingStrategy}
              >
                {filteredCategories.map((category) => (
                  <CategoryTableRow
                    refetch={refetch}
                    category={category}
                    key={category.id}
                  />
                ))}
              </SortableContext>
            </Table.Body>
          </Table>
        </div>
      </DndContext>
    </Container>
  );
};

export const config = defineRouteConfig({
  label: "Kategorie",
  icon: GridList,
});

export default CategoriesPage;
