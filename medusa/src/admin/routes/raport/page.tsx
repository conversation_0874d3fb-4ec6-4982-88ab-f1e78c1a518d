import { defineRouteConfig } from "@medusajs/admin-sdk";
import { ArrowPath, ListCheckbox, Spinner } from "@medusajs/icons";
import {
  Container,
  Heading,
  Table,
  Text,
  DatePicker,
  Label,
  Button,
} from "@medusajs/ui";
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { QUERY_KEYS } from "../../lib/constants/query-keys";

import ReportTableRow from "../../components/reports/report-table-row";
import { getReport } from "../../lib/api/report";

const ReportsPage = () => {
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);

  const formatDateForQuery = (date: Date | null, isEndDate: boolean = false) => {
    if (!date) return null;
    
    const adjustedDate = new Date(date);
    
    if (isEndDate) {
      // Set to end of day: 23:59:59.999
      adjustedDate.setHours(23, 59, 59, 999);
    } else {
      // Set to start of day: 00:00:00.000
      adjustedDate.setHours(0, 0, 0, 0);
    }
    
    return adjustedDate.toISOString();
  };

  const { data, isFetching, error, refetch } = useQuery({
    queryKey: [
      QUERY_KEYS.REPORT,
      {
        dateFrom: formatDateForQuery(startDate),
        dateTo: formatDateForQuery(endDate, true),
      },
    ],
    queryFn: () =>
      getReport({
        dateFrom: formatDateForQuery(startDate) ?? "1970-01-01T00:00:00Z",
        dateTo: formatDateForQuery(endDate, true) ?? "2100-01-01T00:00:00Z",
      }),
  });

  if (error) {
    return (
      <Container className="w-full overflow-hidden">
        <Text className="text-center">{error?.message}</Text>
      </Container>
    );
  }
  return (
    <Container className="p-0 flex flex-col overflow-hidden">
      <div className="px-4 py-6 flex items-center justify-between">
        <Heading level="h2">Raporty</Heading>
        {isFetching && (
          <div className="flex gap-2 items-center ml-auto mr-2">
            <div className="size-2 rounded-full bg-orange-400" />
            <Text>Synchronizowanie listy</Text>
            <Spinner className="animate-spin mx-1 size-4" />
          </div>
        )}
        <Button variant="secondary" onClick={() => refetch()}>
          Odśwież dane
          <ArrowPath className={`size-4 ${isFetching ? "animate-spin" : ""}`} />
        </Button>
      </div>

      <div className="px-4 mb-4 flex gap-4 items-center">
        <Label>Od</Label>
        <DatePicker
          value={startDate}
          onChange={(value) => setStartDate(value)}
        />
        <Label>Do</Label>
        <DatePicker value={endDate} onChange={(value) => setEndDate(value)} />
      </div>

      <Table>
        <Table.Header>
          <Table.Row>
            <Table.HeaderCell>Kategoria</Table.HeaderCell>
            <Table.HeaderCell>Wartość</Table.HeaderCell>
          </Table.Row>
        </Table.Header>
        <Table.Body>{data && <ReportTableRow report={data} />}</Table.Body>
      </Table>
    </Container>
  );
};

export const config = defineRouteConfig({
  label: "Raporty",
  icon: ListCheckbox,
});

export default ReportsPage;
