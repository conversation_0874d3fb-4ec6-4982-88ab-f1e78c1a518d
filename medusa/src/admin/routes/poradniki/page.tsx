import { defineRouteConfig } from "@medusajs/admin-sdk";
import { ChatBubbleLeftRight, Spinner } from "@medusajs/icons";
import {
  Button,
  Container,
  FocusModal,
  Heading,
  Text,
  Input,
} from "@medusajs/ui";
import { useQuery } from "@tanstack/react-query";
import GuidePostForm from "../../components/guides/guide-post-form";
import { getAllGuides } from "../../lib/api/guides";
import { QUERY_KEYS } from "../../lib/constants/query-keys";
import { useState, useMemo, useDeferredValue, Suspense } from "react";
import GuidesTable from "../../components/guides/hooks/guides-table";
import { fuzzySearch } from "../../lib/utils/fuzzy-search";

const GuidesPage = () => {
  const [modalOpen, setModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const defferedValue = useDeferredValue(searchQuery);

  const { data, isFetching, refetch } = useQuery({
    queryKey: [QUERY_KEYS.ALL_GUIDES],
    queryFn: getAllGuides,
  });

  const filteredGuides = useMemo(() => {
    if (!data) return [];
    return fuzzySearch(data, defferedValue, (guide) => guide.name);
  }, [data, defferedValue]);

  return (
    <Container className="p-0 flex flex-col overflow-hidden">
      <div className="px-4 py-6 flex items-center justify-between">
        <Heading level="h2">Poradniki</Heading>
        {isFetching && (
          <div className="flex gap-2 items-center ml-auto mr-2">
            <div className="size-2 rounded-full bg-orange-400" />
            <Text>Synchronizowanie listy</Text>
            <Spinner className="animate-spin mx-1 size-4" />
          </div>
        )}

        <FocusModal open={modalOpen} onOpenChange={setModalOpen}>
          <FocusModal.Trigger asChild>
            <Button>Dodaj nowy poradnik</Button>
          </FocusModal.Trigger>
          <FocusModal.Content>
            <FocusModal.Header>
              <FocusModal.Title>Dodaj nowy poradnik</FocusModal.Title>
            </FocusModal.Header>
            <FocusModal.Body className="overflow-y-auto">
              <GuidePostForm
                onSuccessfulSubmit={() => {
                  refetch();
                  setModalOpen(false);
                }}
              />
            </FocusModal.Body>
          </FocusModal.Content>
        </FocusModal>
      </div>

      <div className="px-4 mb-4">
        <Input
          type="search"
          placeholder="Szukaj poradników po tytule..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="max-w-xs"
        />
      </div>

      <Suspense fallback={<div>Ładowanie poradników...</div>}>
        <GuidesTable guides={filteredGuides} refetch={refetch} />
      </Suspense>
    </Container>
  );
};

export const config = defineRouteConfig({
  label: "Poradniki",
  icon: ChatBubbleLeftRight,
});

export default GuidesPage;
