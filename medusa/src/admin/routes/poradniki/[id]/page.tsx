import GuidePostForm from "../../../components/guides/guide-post-form";
import { useQuery } from "@tanstack/react-query";
import { TCreateGuideFormBody } from "../../../components/guides/schemas";
import { useEffect, useState } from "react";
import { getGuideDetails } from "../../../lib/api/guides";
import { QUERY_KEYS } from "../../../lib/constants/query-keys";

const EditGuidePage = () => {
  const guideId = window.location.pathname.split("/").pop();

  const [initialValues, setInitialValues] =
    useState<TCreateGuideFormBody | null>(null);
  const { data, isFetched, refetch } = useQuery({
    queryKey: [QUERY_KEYS.GUIDE_DETAILS, { guideId }],
    queryFn: () => getGuideDetails(guideId!),
    enabled: !!guideId,
  });

  useEffect(() => {
    if (isFetched && data) {
      setInitialValues({
        seo_title: data.seo_title,
        og_title: data.og_title,
        related_guides: data.related_guides,
        name: data.name,
        slug: data.slug,
        content: data.content as unknown as string,
        featured_image: data.featured_image,
        meta_description: data.meta_description,
        tags: data.tags,
        is_published: data.is_published,
        preview_image: data.preview_image,
        position: data.position,
      });
    }
  }, [isFetched, data]);

  return (
    <>
      {initialValues && (
        <GuidePostForm
          idToEdit={data?.id!}
          editMode
          onSuccessfulSubmit={() => refetch()}
          initialValues={initialValues}
        />
      )}
    </>
  );
};

export default EditGuidePage;
