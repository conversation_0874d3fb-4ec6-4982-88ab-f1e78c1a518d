import { defineRouteConfig } from "@medusajs/admin-sdk";
import { List<PERSON>ree, Spinner } from "@medusajs/icons";
import {
  Button,
  Container,
  Heading,
  FocusModal,
  Input,
  Table,
  Text,
} from "@medusajs/ui";
import { useMemo, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { QUERY_KEYS } from "../../lib/constants/query-keys";
import { getAllCities } from "../../lib/api/cities";
import CityForm from "../../components/cities/city-form";
import CityTableRow from "../../components/cities/city-table-row";

const CitiesPage = () => {
  const [modalOpen, setModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const { data, isFetching, refetch } = useQuery({
    queryKey: [QUERY_KEYS.ALL_CITIES],
    queryFn: getAllCities,
  });

  const filteredCities = useMemo(() => {
    if (!data) return [];
    return data.filter((city) =>
      city.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [data, searchQuery]);

  return (
    <Container className="p-0 flex flex-col overflow-hidden">
      <div className="px-4 py-6 flex items-center justify-between">
        <Heading level="h2">Miasta</Heading>
        {isFetching && (
          <div className="flex gap-2 items-center ml-auto mr-2">
            <div className="size-2 rounded-full bg-orange-400" />
            <Text>Synchronizowanie listy</Text>
            <Spinner className="animate-spin mx-1 size-4" />
          </div>
        )}

        <FocusModal open={modalOpen} onOpenChange={setModalOpen}>
          <FocusModal.Trigger asChild>
            <Button>Dodaj nowe miasto</Button>
          </FocusModal.Trigger>
          <FocusModal.Content>
            <FocusModal.Header>
              <FocusModal.Title>Dodaj nowe miasto</FocusModal.Title>
            </FocusModal.Header>
            <FocusModal.Body className="overflow-y-auto">
              <CityForm
                onSuccessfulSubmit={() => {
                  refetch();
                  setModalOpen(false);
                }}
              />
            </FocusModal.Body>
          </FocusModal.Content>
        </FocusModal>
      </div>

      <div className="px-4 mb-4">
        <Input
          type="search"
          placeholder="Szukaj miast po tytule..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="max-w-xs"
        />
      </div>

      <div className="overflow-x-auto w-full">
        <Table className="w-full">
          <Table.Header>
            <Table.Row>
              <Table.HeaderCell>Nazwa miasta</Table.HeaderCell>
              <Table.HeaderCell>Stworzono</Table.HeaderCell>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {filteredCities.map((city) => (
              <CityTableRow refetch={refetch} city={city} key={city.id} />
            ))}
          </Table.Body>
        </Table>
      </div>
    </Container>
  );
};

export const config = defineRouteConfig({
  label: "Miasta",
  icon: ListTree,
});

export default CitiesPage;
