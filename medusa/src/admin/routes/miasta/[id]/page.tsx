import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { QUERY_KEYS } from "../../../lib/constants/query-keys";
import { TCreateCityFormBody } from "../../../components/tours/schemas";
import { getCityDetails } from "../../../lib/api/cities";
import CityForm from "../../../components/cities/city-form";

const EditCitiesPage = () => {
  const cityId = window.location.pathname.split("/").pop();

  const [initialValues, setInitialValues] =
    useState<TCreateCityFormBody | null>(null);
  const { data, isFetched, refetch } = useQuery({
    queryKey: [QUERY_KEYS.CITY_DETAILS, { cityId }],
    queryFn: () => getCityDetails(cityId!),
    enabled: !!cityId,
  });

  useEffect(() => {
    if (isFetched && data) {
      setInitialValues({
        name: data.name,
      });
    }
  }, [isFetched, data]);

  return (
    <>
      {initialValues && (
        <CityForm
          idToEdit={data?.id!}
          editMode
          onSuccessfulSubmit={() => refetch()}
          initialValues={initialValues}
        />
      )}
    </>
  );
};

export default EditCitiesPage;
