import { useEffect } from "react";

/**
 * Hook to hide elements in the Medusa Admin UI based on a CSS selector
 * @param selector - The CSS selector for the element to hide
 * @param method - The method to use for hiding the element: "display-none" or "remove"
 * @returns void
 */
export const useHideAdminElement = (
  selector: string,
  method: "display-none" | "remove" = "display-none"
) => {
  // When dealing with complex CSS selectors, it's better to use the raw selector
  // directly rather than trying to escape it, as escaping can sometimes break
  // valid selectors. The browser's querySelector can handle properly formatted selectors.

  // Function to hide the element
  const hideElement = () => {
    try {
      // Use the raw selector directly
      const element = document.querySelector(selector);

      if (!element) {
        console.warn(`Element with selector "${selector}" not found`);
        return;
      }

      if (method === "display-none") {
        // Hide the element by setting display: none
        (element as HTMLElement).style.display = "none";
      } else if (method === "remove") {
        // Remove the element from the DOM completely
        element.parentNode?.removeChild(element);
      }
    } catch (error) {
      console.error(`Error hiding element with selector "${selector}":`, error);
    }
  };

  // Apply the hiding once the component mounts
  useEffect(() => {
    // Initial check might need a delay for dynamically rendered elements
    setTimeout(hideElement, 100);

    // Create a MutationObserver to handle dynamically loaded elements
    const observer = new MutationObserver(() => {
      // Check if our target appears in the DOM after changes
      const element = document.querySelector(selector);
      if (element) {
        hideElement();
      }
    });

    // Start observing the document with the configured parameters
    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    // Clean up the observer when component unmounts
    return () => {
      observer.disconnect();
    };
  }, [selector, method]);
};
