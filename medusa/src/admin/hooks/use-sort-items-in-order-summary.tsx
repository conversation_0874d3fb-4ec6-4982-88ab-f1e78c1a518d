import { useEffect } from "react";
import { sortByAgeGroup } from "../../lib/utils";

/**
 * Custom hook to sort items in order summary by age group
 *
 * This hook:
 * 1. Finds the container element in the order summary
 * 2. Gets all child items
 * 3. Extracts name text from each item
 * 4. Sorts items using sortByAgeGroup function
 * 5. Reorders the children in the container
 */
export const useSortItemsInOrderSummary = () => {
  useEffect(() => {
    // Function to sort and reorder items
    const sortItemsInOrderSummary = () => {
      try {
        // Find the container element using the selector
        const containerSelector =
          "#medusa > div > div.flex.h-screen.w-full.flex-col.overflow-auto > main > div > div > " +
          "div.flex.w-full.flex-col.items-start.gap-x-4.gap-y-3.xl\\:grid.xl\\:grid-cols-\\[minmax\\(0\\,_1fr\\)_440px\\] > " +
          "div.flex.w-full.min-w-0.flex-col.gap-y-3 > div.flex.w-full.flex-col.gap-y-3 > " +
          "div:nth-child(2) > div:nth-child(2)";

        const container = document.querySelector(containerSelector);

        // Try a simpler selector as fallback if needed
        let actualContainer: Element | null = container;
        if (!actualContainer) {
          console.error("Order summary container not found");
          const simpleSelector = "#medusa div:nth-child(2) > div:nth-child(2)";
          actualContainer = document.querySelector(simpleSelector);

          if (!actualContainer) {
            return;
          }
        }

        // Get all child items
        const childItems = Array.from(actualContainer.children);

        // Create a map to keep track of original DOM elements
        const elementMap = new Map<string, Element>();

        // Extract names from each item using the name selector for each child
        const itemsToSort = childItems.map((item, index) => {
          // Target the paragraph with the item name using class selectors
          const nameSelector =
            "p.font-medium.font-sans.txt-compact-small.text-ui-fg-base";
          const nameElement = item.querySelector(nameSelector);
          const name = nameElement?.textContent || "";

          // Store a reference to the original element with a unique key
          const key = `item-${index}`;
          elementMap.set(key, item);

          return {
            title: name || "",
            key: key,
          };
        });

        // Sort items using the sortByAgeGroup function
        let sortedItems;
        try {
          sortedItems = sortByAgeGroup(itemsToSort);
        } catch (error) {
          // Fallback to simple sort if sortByAgeGroup fails
          sortedItems = itemsToSort
            .slice()
            .sort((a, b) => a.title.localeCompare(b.title));
        }

        if (!sortedItems) {
          return;
        }

        // Reorder children in the container
        sortedItems.forEach((item: any) => {
          const element = elementMap.get(item.key);
          if (element) {
            actualContainer?.appendChild(element);
          }
        });
      } catch (error) {
        console.error("Error sorting order summary items:", error);
      }
    };

    // Wait for DOM to be ready
    const timeoutId = setTimeout(() => {
      sortItemsInOrderSummary();
    }, 100);

    // Cleanup timeout if component unmounts
    return () => {
      clearTimeout(timeoutId);
    };
  }, []); // Empty dependency array to run only once on mount
};

export default useSortItemsInOrderSummary;
