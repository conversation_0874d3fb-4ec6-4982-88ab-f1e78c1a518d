import { useState, useCallback, useMemo } from "react";
import { OrderStatus } from "../widgets/order-status/types";

// Enhanced attraction name matching function
const createAttractionNameMatcher = (searchQuery: string) => {
  if (!searchQuery || searchQuery.trim() === "") {
    return () => true;
  }

  const queryWords = searchQuery
    .toLowerCase()
    .trim()
    .split(/\s+/)
    .filter((word) => word.length > 0);

  return (attractionName: string) => {
    if (!attractionName) return false;

    const attractionWords = attractionName
      .toLowerCase()
      .split(/\s+/)
      .filter((word) => word.length > 0);

    // Check if all query words have a match in attraction name
    return queryWords.every((queryWord) =>
      attractionWords.some((attractionWord) =>
        attractionWord.includes(queryWord)
      )
    );
  };
};

export type OrderFilters = {
  display_id?: string;
  "customer.name"?: string;
  "metadata.status"?: OrderStatus;
  "ticket.attraction_name"?: string;
  "ticket.selected_date_from"?: string;
  "ticket.selected_date_to"?: string;
  "ticket.selected_date"?: string;
};

type UseOrderFilteringProps = {
  initialFilters?: OrderFilters;
};

export const useOrderFiltering = ({
  initialFilters = {},
}: UseOrderFilteringProps = {}) => {
  const [filters, setFilters] = useState<OrderFilters>(initialFilters);
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  const [orderBy, setOrderBy] = useState<string>("created_at");
  const [orderDirection, setOrderDirection] = useState<"ASC" | "DESC">("DESC");
  const [currentPage, setCurrentPage] = useState(0);

  // For specific filter controls
  const [orderNumberSearch, setOrderNumberSearch] = useState<string>("");
  const [dateFrom, setDateFrom] = useState<Date | null>(null);
  const [dateTo, setDateTo] = useState<Date | null>(null);

  const handleFilterChange = useCallback((key: string, value: any) => {
    setFilters((prev) => {
      const newFilters = { ...prev };

      if (value === "" || value === undefined) {
        delete newFilters[key as keyof OrderFilters];

        // Remove from active filters
        setActiveFilters((prev) => prev.filter((f) => f !== key));
      } else {
        newFilters[key as keyof OrderFilters] = value;

        // Add to active filters if not already there
        setActiveFilters((prev) => {
          if (!prev.includes(key)) {
            return [...prev, key];
          }
          return prev;
        });
      }

      return newFilters;
    });

    // Reset to first page when filters change
    setCurrentPage(0);
  }, []);

  const clearFilter = useCallback(
    (key: string) => {
      handleFilterChange(key, "");
    },
    [handleFilterChange]
  );

  const clearAllFilters = useCallback(() => {
    setFilters({});
    setActiveFilters([]);
    setCurrentPage(0);
    setOrderNumberSearch("");
    setDateFrom(null);
    setDateTo(null);
  }, []);

  const handleSort = useCallback(
    (key: string) => {
      if (orderBy === key) {
        // Toggle order direction if same column
        setOrderDirection((prev) => (prev === "ASC" ? "DESC" : "ASC"));
      } else {
        // New column, default to DESC
        setOrderBy(key);
        setOrderDirection("DESC");
      }
      setCurrentPage(0);
    },
    [orderBy]
  );

  // Handle date range filter
  const handleDateRangeChange = useCallback(
    (fromDate: Date | null, toDate: Date | null) => {
      setDateFrom(fromDate);
      setDateTo(toDate);

      const newFilters = { ...filters } as Record<string, any>;

      // Clear existing date filter if any
      if ("ticket.selected_date" in newFilters) {
        delete newFilters["ticket.selected_date"];
        setActiveFilters((prev) =>
          prev.filter((f) => f !== "ticket.selected_date")
        );
      }

      if (fromDate) {
        const fromDateStr = fromDate.toISOString().split("T")[0];
        newFilters["ticket.selected_date_from"] = fromDateStr;

        if (!activeFilters.includes("ticket.selected_date_from")) {
          setActiveFilters((prev) => [...prev, "ticket.selected_date_from"]);
        }
      } else {
        delete newFilters["ticket.selected_date_from"];
        setActiveFilters((prev) =>
          prev.filter((f) => f !== "ticket.selected_date_from")
        );
      }

      if (toDate) {
        const toDateStr = toDate.toISOString().split("T")[0];
        newFilters["ticket.selected_date_to"] = toDateStr;

        if (!activeFilters.includes("ticket.selected_date_to")) {
          setActiveFilters((prev) => [...prev, "ticket.selected_date_to"]);
        }
      } else {
        delete newFilters["ticket.selected_date_to"];
        setActiveFilters((prev) =>
          prev.filter((f) => f !== "ticket.selected_date_to")
        );
      }

      setFilters(newFilters as OrderFilters);
      setCurrentPage(0);
    },
    [filters, activeFilters]
  );

  // Handle order number search
  const handleOrderNumberSearch = useCallback(() => {
    if (orderNumberSearch) {
      handleFilterChange("display_id", orderNumberSearch);
    } else {
      clearFilter("display_id");
    }
  }, [orderNumberSearch, handleFilterChange, clearFilter]);

  const limit = 100;
  const offset = useMemo(() => {
    return currentPage * limit;
  }, [currentPage]);

  // Enhanced attraction name matcher
  const attractionNameMatcher = useMemo(() => {
    return createAttractionNameMatcher(filters["ticket.attraction_name"] || "");
  }, [filters["ticket.attraction_name"]]);

  return {
    // State
    filters,
    activeFilters,
    orderBy,
    orderDirection,
    currentPage,
    limit,
    offset,
    orderNumberSearch,
    dateFrom,
    dateTo,

    // Setters
    setCurrentPage,
    setOrderNumberSearch,

    // Handlers
    handleFilterChange,
    clearFilter,
    clearAllFilters,
    handleSort,
    handleDateRangeChange,
    handleOrderNumberSearch,

    // Enhanced matching
    attractionNameMatcher,
  };
};
