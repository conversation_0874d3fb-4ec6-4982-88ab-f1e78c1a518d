import { AdminOrder } from "@medusajs/types";
import { Order } from "@medusajs/types/dist/dal/utils";

export type IssueRefundDTO = {
  amount: number;
  reason: string;
};

// Define enhanced types for the order with calculated PLN amount
export type OrderSummary = {
  transaction_total?: number;
  // Include other summary fields as needed
};

// Order with calculated PLN amount
export type EnhancedOrder = AdminOrder & {
  calculated_pln_amount: number | null;
};
