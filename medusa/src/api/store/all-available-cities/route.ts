import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { ContainerRegistrationKeys } from "@medusajs/framework/utils";

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

  const { data: productCities } = await query.graph({
    entity: "product_categories",
    fields: ["products.tour.cities.name", "products.tour.cities.id"],
    filters: {
      products: {
        // @ts-expect-error - query does not seem to infer related entities
        tour: {
          is_active: true,
        },
      },
    },
  });

  if (!productCities.length) {
    res.status(404).json({ message: "Miasta nie zostały znalezione" });
    return;
  }

  const uniqueCitiesById = new Map();

  productCities.forEach((category) => {
    if (category.products.length > 0) {
      category.products.forEach((product) => {
        const cities = product.tour?.cities || [];

        cities.forEach((city) => {
          if (city?.id && city?.name) {
            uniqueCitiesById.set(city.id, city);
          }
        });
      });
    }
  });

  const allCities = Array.from(uniqueCitiesById.values());

  res.status(200).json({
    cities: allCities,
  });
}
