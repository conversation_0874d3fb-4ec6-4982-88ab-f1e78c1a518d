import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { ContainerRegistrationKeys } from "@medusajs/framework/utils";

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
  const { slug } = req.params;

  const { data: productCities } = await query.graph({
    entity: "product_categories",
    fields: ["products.tour.cities.name", "products.tour.cities.id"],
    filters: {
      products: {
        // @ts-expect-error - query does not seem to infer related entities
        tour: {
          is_active: true,
        },
      },
      // @ts-expect-error - query does not seem to infer related entities
      metadata: {
        slug: slug,
      },
    },
  });

  if (!productCities.length) {
    res
      .status(404)
      .json({ message: "Miasta dla tej kategorii nie zostały znalezione" });
    return;
  }

  const uniqueCitiesById = new Map();

  productCities[0].products.forEach((product) => {
    product.tour?.cities?.forEach((city) => {
      uniqueCitiesById.set(city?.id, city);
    });
  });

  res.status(200).json({
    cities: Array.from(uniqueCitiesById.values()),
  });
}
