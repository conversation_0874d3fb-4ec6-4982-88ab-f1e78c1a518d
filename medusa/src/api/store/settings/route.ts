import { MedusaRequest } from "@medusajs/framework/http";
import { MedusaResponse } from "node_modules/@medusajs/framework/dist/http/types";
import { IStoreModuleService } from "@medusajs/types";
import { Modules } from "@medusajs/framework/utils";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const storeService: IStoreModuleService = req.scope.resolve(Modules.STORE);

  const stores = await storeService.listStores();

  const defaultStore = await storeService.retrieveStore(stores[0].id);

  // Extract relevant settings from store metadata
  const settings = {
    is_shop_enabled: defaultStore.metadata?.is_shop_enabled ?? true,
    global_amount_of_hours_calendar_handicap: defaultStore.metadata?.global_amount_of_hours_calendar_handicap ?? 0,
    latest_booking_hour: defaultStore.metadata?.latest_booking_hour ?? 19,
    latest_booking_minute: defaultStore.metadata?.latest_booking_minute ?? 0,
  };

  res.status(200).json(settings);
};
