import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { ContainerRegistrationKeys } from "@medusajs/framework/utils";

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const { category_slug, cities } = req.query;
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

  const { data: productCategories } = await query.graph({
    entity: "product_categories",
    fields: [
      "products.tour.featured_image",
      "products.tour.description",
      "products.tour.slug",
      "products.tour.name",
      "products.tour.position",
      "products.tour.cities.name",
      "products.tour.is_bargain",
      "products.tour.is_promotion",
      "products.tour.is_recommended",
      "products.tour.price_from",
      "products.tour.promotion_price_from",
      "products.tour.hourly_length",
      "products.tour.dates_from_to",
      "products.tour.cities.id",
      "products.tour.blocked_dates.*",
      "products.tour.blocked_dates_by_month.*",
    ],
    filters: {
      // @ts-ignore
      metadata: {
        slug: category_slug,
      },
      products: {
        // @ts-expect-error - query does not seem to infer related entities
        tour: {
          is_active: true,
          ...(cities &&
            Array.isArray(cities) &&
            cities.length > 0 && {
              cities: {
                name: {
                  $in: cities,
                },
              },
            }),
        },
      },
    },
  });

  if (!productCategories.length) {
    res
      .status(404)
      .json({ message: "Produkty dla tej kategorii nie zostały znalezione" });
    return;
  }

  const sortedAttractions = productCategories[0].products.sort(
    (a, b) => (a?.tour?.position ?? 0) - (b?.tour?.position ?? 0)
  );

  res.status(200).json({
    attractions: sortedAttractions.flatMap((product) => product.tour),
  });
}
