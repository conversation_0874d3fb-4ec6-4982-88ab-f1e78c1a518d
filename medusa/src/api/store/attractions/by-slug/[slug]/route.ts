import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { ContainerRegistrationKeys } from "@medusajs/framework/utils";

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const { slug } = req.params;
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

  const { data: tourDetails } = await query.graph({
    entity: "tour",
    fields: [
      "*",
      "product.categories.*",
      "product.variants.*",
      "cities.*",
      "product.*",
      "start_times.*",
      "food_options.*",
      "content_blocks.*",
      "pricing.*",
      "content_blocks.faq.*",
      "tour_seo.*",
      "product.variants.prices.*",
    ],
    filters: {
      slug: [slug],
      is_active: true,
    },
  });

  if (!tourDetails.length) {
    res.status(404).json({ message: "Atrakcja nie znaleziona" });
    return;
  }

  const tourId = tourDetails[0].id;

  const [
    { data: start_places },
    { data: blocked_dates },
    { data: start_places_by_date },
    { data: blocked_dates_by_month },
    { data: blocked_start_times },
  ] = await Promise.all([
    query.graph({
      entity: "tour_available_start_places",
      fields: ["*"],
      filters: {
        tour_id: {
          $eq: tourId,
        },
      },
    }),
    query.graph({
      entity: "tour_blocked_dates",
      fields: ["*"],
      filters: {
        tour_id: { $eq: tourId },
      },
    }),
    query.graph({
      entity: "tour_start_places_by_date",
      fields: ["*"],
      filters: {
        tour_id: {
          $eq: tourId,
        },
      },
    }),
    query.graph({
      entity: "tour_blocked_dates_by_month",
      fields: ["*"],
      filters: {
        tour_id: { $eq: tourId },
      },
    }),
    query.graph({
      entity: "tour_blocked_start_times_by_date",
      fields: ["*"],
      filters: { tour_id: { $eq: tourId } },
    }),
  ]);

  const sortedAvailableStartPlaces = start_places?.sort(
    (a, b) => (a?.position ?? 0) - (b?.position ?? 0)
  );

  res.status(200).json({
    ...tourDetails[0],
    available_start_places: sortedAvailableStartPlaces,
    blocked_dates: blocked_dates,
    start_places_by_date: start_places_by_date,
    blocked_dates_by_month: blocked_dates_by_month,
    blocked_start_times: blocked_start_times,
  });
}
