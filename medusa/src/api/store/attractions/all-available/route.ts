import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { ContainerRegistrationKeys } from "@medusajs/framework/utils";

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const queryParams = req.query;

  const cities = queryParams.cities as string[];
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

  const { data: tours } = await query.graph({
    entity: "tour",
    fields: [
      "featured_image",
      "description",
      "price_from",
      "promotion_price_from",
      "hourly_length",
      "dates_from_to",
      "is_recommended",
      "is_bargain",
      "is_promotion",
      "slug",
      "name",
      "position",
      "cities.name",
      "cities.id",
      "blocked_dates.*",
      "blocked_dates_by_month.*",
    ],
    filters: {
      is_active: true,
      ...(cities &&
        Array.isArray(cities) &&
        cities.length > 0 && {
          cities: {
            name: { $in: cities },
          },
        }),
    },
  });

  if (!tours.length) {
    res.status(404).json({ message: "Nie znaleziono atrakcji" });
    return;
  }

  const sortedAttractions = tours.sort(
    (a, b) => (a?.position ?? 0) - (b?.position ?? 0)
  );

  console.log({ sortedAttractions });
  res.status(200).json({
    attractions: sortedAttractions,
  });
}
