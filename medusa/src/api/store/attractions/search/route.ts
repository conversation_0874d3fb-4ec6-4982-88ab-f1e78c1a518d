import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { TOUR_MODULE } from "src/modules/tours";
import TourService from "src/modules/tours/service";

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const { q } = req.query;

  if (!q || typeof q !== "string") {
    return res.status(400).json({
      message: "Search query parameter 'q' is required",
    });
  }

  const tourService: TourService = req.scope.resolve(TOUR_MODULE);

  try {
    const tours = await tourService.searchToursByQuery(q);

    // Transform the results to match the expected format for the storefront
    const transformedTours = tours.map((tour) => ({
      id: tour.id,
      name: tour.name,
      slug: tour.slug,
      description: tour.description,
      featured_image: tour.featured_image,
      position: tour.position,
      is_active: tour.is_active,
      cities: tour.cities,
      price_from: tour.price_from,
      promotion_price_from: tour.promotion_price_from,
      hourly_length: tour.hourly_length,
      dates_from_to: tour.dates_from_to,
      is_recommended: tour.is_recommended,
      is_bargain: tour.is_bargain,
      is_promotion: tour.is_promotion,
    }));

    res.status(200).json({
      tours: transformedTours,
      count: transformedTours.length,
    });
  } catch (error) {
    console.error("Error searching tours:", error);
    res.status(500).json({
      message: "Internal server error while searching tours",
    });
  }
}
