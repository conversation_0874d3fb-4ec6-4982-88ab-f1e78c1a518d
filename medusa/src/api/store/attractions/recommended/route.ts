import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { ContainerRegistrationKeys } from "@medusajs/framework/utils";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const { ids } = req.query;

  if (!ids) {
    return res.status(400).json({
      message: "Ids are required",
    });
  }

  // Convert query parameter to string array
  const idsArray: string[] = Array.isArray(ids)
    ? ids.map((id) => String(id))
    : String(ids).split(",");

  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

  const { data } = await query.graph({
    entity: "tour",
    fields: ["id", "name", "slug", "featured_image"],
    filters: {
      id: {
        $in: idsArray,
      },
      is_active: true,
    },
  });

  // Sort the attractions in the same order as the requested IDs
  const sortedAttractions = idsArray
    .map((id) => data.find((attraction) => attraction.id === id))
    .filter(Boolean); // Remove any undefined items (in case some IDs weren't found)

  res.status(200).json({
    attractions: sortedAttractions,
  });
};
