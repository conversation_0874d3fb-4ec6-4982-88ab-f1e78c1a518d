import { MedusaRequest } from "@medusajs/framework/http";
import { MedusaResponse } from "node_modules/@medusajs/framework/dist/http/types";
import ExchangeRateService from "src/modules/exchange-rate/service";
import { EXCHANGE_RATE_MODULE } from "src/modules/exchange-rate";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const exchangeRateService: ExchangeRateService =
    req.scope.resolve(EXCHANGE_RATE_MODULE);

  const exchangeRate = await exchangeRateService.getExchangeRate("eur");

  res.status(200).json({ exchangeRate });
};
