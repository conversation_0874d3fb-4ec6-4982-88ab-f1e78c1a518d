import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { Modules } from "@medusajs/framework/utils";
import { es } from "react-day-picker/locale";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const { session_id } = req.params;

  if (!session_id || typeof session_id !== "string") {
    return res.status(400).json({ error: "Session ID is required" });
  }

  const paymentService = req.scope.resolve(Modules.PAYMENT);

  const paymentSession = await paymentService.retrievePaymentSession(
    session_id
  );

  if (!paymentSession) {
    return res.status(404).json({ error: "Payment session not found" });
  }

  res.json({
    status: paymentSession.status,
    data: paymentSession,
  });
};
