import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import {
  PaymentSessionDTO,
  WebhookActionResult,
} from "@medusajs/framework/types";
import { BigNumber, Modules } from "@medusajs/framework/utils";
import { processPaymentWorkflow } from "@medusajs/medusa/core-flows";
import { config } from "src/config";
import { TPayWebhookPayload } from "src/modules/tpay/service";

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const data = req.body;

  const tpayPayload = data as TPayWebhookPayload;

  const paymentService = req.scope.resolve(Modules.PAYMENT);

  let relatedPayment: PaymentSessionDTO | null = null;

  try {
    if (!tpayPayload.tr_crc) {
      console.error("Missing tr_crc in TPay webhook payload:", tpayPayload);
      res.status(400).end("FALSE");
      return;
    }

    relatedPayment = await paymentService.retrievePaymentSession(
      tpayPayload.tr_crc
    );

    if (!relatedPayment) {
      console.error(
        "Payment session not found for tr_crc:",
        tpayPayload.tr_crc
      );
      res.status(404).end("FALSE");
      return;
    }
  } catch (error) {
    console.error("Error retrieving payment session:", error);
    res.status(500).end("FALSE");
    return;
  }

  const getTransactionResult = async (): Promise<WebhookActionResult> => {
    try {
      switch (tpayPayload.tr_status) {
        case "TRUE":
          return {
            action: "captured",
            data: {
              session_id: tpayPayload.tr_crc,
              amount: new BigNumber(relatedPayment.amount),
            },
          };
        case "FALSE":
          return {
            action: "failed",
            data: {
              session_id: tpayPayload.tr_crc,
              amount: new BigNumber(relatedPayment.amount),
            },
          };
        default:
          return {
            action: "not_supported",
          };
      }
    } catch (e) {
      console.log(e);
      return {
        action: "failed",
        data: {
          session_id: tpayPayload.tr_crc,
          amount: new BigNumber(relatedPayment.amount),
        },
      };
    }
  };

  try {
    const transactionResult = await getTransactionResult();
    
    console.log("Transaction result for workflow:", {
      action: transactionResult.action,
      session_id: transactionResult.data?.session_id,
      amount: transactionResult.data?.amount?.toString(),
      provider_id: config.TPAY_ID
    });

    const { result } = await processPaymentWorkflow(req.scope).run({
      input: {
        ...transactionResult,
        // @ts-ignore-next-line
        provider_id: config.TPAY_ID,
      },
      logOnError: true,
    });

    console.log("Payment workflow result:", result);

    // Send custom response to Tpay
    res.status(200).end("TRUE");
  } catch (error) {
    console.error("Error processing payment workflow:", error);
    console.error("TPay payload:", tpayPayload);
    console.error("Related payment:", relatedPayment);
    res.status(500).end("FALSE");
  }
}
