import {
  defineMiddlewares,
  MedusaRequest,
  MedusaResponse,
  MedusaNextFunction,
  authenticate,
} from "@medusajs/framework/http";
import { Modules, parseCorsOrigins } from "@medusajs/framework/utils";
import { ConfigModule } from "@medusajs/types";
import cors from "cors";
import { tourValidationBody } from "src/admin/components/tours/schemas";
import { ADMIN_EMAILS } from "src/lib/constants/adminEmails";

declare module "@medusajs/framework/http" {
  interface MedusaRequest {
    auth_context?: {
      actor_id?: string;
    };
  }
}

export default defineMiddlewares({
  routes: [
    {
      matcher: "/guides*",
      middlewares: [
        (req: MedusaRequest, res: MedusaResponse, next: MedusaNextFunction) => {
          const configModule: ConfigModule = req.scope.resolve("configModule");
          return cors({
            origin: parseCorsOrigins(configModule.projectConfig.http.storeCors),
            credentials: true,
          })(req, res, next);
        },
      ],
    },
    {
      method: "POST",
      matcher: "/admin/products",
      // @ts-expect-error
      additionalDataValidator: tourValidationBody,
    },
    {
      method: "GET",
      matcher: "/admin/report",
      middlewares: [
        authenticate("user", ["session", "bearer", "api-key"]),
        async (req, res, next) => {
          if (req.auth_context?.actor_id) {
            const userModuleService = req.scope.resolve(Modules.USER);
            const user = await userModuleService.retrieveUser(
              req.auth_context.actor_id
            );

            if (!ADMIN_EMAILS.includes(user.email)) {
              return res.status(403).json({
                message: "Brak dostępu dla tego adresu e-mail",
              });
            }
          }
          next();
        },
      ],
    },
  ],
});
