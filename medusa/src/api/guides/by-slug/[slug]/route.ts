import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { ContainerRegistrationKeys } from "@medusajs/framework/utils";

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const { slug } = req.params;
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

  const { data: guidePosts } = await query.graph({
    entity: "guide_post",
    fields: ["*"],
    filters: {
      slug: [slug],
    },
  });

  if (!guidePosts.length) {
    res
      .status(404)
      .json({ message: "Nie znaleziono poradnika dla podanego linku" });
  }

  res.status(200).json(guidePosts[0]);
}
