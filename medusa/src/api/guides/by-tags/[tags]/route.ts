import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { ContainerRegistrationKeys } from "@medusajs/framework/utils";

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const { tags } = req.params;
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

  const tagsArray = tags.includes(",") ? tags.split(",") : [tags];

  const { data: guidePosts } = await query.graph({
    entity: "guide_post",
    fields: ["*"],
    filters: {
      // @ts-expect-error - query does not seem to infer related entities
      tags: {
        $overlap: tagsArray,
      },
    },
  });

  if (!guidePosts.length) {
    res.status(404).json({ message: "Guide posts not found" });
    return;
  }

  res.status(200).json(guidePosts);
}
