import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { GUIDE_MODULE } from "src/modules/guides";
import GuideService from "src/modules/guides/service";

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const guideService: GuideService = req.scope.resolve(GUIDE_MODULE);

  const blog_post = await guideService.retrieveGuidePost(req.params.id);

  res.status(200).json(blog_post);
}

export async function PUT(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const targetId = req.params.id;

  const guideService: GuideService = req.scope.resolve(GUIDE_MODULE);

  const updatedGuidePost = await guideService.updateGuidePosts({
    id: targetId,
    ...(req.body as Record<string, unknown>),
  });

  res.status(200).json({ guide: updatedGuidePost });
}

export async function DELETE(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const guideService: GuideService = req.scope.resolve(GUIDE_MODULE);

  await guideService.deleteGuidePosts(req.params.id);

  res.status(200).json({ id: req.params.id });
}
