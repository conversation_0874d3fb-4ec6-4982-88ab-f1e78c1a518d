import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { GUIDE_MODULE } from "src/modules/guides";
import GuideService from "src/modules/guides/service";

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const q = req.query.q ?? "";
  const limit =
    typeof req.query.limit === "string" ? parseInt(req.query.limit, 10) : null;
  const guideService: GuideService = req.scope.resolve(GUIDE_MODULE);

  const blog_post = await guideService.listGuidePosts(
    {
      q,
      is_published: true,
    },
    {
      order: {
        position: "asc",
      },
      ...(limit && { take: limit }),
      select: ["name", "meta_description", "slug", "preview_image"],
    }
  );

  res.status(200).json(blog_post);
}

export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const guideService: GuideService = req.scope.resolve(GUIDE_MODULE);

  const guidePost = await guideService.createGuidePosts(req.body);

  res.status(201).json(guidePost);
}
