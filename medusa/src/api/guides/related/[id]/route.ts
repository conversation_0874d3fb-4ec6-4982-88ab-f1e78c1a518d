import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { ContainerRegistrationKeys } from "@medusajs/framework/utils";

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const { id } = req.params;
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

  // First, fetch the related guide IDs
  const { data: guideWithRelated } = await query.graph({
    entity: "guide_post",
    fields: ["related_guides"],
    filters: {
      id: {
        $eq: id,
      },
    },
  });

  if (!guideWithRelated.length) {
    res.status(404).json({ message: "Guide post not found" });
    return;
  }

  const relatedGuideIds = guideWithRelated[0].related_guides;

  // Then, fetch the related guides with the specified fields
  const { data: relatedGuides } = await query.graph({
    entity: "guide_post",
    fields: ["preview_image", "name", "slug", "meta_description"],
    filters: {
      id: {
        $in: relatedGuideIds,
      },
    },
  });

  res.status(200).json(relatedGuides);
}
