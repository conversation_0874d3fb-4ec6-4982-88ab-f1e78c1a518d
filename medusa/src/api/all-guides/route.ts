import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { GUIDE_MODULE } from "src/modules/guides";
import GuideService from "src/modules/guides/service";

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const q = req.query.q ?? "";
  const guideService: GuideService = req.scope.resolve(GUIDE_MODULE);

  const blog_post = await guideService.listGuidePosts(
    {
      q,
    },
    {
      select: [
        "position",
        "created_at",
        "name",
        "slug",
        "preview_image",
        "is_published",
      ],
      order: {
        position: "asc",
      },
    }
  );

  res.status(200).json(blog_post);
}
