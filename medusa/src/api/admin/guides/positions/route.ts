import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { GUIDE_MODULE } from "src/modules/guides";
import GuideService, {
  UpdateGuidePositionInput,
} from "src/modules/guides/service";

export async function PUT(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const guideService: GuideService = req.scope.resolve(GUIDE_MODULE);
  const { positions } = req.body as { positions: UpdateGuidePositionInput[] };

  await guideService.updateGuidePositions(positions);

  res.status(200).json({ message: "Guide positions updated successfully" });
}
