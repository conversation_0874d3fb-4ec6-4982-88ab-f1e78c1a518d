import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { generateTicketPDFWorkflow } from "src/workflows/generate-ticket-pdf-workflow";

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  const { order_id } = req.params;

  const payload = await req.body as {
    withPathSave?: boolean;
  };

  const { result: buffer } = await generateTicketPDFWorkflow.run({
    input: {
      order: {
        id: order_id,
      },
      withPathSave: payload.withPathSave,
    },
  });

  res.status(200).json({
    ticketBuffer: buffer.result,
  });
};
