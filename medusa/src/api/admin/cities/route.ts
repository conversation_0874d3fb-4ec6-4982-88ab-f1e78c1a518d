import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { ContainerRegistrationKeys } from "@medusajs/framework/utils";
import TourService from "src/modules/tours/service";
import { TOUR_MODULE } from "src/modules/tours";
import { TCreateCityFormBody } from "src/admin/components/tours/schemas";

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

  const { data: cities } = await query.graph({
    entity: "cities",
    fields: ["name", "id", "created_at"],
  });

  if (!cities.length) {
    res.status(404).json({ message: "Nie znaleziono miast" });
    return;
  }

  const sortCitiesByName = cities.sort((a, b) => {
    return a.name.localeCompare(b.name);
  });

  res.status(200).json(sortCitiesByName);
}

export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const data = req.body as TCreateCityFormBody;

  const tourService: TourService = req.scope.resolve(TOUR_MODULE);

  const result = await tourService.createCities(data);

  res.status(201).json({ message: "Miasto utworzone", result });
}
