import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { TCreateCityFormBody } from "src/admin/components/tours/schemas";
import { TOUR_MODULE } from "src/modules/tours";
import TourService from "src/modules/tours/service";

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const { id } = req.params;
  const tourService: TourService = req.scope.resolve(TOUR_MODULE);

  const result = await tourService.retrieveCity(id);

  res.status(200).json(result);
}

export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  const { id } = req.params;
  const data = req.body as TCreateCityFormBody;

  const tourService: TourService = req.scope.resolve(TOUR_MODULE);

  const result = await tourService.updateCities({
    id,
    ...data,
  });

  res.status(200).json({ message: "Miasto zaktualizowane", result });
}

export async function DELETE(req: MedusaRequest, res: MedusaResponse) {
  const { id } = req.params;
  const tourService: TourService = req.scope.resolve(TOUR_MODULE);

  await tourService.deleteCities(id);

  res.status(200).json({ message: "Miasto usunięte" });
}
