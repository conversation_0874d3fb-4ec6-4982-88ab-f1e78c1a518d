import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { MedusaError } from "@medusajs/framework/utils";
import { TCreateRedirectionFormBody } from "src/admin/components/redirection/schemas";
import { REDIRECTION_MODULE } from "src/modules/redirections";
import RedirectionModuleService from "src/modules/redirections/service";

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const { id } = req.params;

  if (!id) {
    throw new MedusaError(
      MedusaError.Types.INVALID_DATA,
      "ID przekierowania jest wymagane"
    );
  }

  const redirectionService: RedirectionModuleService =
    req.scope.resolve(REDIRECTION_MODULE);

  const result = await redirectionService.retrieveRedirection(id);

  if (!result) {
    throw new MedusaError(
      MedusaError.Types.NOT_FOUND,
      "Nie znaleziono przekierowania"
    );
  }

  res.status(200).json(result);
}

export async function PUT(
  req: MedusaRequest<TCreateRedirectionFormBody>,
  res: MedusaResponse
) {
  const { id } = req.params;
  const data = req.body;

  if (!id) {
    throw new MedusaError(
      MedusaError.Types.INVALID_DATA,
      "ID przekierowania jest wymagane"
    );
  }

  const redirectionService: RedirectionModuleService =
    req.scope.resolve(REDIRECTION_MODULE);

  const redirections = await redirectionService.listRedirections();

  const isRedirectionExisted = redirections?.find(
    (redirection) =>
      redirection?.from_path === data?.from_path && redirection?.id !== id
  );

  if (isRedirectionExisted) {
    throw new MedusaError(
      MedusaError.Types.NOT_ALLOWED,
      `Adres ${data?.from_path} istnieje już w bazie`
    );
  }

  const result = await redirectionService.updateRedirections({
    id,
    ...data,
    from_path: data?.from_path?.trim(),
    to_path: data?.to_path?.trim(),
  });

  res.status(200).json({ message: "Przekierowanie zaktualizowane", result });
}

export async function DELETE(req: MedusaRequest, res: MedusaResponse) {
  const { id } = req.params;

  if (!id) {
    throw new MedusaError(
      MedusaError.Types.INVALID_DATA,
      "ID przekierowania jest wymagane"
    );
  }

  const redirectionService: RedirectionModuleService =
    req.scope.resolve(REDIRECTION_MODULE);

  await redirectionService.deleteRedirections(id);

  res.status(200).json({ message: "Przekierowanie usunięte" });
}
