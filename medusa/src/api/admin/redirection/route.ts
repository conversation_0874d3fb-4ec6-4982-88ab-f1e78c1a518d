import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { MedusaError } from "@medusajs/framework/utils";
import { TCreateRedirectionFormBody } from "src/admin/components/redirection/schemas";
import { REDIRECTION_MODULE } from "src/modules/redirections";
import RedirectionModuleService from "src/modules/redirections/service";

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const redirectionService: RedirectionModuleService =
    req.scope.resolve(REDIRECTION_MODULE);

  const result = await redirectionService.listRedirections();

  if (result.length < 0) {
    throw new MedusaError(
      MedusaError.Types.NOT_FOUND,
      "Nie znaleziono przekierowań"
    );
  }

  res.status(200).json(result);
}

export async function POST(
  req: MedusaRequest<TCreateRedirectionFormBody>,
  res: MedusaResponse
) {
  const data = req.body;

  const redirectionService: RedirectionModuleService =
    req.scope.resolve(REDIRECTION_MODULE);

  const redirections = await redirectionService.listRedirections();

  const isRedirectionExisted = redirections?.find(
    (redirection) => redirection?.from_path === data?.from_path
  );

  if (isRedirectionExisted) {
    throw new MedusaError(
      MedusaError.Types.NOT_ALLOWED,
      `Adres ${data?.from_path} istnieje już w bazie`
    );
  }

  const result = await redirectionService.createRedirections({
    ...data,
    from_path: data?.from_path?.trim(),
    to_path: data?.to_path?.trim(),
  });

  res.status(200).json({ message: "Przekierowanie utworzone", result });
}
