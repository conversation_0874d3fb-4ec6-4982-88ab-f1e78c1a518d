import { logger, MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { CATEGORY_MODULE } from "src/modules/categories";
import CategoryService from "src/modules/categories/service";

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  const payload = req.body as {
    updates: {
      id: string;
      rank: number;
    }[];
  };

  const categoryService: CategoryService = req.scope.resolve(CATEGORY_MODULE);

  await categoryService.updateCategoryPositions(payload.updates);

  res
    .status(201)
    .json({ message: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kategorii została zaktualizowana " });
};
