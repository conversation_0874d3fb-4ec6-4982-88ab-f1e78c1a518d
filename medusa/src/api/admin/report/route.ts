import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { generateReportWorkflow } from "src/workflows/generate-report-workflow";
import { SalesReportInput } from "src/workflows/generate-report-workflow/steps/generate-report";

export const GET = async (
  req: MedusaRequest<unknown, SalesReportInput>,
  res: MedusaResponse
) => {
  const { dateFrom, dateTo } = req.query;

  if (!dateFrom || !dateTo) {
    return res.status(400).json({ error: "dateFrom and dateTo are required" });
  }

  try {
    const input: SalesReportInput = {
      dateFrom: dateFrom as string,
      dateTo: dateTo as string,
    };
    const { result } = await generateReportWorkflow().run({ input });
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};
