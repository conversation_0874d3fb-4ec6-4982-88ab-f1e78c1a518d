import { container } from "@medusajs/framework";
import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { ContainerRegistrationKeys } from "@medusajs/framework/utils";
import { IssueRefundDTO } from "src/types";
import { updatePaymentCollectionAfterOrderLineItemChangeWorkflow } from "src/workflows/update-payment-collection-after-order-line-item-change-workflow";

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const { id } = req.params;

  const payload = req.body as IssueRefundDTO;

  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

  const { data } = await query.graph({
    entity: "order",
    fields: ["*", "ticket.id", "summary.*", "payment_collections.*"],
    filters: {
      id,
    },
  });

  const { data: paymentData } = await query.graph({
    entity: "payment",
    fields: ["*"],
    filters: {
      payment_collection_id: data.at(0)?.payment_collections?.[0]?.id,
    },
  });

  const order = data.at(0);

  if (!order?.ticket?.id) {
    throw new Error("Ticket not found");
  }

  // const prepaidPercentage = paymentData.at(0)?.data?.prepaidPercentage;

  // const orderSummaryDifference = getOrderSummaryDifference(
  //   OrderChange.at(0)?.order?.summary ?? []
  // );

  const result =
    await updatePaymentCollectionAfterOrderLineItemChangeWorkflow.run({
      container: container,
      input: {
        payment_id: paymentData.at(0)?.id,
        calculated_refund_amount: payload.amount,
        order_id: id,
      },
      logOnError: true,
    });

  res.status(200).json({
    message: "Order updated successfully",
    ticket_id: order?.ticket?.id,
  });
}
