import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { ContainerRegistrationKeys } from "@medusajs/framework/utils";
import { TICKET_MODULE } from "src/modules/ticket";
import TicketService from "src/modules/ticket/service";
import { TicketDTO } from "src/modules/ticket/types";

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const { id } = req.params;

  const payload = (await req.body) as Partial<TicketDTO>;

  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

  const ticketService: TicketService = req.scope.resolve(TICKET_MODULE);

  const { data } = await query.graph({
    entity: "order",
    fields: ["*", "ticket.id"],
    filters: {
      id,
    },
  });

  const order = data.at(0);

  if (!order?.ticket?.id) {
    throw new Error("Ticket not found");
  }

  const updateTicketDTO: Partial<TicketDTO> = {
    id: order?.ticket?.id,
    ...payload,
  };

  const result = await ticketService.updateTickets(updateTicketDTO);

  res.status(200).json({
    message: "Ticket updated successfully",
    ticket_id: order?.ticket?.id,
    result,
  });
}
