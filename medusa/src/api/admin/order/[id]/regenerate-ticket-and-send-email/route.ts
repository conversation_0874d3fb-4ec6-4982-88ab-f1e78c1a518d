import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { logger } from "@medusajs/framework/logger";
import { ContainerRegistrationKeys, Modules } from "@medusajs/framework/utils";
import {
  ExtendedNotificationDTO
} from "src/modules/smtp/types";

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const { id } = req.params;

  const payload = (await req.body) as { updateReason?: string };

  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

  const { data } = await query.graph({
    entity: "order",
    fields: [
      "*",
      "ticket.id",
      "ticket.attraction_name",
      "summary.*",
      "payment_collections.*",
    ],
    filters: {
      id,
    },
  });

  const order = data.at(0);

  if (!order?.ticket?.id) {
    throw new Error("Ticket not found");
  }

  const notificationModuleService = req.scope.resolve(Modules.NOTIFICATION);

  try {
    await notificationModuleService.createNotifications({
      to: order.email!,
      channel: "email",
      template: "order-ticket-updated",
      resource_type: "order-ticket-updated",
      data: {
        order_id: order.id,
        customer_id: order.customer_id!,
        reservation_number: order.display_id.toString(),
        attraction_name: order.ticket?.attraction_name ?? "",
        update_reason: payload.updateReason ?? "",
      },
    } satisfies ExtendedNotificationDTO);
  } catch (error) {
    logger.error(
      `Failed to send notification for order ${order.id}: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    );
    // Re-throw the error to ensure the event bus knows this handler failed
    throw error;
  }

  res.status(200).json({
    message: "Email z aktualizacją biletu wysłany",
    ticket_id: order?.ticket?.id,
  });
}
