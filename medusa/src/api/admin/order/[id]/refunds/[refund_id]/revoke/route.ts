import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { REFUNDS_MODULE } from "src/modules/refunds";

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const { id, refund_id } = req.params;

  try {
    const refundsService = req.scope.resolve(REFUNDS_MODULE);

    const result = await refundsService.cancelRefund({
      orderId: id,
      refundId: refund_id,
      scope: req.scope,
    });

    if (result.success) {
      res.status(200).json({
        message: result.message,
        debug: {
          originalRefund: result.originalRefund,
          revertedAmount: result.revertedAmount,
          summaryUpdates: result.summaryUpdates,
        },
      });
    } else {
      res.status(400).json({
        error: result.error,
        message: result.message,
      });
    }
  } catch (error: any) {
    console.error("Error revoking refund:", error);
    res.status(500).json({
      error: "Failed to revoke refund",
      details: error.message,
    });
  }
}
