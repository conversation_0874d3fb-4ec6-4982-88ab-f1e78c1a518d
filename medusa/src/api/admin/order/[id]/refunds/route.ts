import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { REFUNDS_MODULE } from "src/modules/refunds";
import RefundsService from "src/modules/refunds/service";

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const { id } = req.params;

  try {
    const refundsService: RefundsService = req.scope.resolve(REFUNDS_MODULE);

    const result = await refundsService.retrieveRefunds({
      orderId: id,
    });

    res.status(200).json(result);
  } catch (error: any) {
    console.error("Error retrieving refunds:", error);
    res.status(500).json({
      error: "Failed to retrieve refunds",
      details: error.message,
    });
  }
}
