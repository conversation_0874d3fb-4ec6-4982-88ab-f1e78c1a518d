import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import {
  ContainerRegistrationKeys,
  Modules,
  OrderStatus,
} from "@medusajs/framework/utils";
import { IEventBusModuleService, IOrderModuleService } from "@medusajs/types";

import { OrderStatusDTO } from "../../../../../admin/widgets/order-status/types";
import { NotificationEventDataDTO } from "src/modules/smtp/types";

export const OrderStatusMap: Record<
  OrderStatusDTO["order_status"],
  OrderStatus
> = {
  new_order: OrderStatus.DRAFT,
  canceled: OrderStatus.CANCELED,
  during_realization: OrderStatus.PENDING,
  refunded: OrderStatus.REQUIRES_ACTION,
  feedback_requested: OrderStatus.COMPLETED,
  completed: OrderStatus.COMPLETED,
  unpaid: OrderStatus.DRAFT,
  deleted: OrderStatus.CANCELED,
};

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const { id } = req.params;

  const payload = (await req.body) as OrderStatusDTO;

  const orderService: IOrderModuleService = req.scope.resolve(Modules.ORDER);

  const order = await orderService.retrieveOrder(id);

  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

  const { data } = await query.graph({
    entity: "order",
    fields: ["ticket.attraction_name"],
    filters: {
      id: {
        $eq: id,
      },
    },
  });

  const targetOrder = data.at(0);

  const targetTour = targetOrder?.ticket?.attraction_name;

  if (!order) {
    throw new Error("Order not found");
  }

  try {
    await orderService.updateOrders([
      {
        id: id,
        status: OrderStatusMap[payload.order_status],
        metadata: {
          ...order.metadata,
          status: payload.order_status,
        },
      },
    ]);
  } catch (error) {
    throw new Error(`Failed to update order status - ${JSON.stringify(error)}`);
  }

  const emitter: IEventBusModuleService = req.scope.resolve(Modules.EVENT_BUS);

  try {
    emitter.emit({
      name: "order.status.updated",
      data: {
        id,
        status: OrderStatusMap[payload.order_status],
        metadata_status: payload.order_status,
        email_to: order.email,
        data: {
          customer_id: order.customer_id,
          reservation_number: order.display_id.toString(),
          attraction_name: targetTour,
          order_id: id,
        } as NotificationEventDataDTO,
      },
    });
  } catch (error) {
    console.error(
      `Failed to emit order status updated event - ${JSON.stringify(error)}`
    );
  }

  res.status(200).json({
    message:
      "Status zamówienia został zaktualizowany pomyślnie - trwa wysyłanie powiadomień",
  });
}
