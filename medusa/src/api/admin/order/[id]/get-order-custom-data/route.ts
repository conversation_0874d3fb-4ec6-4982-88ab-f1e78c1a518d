import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { IOrderModuleService } from "@medusajs/types";
import { Modules } from "@medusajs/framework/utils";
import { OrderWithMetadata } from "src/admin/types";

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const { id } = req.params;

  const orderService: IOrderModuleService = req.scope.resolve(Modules.ORDER);

  const order = await orderService.retrieveOrder(id);

  const orderMetadata = order.metadata as OrderWithMetadata["metadata"];

  const result = {
    customStatus: orderMetadata?.status,
    additionalCustomerNote: orderMetadata?.additional_customer_note,
    euroRate: orderMetadata?.euro_rate,
    prepaidPercentage: orderMetadata?.prepaid_percentage,
  };

  res.status(200).json({ ...result });
}
