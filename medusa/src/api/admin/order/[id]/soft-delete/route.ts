import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http";
import { Modules } from "@medusajs/framework/utils";
import { OrderWithMetadata } from "src/admin/types";

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const { id } = req.params;

  const orderService = req.scope.resolve(Modules.ORDER);

  const order = await orderService.retrieveOrder(id);

  await orderService.updateOrders([
    {
      id,
      deleted_at: new Date(),
      status: "canceled",
      metadata: {
        ...(order.metadata as OrderWithMetadata["metadata"]),
        status: "deleted",
      },
    } satisfies Partial<OrderWithMetadata>,
  ]);

  res.status(200).json({
    message: "Order deleted successfully",
  });
}
