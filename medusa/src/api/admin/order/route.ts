import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { ContainerRegistrationKeys } from "@medusajs/framework/utils";
import { OrderFilters } from "src/admin/hooks/use-order-filtering";
import { formatDate } from "src/admin/lib/util/string-dates";
import { normalizeText } from "src/admin/lib/utils/fuzzy-search";
import { OrderWithMetadata, OrderWithRelations } from "src/admin/types";
import { getDatePart } from "src/lib/utils";
import { EnhancedOrder } from "src/types";

// Enhanced attraction name matching function
const createAttractionNameMatcher = (searchQuery: string) => {
  if (!searchQuery || searchQuery.trim() === "") {
    return () => true;
  }

  const queryWords = searchQuery
    .toLowerCase()
    .trim()
    .split(/\s+/)
    .filter((word) => word.length > 0);

  return (attractionName: string) => {
    if (!attractionName) return false;

    const attractionWords = attractionName
      .toLowerCase()
      .split(/\s+/)
      .filter((word) => word.length > 0);

    // Check if all query words have a match in attraction name
    return queryWords.every((queryWord) =>
      attractionWords.some((attractionWord) =>
        attractionWord.includes(queryWord)
      )
    );
  };
};

// List of fields that can be directly sorted by the ORM
const NATIVE_SORT_FIELDS = ["id", "display_id", "created_at", "updated_at"];

// Cache to store fetched orders to avoid redundant database queries
// This is a simple in-memory cache - for production, consider using Redis or another distributed cache
interface OrderCache {
  data: any[]; // Use any[] instead of Order[] since we don't have the specific type
  timestamp: number;
  hash: string;
}

// Cache will expire after 3 minutes
const CACHE_TTL = 3 * 60 * 1000;
let orderCache: OrderCache | null = null;

// Generate a hash from the request's native filters to determine cache validity
const generateFilterHash = (nativeFilters: Record<string, any>): string => {
  return JSON.stringify(nativeFilters);
};

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

  const limit = Number(req.query.limit) || 15;
  const offset = Number(req.query.offset) || 0;

  // Parse filters from query parameters
  const filters = req.query.filters
    ? (JSON.parse(req.query.filters as string) as OrderFilters)
    : {};

  // Parse order parameters
  const requestedOrderBy = (req.query.orderBy as string) || "created_at";
  const orderDirection = (req.query.orderDirection as "ASC" | "DESC") || "DESC";

  // Check if we can use native sorting
  const canUseNativeSorting = NATIVE_SORT_FIELDS.includes(requestedOrderBy);

  // Set up order parameter for the query - use requested sort field only if it's a native field
  // Otherwise default to created_at for initial data retrieval
  const orderParam = {
    [canUseNativeSorting ? requestedOrderBy : "created_at"]: orderDirection,
  };

  // Check if we only have display_id filter or no filters
  // Only in these cases we can use the native filtering
  const hasOnlyNativeFilters =
    Object.keys(filters).length === 0 ||
    (Object.keys(filters).length === 1 && "display_id" in filters);

  // If we have only native filters AND native sorting, we can use the native query directly
  if (hasOnlyNativeFilters && canUseNativeSorting) {
    // Use native filtering & sorting - it's efficient with pagination
    const nativeFilters: Record<string, any> = {};

    // Only add display_id if it exists in filters
    if ("display_id" in filters) {
      nativeFilters.display_id = filters.display_id;
    }
    // Exclude soft-deleted orders
    nativeFilters.deleted_at = null;

    const orders = await query.graph({
      entity: "order",
      fields: [
        "items.quantity",
        "customer.first_name",
        "customer.last_name",
        "ticket.attraction_name",
        "ticket.selected_date",
        "summary.transaction_amount",
        "*",
      ],
      pagination: {
        take: limit,
        skip: offset,
        order: orderParam,
      },
      filters: nativeFilters,
    });

    // Calculate PLN transaction amount for each order
    const ordersWithPLNAmount = orders.data.map((order) => {
      const typedOrder = order as unknown as OrderWithMetadata;
      return {
        ...order,
        calculated_pln_amount:
          typedOrder.summary?.transaction_total &&
          typedOrder.metadata?.euro_rate
            ? parseFloat(
                (
                  typedOrder.summary.transaction_total *
                  typedOrder.metadata.euro_rate
                ).toFixed(2)
              )
            : null,
      };
    });

    return res.json({
      orders: ordersWithPLNAmount,
      limit,
      offset,
      count: orders?.metadata?.count,
    });
  } else {
    // We need to handle:
    // 1. Custom filters with any sorting, OR
    // 2. Native filters with custom sorting

    // Get the base query with any native filters
    const nativeFilters: Record<string, any> = {};
    if ("display_id" in filters && filters.display_id) {
      nativeFilters.display_id = filters.display_id;
    }
    // Exclude soft-deleted orders when fetching all data for filtering/sorting
    nativeFilters.deleted_at = null;

    // Generate a hash for the current native filters
    const filterHash = generateFilterHash(nativeFilters);

    // Check if we have a valid cache that matches our current filters
    let allOrders;
    const now = Date.now();

    if (
      orderCache &&
      orderCache.hash === filterHash &&
      now - orderCache.timestamp < CACHE_TTL
    ) {
      // Use cached orders if available and not expired
      allOrders = { data: orderCache.data };
      console.log("Using cached orders data");
    } else {
      // Fetch orders from the database
      console.log("Fetching fresh orders data from database");
      allOrders = await query.graph({
        entity: "order",
        fields: [
          "customer.first_name",
          "customer.last_name",
          "ticket.attraction_name",
          "ticket.selected_date",
          "summary.transaction_amount",
          "items.quantity",
          "*",
        ],
        pagination: {
          skip: 0, // Start from the beginning
          order: orderParam, // Use default sorting when fetching all data
        },
        filters: nativeFilters,
      });

      // Update the cache
      orderCache = {
        data: allOrders.data,
        timestamp: now,
        hash: filterHash,
      };
    }

    let filteredOrders = [...allOrders.data];

    // Apply transformation to enhance orders with calculated PLN amount
    const enhancedOrders = filteredOrders.map((order) => {
      const typedOrder = order as unknown as OrderWithMetadata;
      return {
        ...order,
        calculated_pln_amount:
          typedOrder.summary?.transaction_total &&
          typedOrder.metadata?.euro_rate
            ? parseFloat(
                (
                  typedOrder.summary.transaction_total *
                  typedOrder.metadata.euro_rate
                ).toFixed(2)
              )
            : null,
      } as unknown as EnhancedOrder;
    });

    filteredOrders = enhancedOrders;

    // Apply custom filters manually
    if (filteredOrders.length > 0) {
      // Customer name filter - searches in both first_name and last_name
      if ("customer.name" in filters && filters["customer.name"]) {
        const nameSearchTerm = String(filters["customer.name"]).toLowerCase();
        filteredOrders = filteredOrders.filter((order) => {
          const firstName =
            typeof order.customer?.first_name === "string"
              ? order.customer.first_name.toLowerCase()
              : "";
          const lastName =
            typeof order.customer?.last_name === "string"
              ? order.customer.last_name.toLowerCase()
              : "";
          const fullName = `${firstName} ${lastName}`.trim();

          // Match if the search term is found in first name, last name, or the full name
          return (
            firstName.includes(nameSearchTerm) ||
            lastName.includes(nameSearchTerm) ||
            fullName.includes(nameSearchTerm)
          );
        });
      }

      // Backward compatibility for customer.first_name filter
      else if (
        "customer.first_name" in filters &&
        filters["customer.first_name"]
      ) {
        const nameSearchTerm = String(
          filters["customer.first_name"]
        ).toLowerCase();
        filteredOrders = filteredOrders.filter((order) => {
          if (!order.customer?.first_name) return false;
          return order.customer.first_name
            .toLowerCase()
            .includes(nameSearchTerm);
        });
      }

      // Status filter
      if ("metadata.status" in filters && filters["metadata.status"]) {
        filteredOrders = filteredOrders.filter(
          (order) => order.metadata?.status === filters["metadata.status"]
        );
      }

      // Enhanced attraction name filter
      if (
        "ticket.attraction_name" in filters &&
        filters["ticket.attraction_name"]
      ) {
        const attractionMatcher = createAttractionNameMatcher(
          filters["ticket.attraction_name"]
        );
        filteredOrders = filteredOrders.filter((order) =>
          attractionMatcher(order.ticket?.attraction_name || "")
        );
      }

      // Selected date filter - using date string comparison to avoid timezone issues
      if (
        "ticket.selected_date" in filters &&
        filters["ticket.selected_date"]
      ) {
        const normalizedFilterDate = getDatePart(
          filters["ticket.selected_date"]
        );

        filteredOrders = filteredOrders.filter((order) => {
          if (!order.ticket?.selected_date) return false;
          const normalizedOrderDate = getDatePart(order.ticket.selected_date);
          return normalizedOrderDate === normalizedFilterDate;
        });
      }

      // Date range filters with string-based date comparison
      if (
        "ticket.selected_date_from" in filters &&
        filters["ticket.selected_date_from"]
      ) {
        const normalizedFromDate = getDatePart(
          filters["ticket.selected_date_from"]
        );

        filteredOrders = filteredOrders.filter((order) => {
          if (!order.ticket?.selected_date) return false;
          const normalizedOrderDate = getDatePart(order.ticket.selected_date);
          return normalizedOrderDate >= normalizedFromDate;
        });
      }

      if (
        "ticket.selected_date_to" in filters &&
        filters["ticket.selected_date_to"]
      ) {
        const normalizedToDate = getDatePart(
          filters["ticket.selected_date_to"]
        );

        filteredOrders = filteredOrders.filter((order) => {
          if (!order.ticket?.selected_date) return false;
          const normalizedOrderDate = getDatePart(order.ticket.selected_date);
          return normalizedOrderDate <= normalizedToDate;
        });
      }
    }

    // Apply manual sorting - this happens for both:
    // - Native filters + non-native sorting
    // - Non-native filters + any sorting (including native ones)
    // This ensures consistent ordering in all cases
    filteredOrders.sort((a, b) => {
      // Helper function to get nested property value
      const getNestedValue = (obj: any, path: string) => {
        const keys = path.split(".");
        let value = obj;
        for (const key of keys) {
          if (value === null || value === undefined) return undefined;
          value = value[key];
        }
        return value;
      };

      // Special case for PLN amount sorting
      if (requestedOrderBy === "calculated_pln_amount") {
        const aTyped = a as unknown as EnhancedOrder;
        const bTyped = b as unknown as EnhancedOrder;
        const aValue = aTyped.calculated_pln_amount;
        const bValue = bTyped.calculated_pln_amount;

        // Handle undefined values
        if (aValue === undefined && bValue === undefined) return 0;
        if (aValue === undefined || aValue === null)
          return orderDirection === "ASC" ? -1 : 1;
        if (bValue === undefined || bValue === null)
          return orderDirection === "ASC" ? 1 : -1;

        return orderDirection === "ASC" ? aValue - bValue : bValue - aValue;
      }

      // Special case for participant count sorting
      if (requestedOrderBy === "items.quantity") {
        const getParticipantCount = (order: OrderWithRelations) => {
          return (
            order.items?.reduce(
              (acc: number, item: any) => acc + (item?.quantity || 0),
              0
            ) || 0
          );
        };

        const aCount = getParticipantCount(a);
        const bCount = getParticipantCount(b);

        return orderDirection === "ASC" ? aCount - bCount : bCount - aCount;
      }

      const aValue = getNestedValue(a, requestedOrderBy);
      const bValue = getNestedValue(b, requestedOrderBy);

      // Handle undefined values
      if (aValue === undefined && bValue === undefined) return 0;
      if (aValue === undefined) return orderDirection === "ASC" ? -1 : 1;
      if (bValue === undefined) return orderDirection === "ASC" ? 1 : -1;

      // Special handling for dates in sorting
      if (
        aValue instanceof Date ||
        (typeof aValue === "string" && !isNaN(Date.parse(aValue)))
      ) {
        const dateA = aValue instanceof Date ? aValue : new Date(aValue);
        const dateB = bValue instanceof Date ? bValue : new Date(bValue);

        return orderDirection === "ASC"
          ? dateA.getTime() - dateB.getTime()
          : dateB.getTime() - dateA.getTime();
      }
      // Compare values for non-dates
      else if (typeof aValue === "string") {
        return orderDirection === "ASC"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      } else {
        return orderDirection === "ASC"
          ? aValue > bValue
            ? 1
            : -1
          : aValue > bValue
          ? -1
          : 1;
      }
    });

    // Apply pagination manually
    const paginatedOrders = filteredOrders.slice(offset, offset + limit);

    return res.json({
      orders: paginatedOrders,
      limit,
      offset,
      count: filteredOrders.length,
    });
  }
};
