import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { ContainerRegistrationKeys } from "@medusajs/framework/utils";
import { TOUR_MODULE } from "src/modules/tours";
import TourService from "src/modules/tours/service";

type UpdatePositionPayload = {
  id: string;
  position: number;
};

export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  const { positions } = req.body as { positions: UpdatePositionPayload[] };
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

  const tourService: TourService = req.scope.resolve(TOUR_MODULE);

  try {
    await tourService.updateTourPositions(positions);
    res.status(200).json({ message: "Tour positions updated successfully" });
  } catch (error) {
    console.error("Error updating tour positions:", error);
    res.status(500).json({ message: "Error updating tour positions" });
  }
}
