import { MedusaResponse, MedusaRequest } from "@medusajs/framework";

import { ContainerRegistrationKeys } from "@medusajs/framework/utils";

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

  const { data: tours } = await query.graph({
    entity: "tours",
    fields: ["id", "name", "featured_image"],
  });

  res.json({ tours });
}
