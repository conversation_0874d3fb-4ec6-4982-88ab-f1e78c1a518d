import { MedusaRequest } from "@medusajs/framework/http";

import { MedusaResponse } from "@medusajs/framework/http";
import { ContainerRegistrationKeys } from "@medusajs/framework/utils";

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const { id } = req.params;
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

  const { data: start_places } = await query.graph({
    entity: "tour_available_start_places",
    fields: ["*"],
    filters: {
      tour_id: {
        $eq: id,
      },
    },
  });

  const { data: tours } = await query.graph({
    entity: "tours",
    fields: [
      "*",
      "product.categories.*",
      "product.variants.*",
      "cities.*",
      "product.*",
      "start_times.*",
      "start_places_by_date.*",
      "blocked_dates.*",
      "food_options.*",
      "content_blocks.*",
      "pricing.*",
      "ticket.*",
      "content_blocks.faq.*",
      "blocked_dates_by_month.*",
      "tour_seo.*",
      "product.variants.prices.*",
    ],
    filters: {
      id: {
        $eq: id,
      },
    },
  });

  const tour = {
    ...tours[0],
    available_start_places: start_places?.sort(
      (a, b) => (a?.position ?? 0) - (b?.position ?? 0)
    ),
    blocked_dates_by_month: [],
  };

  if (!tours || tours.length === 0) {
    return res.status(404).json({ message: "Tour not found" });
  }

  res.json({ tour });
}
