import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { ContainerRegistrationKeys } from "@medusajs/framework/utils";
import {
  deleteProductsWorkflow,
  updateProductsWorkflow,
} from "@medusajs/medusa/core-flows";
import { AdminCreateProduct, InferTypeOf } from "@medusajs/types";

import { TCreateTourFormBody } from "src/admin/components/tours/schemas";
import { Tour } from "src/modules/tours/models/tour";

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const { id } = req.params;
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

  const { data: start_places } = await query.graph({
    entity: "tour_available_start_places",
    fields: ["*"],
    filters: {
      tour_id: {
        $eq: id,
      },
    },
  });

  const { data: blocked_dates } = await query.graph({
    entity: "tour_blocked_dates",
    fields: ["*"],
    filters: {
      tour_id: { $eq: id },
    },
  });

  const { data: start_places_by_date } = await query.graph({
    entity: "tour_start_places_by_date",
    fields: ["*"],
    filters: {
      tour_id: {
        $eq: id,
      },
    },
  });

  const { data: blocked_dates_by_month } = await query.graph({
    entity: "tour_blocked_dates_by_month",
    fields: ["*"],
    filters: {
      tour_id: { $eq: id },
    },
  });

  const { data: blocked_start_times } = await query.graph({
    entity: "tour_blocked_start_times_by_date",
    fields: ["*"],
    filters: {
      tour_id: { $eq: id },
    },
  });

  const { data: tours } = await query.graph({
    entity: "tours",
    fields: [
      "*",
      "product.categories.*",
      "product.variants.*",
      "cities.*",
      "product.*",
      "start_times.*",
      "food_options.*",
      "content_blocks.*",
      "pricing.*",
      "ticket.*",
      "content_blocks.faq.*",
      "tour_seo.*",
      "product.variants.prices.*",
    ],
    filters: {
      id: {
        $eq: id,
      },
    },
  });

  if (!tours || tours.length === 0) {
    return res.status(404).json({ message: "Tour not found" });
  }

  const tourWithSortedFaqs: InferTypeOf<typeof Tour> = {
    ...tours[0],
    content_blocks: {
      ...tours[0].content_blocks,
      faq: tours[0].content_blocks.faq.sort(
        (a, b) => (a?.position ?? 0) - (b?.position ?? 0)
      ),
    },
    available_start_places: start_places?.sort(
      (a, b) => (a?.position ?? 0) - (b?.position ?? 0)
    ),
    start_places_by_date: start_places_by_date,
    blocked_dates: blocked_dates,
    blocked_dates_by_month: blocked_dates_by_month,
    blocked_start_times: blocked_start_times,
  };

  res.json({ tour: tourWithSortedFaqs });
}

export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  const { id } = req.params;
  const { data } = req.body as {
    data: AdminCreateProduct & {
      additional_data: TCreateTourFormBody & { tour_id: string };
    };
  };

  const tour = await updateProductsWorkflow(req.scope).run({
    input: {
      products: [
        {
          id,
          ...data,
        },
      ],
    },
  });

  res.json({ tour });
}

export async function DELETE(req: MedusaRequest, res: MedusaResponse) {
  const { id } = req.params;

  await deleteProductsWorkflow(req.scope).run({
    input: {
      ids: [id],
    },
  });

  res.status(204).send();
}
