import { MedusaRequest, MedusaResponse } from "@medusajs/framework";
import { ContainerRegistrationKeys } from "@medusajs/framework/utils";
import { TCreateTourFormBody } from "src/admin/components/tours/schemas";
import { AdminCreateProduct } from "@medusajs/types";
import { createProductsWorkflow } from "@medusajs/medusa/core-flows";

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

  const { data: tours } = await query.graph({
    entity: "tours",
    fields: [
      "ticket.*",
      "content_blocks.*",
      "product.*",
      "product.id",
      "created_at",
      "updated_at",
      "featured_image",
      "is_active",
      "name",
      "slug",
      "position",
    ],
  });

  const orderedTours = tours.sort((a, b) => a.position - b.position);

  res.json({ tours: orderedTours });
}

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const { data } = req.body as {
    data: AdminCreateProduct & { additional_data: TCreateTourFormBody };
  };

  const product = await createProductsWorkflow(req.scope).run({
    input: {
      products: [
        {
          ...data,
        },
      ],
    },
  });

  res.json({ product });
}
