import axios, { AxiosInstance } from "axios";

// Types
export interface TpayConfig {
  clientId: string;
  clientSecret: string;
  apiUrl?: string;
  isSandbox?: boolean;
}

export interface TpayAuthResponse {
  issued_at: number;
  scope: string;
  token_type: string;
  expires_in: number;
  client_id: string;
  access_token: string;
}

export interface TpayPayer {
  email: string;
  name: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  postalCode?: string;
}

export interface TpayCallbacks {
  payerUrls: {
    // Address to redirect used when payment was successful.
    success: string | null;
    // Address to redirect used when payment failed.
    error: string | null;
  };
  notification: {
    /*
    Result url for POST system notification
    */
    url: string | null;
    /* 
   Merchant email addresses used to send notification when payment is finished.
   */
    email: string | null;
  };
}

export interface CreateTransactionRequest {
  amount: number;
  description: string;
  payer: TpayPayer;
  hiddenDescription?: string;
  callbacks?: TpayCallbacks;
  additionalDescription: string;
}

export interface TransactionPaymentDetails {
  status: string;
  method: string | null;
  amountPaid: number;
  date: {
    realization: string | null;
  };
}

export interface TransactionResponse {
  result: string;
  requestId: string;
  transactionId: string;
  title: string;
  posId: string;
  status: string;
  date: {
    creation: string;
    realization: string | null;
  };
  amount: number;
  currency: string;
  description: string;
  hiddenDescription: string;
  payer: TpayPayer;
  payments: TransactionPaymentDetails;
  transactionPaymentUrl: string;
}

export class TpayClient {
  private readonly config: TpayConfig;
  private axiosInstance: AxiosInstance;
  private accessToken: string | null = null;
  private tokenExpiresAt: number | null = null;
  private isSandbox: boolean;

  constructor(config: TpayConfig) {
    this.isSandbox = config.isSandbox ?? false;
    this.config = {
      apiUrl: this.isSandbox
        ? "https://openapi.sandbox.tpay.com"
        : "https://api.tpay.com",
      ...config,
    };

    this.axiosInstance = axios.create({
      baseURL: this.config.apiUrl,
      headers: {
        "Content-Type": "application/json",
      },
    });

    // Add response interceptor for handling errors
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response) {
          throw new Error(
            `Tpay API error: ${error.response.status} - ${JSON.stringify(
              error.response.data
            )}`
          );
        }
        throw error;
      }
    );
  }

  private async ensureValidToken(): Promise<string> {
    if (
      !this.accessToken ||
      !this.tokenExpiresAt ||
      Date.now() >= this.tokenExpiresAt
    ) {
      await this.authenticate();
    }
    return this.accessToken!;
  }

  private async authenticate(): Promise<void> {
    try {
      const response = await this.axiosInstance.post<TpayAuthResponse>(
        "/oauth/auth",
        new URLSearchParams({
          client_id: this.config.clientId,
          client_secret: this.config.clientSecret,
        }),
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );
      console.log({ response });

      this.accessToken = response.data.access_token;
      // Set token expiration time with 5-minute buffer
      this.tokenExpiresAt =
        (response.data.issued_at + response.data.expires_in - 300) * 1000;

      // Update axios instance headers with the new token
      this.axiosInstance.defaults.headers.common[
        "Authorization"
      ] = `Bearer ${this.accessToken}`;
    } catch (error) {
      throw new Error(`Failed to authenticate with Tpay API: ${error}`);
    }
  }

  async createTransaction(
    data: CreateTransactionRequest
  ): Promise<TransactionResponse> {
    await this.ensureValidToken();

    try {
      const response = await this.axiosInstance.post<TransactionResponse>(
        "/transactions",
        data
      );
      return response.data;
    } catch (error) {
      throw new Error(`Failed to create transaction: ${error}`);
    }
  }

  async getTransaction(transactionId: string): Promise<TransactionResponse> {
    await this.ensureValidToken();

    try {
      const response = await this.axiosInstance.get<TransactionResponse>(
        `/transactions/${transactionId}`
      );
      return response.data;
    } catch (error) {
      throw new Error(
        `Failed to get transaction details for ID: ${transactionId}`
      );
    }
  }

  async cancelTransaction(transactionId: string) {
    await this.ensureValidToken();

    try {
      // First, check if the transaction is in pending state
      const transaction = await this.getTransaction(transactionId);

      if (transaction.status !== "pending") {
        throw new Error(
          `Transaction ${transactionId} cannot be cancelled - status is ${transaction.status}`
        );
      }

      const response = await this.axiosInstance.post(
        `/transactions/${transactionId}/cancel`
      );
      return response.data;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(`Failed to cancel transaction: ${error}`);
    }
  }

  async refundTransaction(
    transactionId: string,
    amount?: number
  ): Promise<TransactionResponse> {
    await this.ensureValidToken();

    try {
      const response = await this.axiosInstance.post<TransactionResponse>(
        `/transactions/${transactionId}/refunds`,
        {
          amount: Number(amount),
        }
      );
      return response.data;
    } catch (error) {
      throw new Error(`Failed to refund transaction: ${error}`);
    }
  }
}
