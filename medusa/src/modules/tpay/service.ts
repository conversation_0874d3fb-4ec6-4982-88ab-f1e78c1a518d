import {
  AbstractPaymentProvider,
  ContainerReg<PERSON><PERSON><PERSON><PERSON><PERSON>,
  MedusaError,
  Mo<PERSON><PERSON>,
} from "@medusajs/framework/utils";

import { container } from "@medusajs/framework";
import { Logger } from "@medusajs/js-sdk";
import { completeCartWorkflow } from "@medusajs/medusa/core-flows";
import {
  BigNumberInput,
  IEventBusModuleService,
  PaymentProviderContext,
  PaymentProviderError,
  PaymentProviderSessionResponse,
  PaymentSessionStatus,
} from "@medusajs/types";
import { OrderWithMetadata } from "src/admin/types";
import { OrderStatusEnum } from "src/admin/widgets/order-status/types";
import { config } from "src/config";
import { findOrCreateCustomerWorkflow } from "src/workflows/find-or-create-customer-workflow";
import { obtainExchangeRateWorkflow } from "src/workflows/obtain-exchange-rate";
import { updateCartByCustomerWorkflow } from "src/workflows/update-cart-by-customer-workflow";
import { updatePaymentSessionToMatchAmount } from "src/workflows/update-payment-session-to-match-amount";
import { CreateTransactionRequest, TpayClient } from "./tpay-client";

type InitiatePaymentOutput = {
  id: string;
  data: Record<string, unknown>;
};

type InitiatePaymentInput = {
  amount: BigNumberInput;
  currency_code: string;
  data: Record<string, unknown>;
  context: PaymentProviderContext & {
    tour_id: string;
    cart_id: string;
    payment_description: string;
    first_name: string;
    last_name: string;
    email: string;
    phone_number: string;
    date_for_advance_calculation: string;
    additional_description: string;
  };
};

type CancelPaymentInput = {
  data: Record<string, unknown>;
  context: PaymentProviderContext;
};

type PaymentProviderOutput = {
  data: Record<string, unknown>;
};

type Options = {
  clientId: string;
  clientSecret: string;
};

type InjectedDependencies = {
  logger: Logger;
};

export type TPayWebhookPayload = {
  id: string;
  tr_id: string;
  tr_date: string;
  tr_crc: string;
  tr_amount: string;
  tr_paid: string;
  tr_desc: string;
  tr_status: string;
  tr_error: string;
  tr_email: string;
  test_mode: string;
  md5sum: string;
};

type RefundPaymentInput = {
  amount: BigNumberInput;
  data: {
    posId: string;
    title: string;
    transactionId: string;
    hiddenDescription: string;
    transactionPaymentUrl: string;
  };
  context: PaymentProviderContext;
};

class TpayPaymentProviderService extends AbstractPaymentProvider<Options> {
  // TODO implement methods
  static identifier = "bramka_platnosci";

  protected logger_: Logger;
  protected options_: Options;
  protected client: TpayClient;
  protected eventBusService: IEventBusModuleService;

  constructor(container_: InjectedDependencies, options: Options) {
    super(container_, options);

    this.logger_ = container_.logger;
    this.options_ = options;
    this.eventBusService = container.resolve(Modules.EVENT_BUS);

    this.client = new TpayClient({
      clientId: options.clientId,
      clientSecret: options.clientSecret,
      isSandbox: process.env.TPAY_SANDBOX_FLAG === "true",
    });
  }

  static validateOptions(options: Record<any, any>) {
    if (!options.clientId) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "Client ID is required in the provider's options."
      );
    }

    if (!options.clientSecret) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "Client Secret is required in the provider's options."
      );
    }
  }

  async initiatePayment(
    input: InitiatePaymentInput
  ): Promise<InitiatePaymentOutput> {
    // Check if shop is enabled before processing payment
    const storeService = container.resolve(Modules.STORE);
    const stores = await storeService.listStores();
    const currentStore = stores?.[0];

    const isShopEnabled = currentStore?.metadata?.is_shop_enabled ?? true;

    if (!isShopEnabled) {
      throw new MedusaError(
        MedusaError.Types.NOT_ALLOWED,
        "Sklep jest tymczasowo wyłączony. Dostępny od 1.04."
      );
    }

    const {
      result: {
        calculatedAmount,
        calculatedAmountEuro,
        exchangeRate,
        prepaidPercentage,
      },
    } = await obtainExchangeRateWorkflow.run({
      input: {
        currency_code: input.currency_code,
        tour_id: input.context.tour_id!,
        date_for_advance_calculation:
          input.context.date_for_advance_calculation,
        amount: input.amount,
      },
    });

    if (!calculatedAmount) {
      throw new Error(`Failed to calculate amount`);
    }

    await updatePaymentSessionToMatchAmount.run({
      input: {
        amount: calculatedAmountEuro,
        session_id: input.context.session_id,
      },
    });

    const {
      result: { customer },
    } = await findOrCreateCustomerWorkflow.run({
      input: {
        email: input.context.email!,
        payment_id: input.context.session_id,
        phone_number: input.context.phone_number,
        first_name: input.context.first_name,
        last_name: input.context.last_name,
      },
      logOnError: true,
    });

    const { result: updatedCart } = await updateCartByCustomerWorkflow.run({
      input: {
        customer_id: customer?.id!,
        cart_id: input.context.cart_id,
      },
    });

    const { result: createdOrder } = await completeCartWorkflow.run({
      input: {
        id: updatedCart.response.updatedCart.id,
      },
      container: container,
      logOnError: true,
      throwOnError: true,
    });

    const orderService = container.resolve(Modules.ORDER);

    const [updatedOrder] = await orderService.updateOrders([
      {
        id: createdOrder.id,
        metadata: {
          status: OrderStatusEnum.unpaid,
          additional_customer_note: input.context.additional_description,
          euro_rate: exchangeRate.rate,
          prepaid_percentage: prepaidPercentage,
        },
      },
    ]);

    const createTransactionInput: CreateTransactionRequest = {
      amount: calculatedAmount,
      description: `Zamówienie nr ${updatedOrder.display_id}`,
      hiddenDescription: input.context.session_id,
      additionalDescription: input.context.additional_description,
      payer: {
        name: input.context.first_name + " " + input.context.last_name,
        email: input.context.email,
        phone: input.context.phone_number,
      },
      callbacks: {
        payerUrls: {
          success: `${config.MEDUSA_STOREFRONT_URL}/status-zamowienia?session=${input.context.session_id}&success=true`,
          error: `${config.MEDUSA_STOREFRONT_URL}/status-zamowienia?session=${input.context.session_id}&success=false`,
        },
        notification: {
          url: config.TPAY_NOTIFICATION_URL,
          email: process.env.TPAY_NOTIFICATION_EMAIL || null,
        },
      },
    };

    const transactionResponse = await this.client.createTransaction(
      createTransactionInput
    );

    return {
      id: transactionResponse.transactionId,
      data: {
        ...transactionResponse,
        historicalEuroExchangeRate: exchangeRate,
        prepaidPercentage,
        additional_description: input.context.additional_description,
      },
    };
  }

  async retrievePayment(
    paymentSessionData: Record<string, unknown>
  ): Promise<PaymentProviderError | PaymentProviderSessionResponse["data"]> {
    return {
      error: "Not implemented",
    };
  }

  async deletePayment(
    paymentSessionData: Record<string, unknown>
  ): Promise<PaymentProviderError | PaymentProviderSessionResponse["data"]> {
    console.log({ paymentSessionData });
    console.log("deletePayment triggered");

    return {
      data: {
        id: paymentSessionData.id,
        status: "canceled",
      },
    };
  }

  async getPaymentStatus(
    paymentSessionData: Record<string, unknown>
  ): Promise<PaymentSessionStatus> {
    const transactionResponse = await this.client.getTransaction(
      paymentSessionData.id as string
    );
  }

  async updatePayment(
    paymentSessionData: Record<string, unknown>
  ): Promise<PaymentSessionStatus> {
    console.log({ paymentSessionData });
    console.log("updatePayment triggered");

    return {
      data: paymentSessionData,
    };
  }

  async authorizePayment(
    paymentSessionData: Record<string, unknown>,
    context: Record<string, unknown>
  ): Promise<
    | PaymentProviderError
    | {
        status: PaymentSessionStatus;
        data: PaymentProviderSessionResponse["data"];
      }
  > {
    return {
      data: paymentSessionData,
      status: "authorized",
    };
  }

  async cancelPayment(
    input: CancelPaymentInput
  ): Promise<PaymentProviderOutput> {
    const externalId = input.data?.id;

    // assuming you have a client that cancels the payment
    // const paymentData = await this.client.cancelPayment(externalId);
    console.log(`triggered ${JSON.stringify(input)} - cancel step`);
    return { data: input };
  }

  async statusPayment(input: any): Promise<any> {
    const newData = await this.client.getTransaction(input.data.id);

    return {
      data: newData,
    };
  }

  async capturePayment(input: {
    email: string;
    cart_id: string;
  }): Promise<PaymentProviderError | { data: Record<string, unknown> }> {
    try {
      console.log({ DATA_IN_CAPTURE: input });

      const query = container.resolve(ContainerRegistrationKeys.QUERY);

      // Retrieve the order linked to the cart
      const { data: carts } = await query.graph({
        entity: "cart",
        fields: ["order.*"],
        filters: {
          id: input.cart_id,
        },
      });

      if (!carts || carts.length === 0 || !carts[0].order) {
        // Handle case where no cart or order is found
        return {
          error: "No order found for the given cart ID",
          code: "not_found",
        };
      }

      const orderId = carts[0].order.id;

      // Now you can use the order ID
      const orderService = container.resolve(Modules.ORDER);

      await orderService.updateOrders([
        {
          id: orderId,
          metadata: {
            status: "new_order",
          },
        },
      ]);

      // Emit event with the found order ID
      this.eventBusService.emit({
        name: "order-payment-captured",
        data: {
          id: orderId,
        },
      });

      return { data: { order_id: orderId } };
    } catch (error) {
      console.error("Error in capturePayment:", error);
      return {
        error: "Failed to capture payment",
        code: "capture_failed",
      };
    }
  }

  async refundPayment(
    paymentData: {
      data: {
        order_id?: string;
        email: string;
        cart_id?: string;
      };
    },
    refundAmount: { value: string | number; precision: number }
  ): Promise<PaymentProviderOutput> {
    const query = container.resolve(ContainerRegistrationKeys.QUERY);

    const { data: order } = await query.graph({
      entity: "order",
      fields: ["metadata"],
      filters: {
        id: paymentData.data.order_id,
      },
    });

    if (!order || order.length === 0) {
      throw new Error("Order not found");
    }

    const orderMetadata = order[0].metadata as OrderWithMetadata["metadata"];

    const amountToRefundByMultipliedHistoricalEuroValue =
      Number(refundAmount.value) * Number(orderMetadata.euro_rate);

    const decimalPriceWithPrecision2DecimalPlaces = Number(
      amountToRefundByMultipliedHistoricalEuroValue
    ).toFixed(2);

    // TODO: uncomment this later on but handle that tpay accepts only one refund per transaction
    // const response = await this.client.refundTransaction(
    //   paymentData.transactionId as string,
    //   Number(decimalPriceWithPrecision2DecimalPlaces)
    // );

    return { data: { amount: decimalPriceWithPrecision2DecimalPlaces } };
  }
}

export default TpayPaymentProviderService;
