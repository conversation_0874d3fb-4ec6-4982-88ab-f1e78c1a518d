import {
  InjectTransactionManager,
  MedusaContext,
  MedusaService,
} from "@medusajs/framework/utils";
import { EntityManager } from "@mikro-orm/knex";
import { InjectManager } from "@medusajs/framework/utils";
import { Context } from "@medusajs/framework/types";
import { OrderTransaction } from "./models/order-transaction";

type DeleteOrderTransactionInput = {
  orderTransactionId: string;
};

type MarkOrderTransactionAsRevokedInput = {
  orderTransactionId: string;
};

class OrderTransactionService extends MedusaService({ OrderTransaction }) {
  @InjectTransactionManager()
  protected async deleteOrderTransaction_(
    update: DeleteOrderTransactionInput,
    @MedusaContext() sharedContext?: Context<EntityManager>
  ): Promise<void> {
    const transactionManager = sharedContext?.transactionManager;

    if (!transactionManager) {
      throw new Error("Transaction manager not found");
    }

    await transactionManager.nativeDelete("order_transaction", {
      id: update.orderTransactionId,
    });
  }

  @InjectTransactionManager()
  protected async markOrderTransactionAsRevoked_(
    update: MarkOrderTransactionAsRevokedInput,
    @MedusaContext() sharedContext?: Context<EntityManager>
  ): Promise<void> {
    const transactionManager = sharedContext?.transactionManager;

    if (!transactionManager) {
      throw new Error("Transaction manager not found");
    }

    await transactionManager.nativeUpdate(
      "order_transaction",
      {
        id: update.orderTransactionId,
      },
      {
        reference: "refund_revert",
      }
    );
  }

  @InjectManager()
  async deleteOrderTransaction(
    update: DeleteOrderTransactionInput,
    @MedusaContext() sharedContext?: Context<EntityManager>
  ): Promise<void> {
    await this.deleteOrderTransaction_(update, sharedContext);
  }

  @InjectManager()
  async markOrderTransactionAsRevoked(
    update: MarkOrderTransactionAsRevokedInput,
    @MedusaContext() sharedContext?: Context<EntityManager>
  ): Promise<void> {
    await this.markOrderTransactionAsRevoked_(update, sharedContext);
  }
}

export default OrderTransactionService;
