import { Migration } from '@mikro-orm/migrations';

export class Migration20250325082434 extends Migration {

  async up(): Promise<void> {
    this.addSql('create table if not exists "report" ("id" text not null, "income" integer not null default 0, "sales_quantity" integer not null default 0, "num_of_products_sold" integer not null default 0, "num_of_returns" integer not null default 0, "refund_amount" integer not null default 0, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "report_pkey" primary key ("id"));');
    this.addSql('CREATE INDEX IF NOT EXISTS "IDX_report_deleted_at" ON "report" (deleted_at) WHERE deleted_at IS NULL;');

    this.addSql('drop table if exists "raport" cascade;');
  }

  async down(): Promise<void> {
    this.addSql('create table if not exists "raport" ("id" text not null, "income" integer not null default 0, "sales_quantity" integer not null default 0, "num_of_products_sold" integer not null default 0, "num_of_returns" integer not null default 0, "refund_amount" integer not null default 0, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "raport_pkey" primary key ("id"));');
    this.addSql('CREATE INDEX IF NOT EXISTS "IDX_raport_deleted_at" ON "raport" (deleted_at) WHERE deleted_at IS NULL;');

    this.addSql('drop table if exists "report" cascade;');
  }

}
