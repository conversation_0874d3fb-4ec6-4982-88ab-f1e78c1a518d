import { MedusaService } from "@medusajs/framework/utils";
import { Report } from "./models/report";
import { OrderWithMetadata } from "src/admin/types";
import { OrderStatus } from "src/admin/widgets/order-status/types";

interface SalesReport {
  income: number;
  sales_quantity: number;
  refund_amount: number;
  balance: number;
}

class ReportModuleService extends MedusaService({
  Report,
}) {
  STATUSES_TAKEN_INTO_ACCOUNT: OrderStatus[] = [
    "during_realization",
    "feedback_requested",
    "new_order",
  ];

  async generateReport(orders: OrderWithMetadata[]): Promise<SalesReport> {
    const report: SalesReport = {
      income: 0,
      sales_quantity: 0,
      refund_amount: 0,
      balance: 0,
    };

    for (const order of orders) {
      const paidTotal = Number(order.summary?.paid_total! || 0);

      if (this.STATUSES_TAKEN_INTO_ACCOUNT.includes(order.metadata?.status)) {
        report.sales_quantity += 1;
        report.income += paidTotal;
      }

      const refundedTotal = Number(order.summary?.refunded_total || 0);
      if (refundedTotal > 0) {
        report.refund_amount += refundedTotal;
      }
    }

    report.balance = report.income - report.refund_amount;
    return report;
  }
}

export default ReportModuleService;
