import {
  InjectTransaction<PERSON>anager,
  MedusaContext,
  MedusaService,
} from "@medusajs/framework/utils";
import { EntityManager } from "@mikro-orm/knex";
import { InjectManager } from "@medusajs/framework/utils";
import { Context } from "@medusajs/framework/types";

type UpdateOrderSummaryInput = {
  orderId: string;
  refunded_total: number;
  transaction_total?: number;
  pending_difference?: number;
  paid_total?: number;
};

class OrderSummaryService extends MedusaService({}) {
  @InjectTransactionManager()
  protected async updateOrderSummary_(
    update: UpdateOrderSummaryInput,
    @MedusaContext() sharedContext?: Context<EntityManager>
  ): Promise<void> {
    const transactionManager = sharedContext?.transactionManager;

    if (!transactionManager) {
      throw new Error("Transaction manager not found");
    }

    // Use raw SQL to get the current totals JSON
    const result = await transactionManager.execute(
      "SELECT totals FROM order_summary WHERE order_id = ?",
      [update.orderId]
    );

    if (!result || result.length === 0) {
      throw new Error("Order summary not found");
    }

    // Parse the current totals JSON
    const currentTotals =
      typeof result[0].totals === "string"
        ? JSON.parse(result[0].totals)
        : result[0].totals;

    // Update the totals object
    const updatedTotals = {
      ...currentTotals,
      refunded_total: update.refunded_total,
      raw_refunded_total: {
        value: update.refunded_total.toString(),
        precision: 20,
      },
    };

    // Add optional fields if provided
    if (update.transaction_total !== undefined) {
      updatedTotals.transaction_total = update.transaction_total;
      updatedTotals.raw_transaction_total = {
        value: update.transaction_total.toString(),
        precision: 20,
      };
    }

    if (update.pending_difference !== undefined) {
      updatedTotals.pending_difference = update.pending_difference;
      updatedTotals.raw_pending_difference = {
        value: update.pending_difference.toString(),
        precision: 20,
      };
    }

    if (update.paid_total !== undefined) {
      updatedTotals.paid_total = update.paid_total;
      updatedTotals.raw_paid_total = {
        value: update.paid_total.toString(),
        precision: 20,
      };
    }

    // Update the order summary with the new totals JSON using raw SQL
    await transactionManager.execute(
      "UPDATE order_summary SET totals = ?, updated_at = NOW() WHERE order_id = ?",
      [JSON.stringify(updatedTotals), update.orderId]
    );
  }

  @InjectManager()
  async updateOrderSummary(
    update: UpdateOrderSummaryInput,
    @MedusaContext() sharedContext?: Context<EntityManager>
  ): Promise<void> {
    await this.updateOrderSummary_(update, sharedContext);
  }
}

export default OrderSummaryService;
