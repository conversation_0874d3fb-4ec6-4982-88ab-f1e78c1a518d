{"namespaces": ["public"], "name": "public", "tables": [{"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "rate": {"name": "rate", "type": "real", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "float"}, "currency_code": {"name": "currency_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'EUR'", "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "exchange_rate", "schema": "public", "indexes": [{"keyName": "IDX_exchange_rate_deleted_at", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_exchange_rate_deleted_at\" ON \"exchange_rate\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "exchange_rate_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}}]}