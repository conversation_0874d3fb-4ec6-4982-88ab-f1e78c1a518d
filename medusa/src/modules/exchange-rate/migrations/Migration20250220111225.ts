import { Migration } from '@mikro-orm/migrations';

export class Migration20250220111225 extends Migration {

  async up(): Promise<void> {
    this.addSql('create table if not exists "exchange_rate" ("id" text not null, "rate" real not null, "currency_code" text not null default \'EUR\', "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "exchange_rate_pkey" primary key ("id"));');
    this.addSql('CREATE INDEX IF NOT EXISTS "IDX_exchange_rate_deleted_at" ON "exchange_rate" (deleted_at) WHERE deleted_at IS NULL;');
  }

  async down(): Promise<void> {
    this.addSql('drop table if exists "exchange_rate" cascade;');
  }

}
