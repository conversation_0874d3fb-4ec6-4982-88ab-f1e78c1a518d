import { MedusaService } from "@medusajs/framework/utils";
import ExchangeRate from "./models/exchange-rate";

export type UpdateGuidePositionInput = {
  id: string;
  position: number;
};

class ExchangeRateService extends MedusaService({
  ExchangeRate,
}) {
  async fetchExchangeRate(currencyCode: string = "eur") {
    try {
      const response = await fetch(
        `https://api.nbp.pl/api/exchangerates/rates/a/${currencyCode}?format=json`
      );
      const data = await response.json();

      const exchangeRate = await this.getExchangeRate(currencyCode);
      if (exchangeRate.currency_code === currencyCode) {
        await this.updateExchangeRates([
          {
            id: exchangeRate.id,
            rate: data.rates[0].mid,
            updated_at: new Date(),
          },
        ]);
      } else {
        await this.createExchangeRates([
          {
            currency_code: currencyCode,
            rate: data.rates[0].mid,
          },
        ]);
      }
      return data;
    } catch (error) {
      console.error(error);
      return null;
    }
  }

  async getExchangeRate(currencyCode: string) {
    const exchangeRate = await this.listExchangeRates({
      currency_code: currencyCode,
    });
    return exchangeRate[0];
  }
}

export default ExchangeRateService;
