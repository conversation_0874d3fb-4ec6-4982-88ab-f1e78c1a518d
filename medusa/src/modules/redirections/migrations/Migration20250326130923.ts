import { Migration } from '@mikro-orm/migrations';

export class Migration20250326130923 extends Migration {

  async up(): Promise<void> {
    this.addSql('create table if not exists "redirection" ("id" text not null, "from_path" text not null, "to_path" text not null, "status_code" integer not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "redirection_pkey" primary key ("id"));');
    this.addSql('CREATE INDEX IF NOT EXISTS "IDX_redirection_deleted_at" ON "redirection" (deleted_at) WHERE deleted_at IS NULL;');
  }

  async down(): Promise<void> {
    this.addSql('drop table if exists "redirection" cascade;');
  }

}
