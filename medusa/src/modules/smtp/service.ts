import {
  AbstractNotificationProviderService,
  ContainerRegistrationKeys,
} from "@medusajs/framework/utils";
import {
  ProviderSendNotificationDTO,
  ProviderSendNotificationResultsDTO,
} from "@medusajs/framework/types";
import nodemailer from "nodemailer";

import { orderTicketCreatedTemplate } from "src/templates/email-templates/order-ticket-created-template";
import { orderTicketUpdatedTemplate } from "src/templates/email-templates/order-ticket-updated-template";
import { generateTicketPDFWorkflow } from "src/workflows/generate-ticket-pdf-workflow";
import { ExtendedNotificationDTO, NotificationTemplate } from "./types";
import { newOrderTemplate } from "src/templates/email-templates/new-order-template";
import { orderCanceledTemplate } from "src/templates/email-templates/order-canceled-templete";
import { container, Query } from "@medusajs/framework";
import { adminNewOrderTemplate } from "src/templates/email-templates/admin/admin-new-order-template";
import { adminOrderCanceledTemplate } from "src/templates/email-templates/admin/admin-order-canceled";
import { ObtainOrderDetailsForEmailResult } from "../ticket/types";
import { orderDayBeforeReminderTemplate } from "src/templates/email-templates/order-day-before-reminder-template";
import { orderRefundedTemplate } from "src/templates/email-templates/order-refunded-template";
import { waitingForFeedbackTemplate } from "src/templates/email-templates/waiting-for-feedback-template";
import { config } from "src/config";
import { adminTicketCreatedTemplate } from "src/templates/email-templates/admin/admin-ticket-created-template";
import { adminTicketUpdatedTemplate } from "src/templates/email-templates/admin/admin-ticket-updated-template";
import { OrderWithMetadata } from "src/admin/types";
import { getTicketFileName } from "src/lib/utils";

type Options = {
  apiKey: string;
};

class SMTPNotificationsService extends AbstractNotificationProviderService {
  static identifier = "smtp";

  protected options_: Options;
  protected queryService_: Query;

  protected readonly transporter: nodemailer.Transporter;

  constructor(containerr, options) {
    super();

    this.queryService_ = container.resolve(ContainerRegistrationKeys.QUERY);

    try {
      this.transporter = nodemailer.createTransport({
        host: process.env.SMTP_HOST,
        port: parseInt(process.env.SMTP_PORT || "587"),
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASSWORD,
        },
      });
    } catch (error) {
      console.error(`SMTP notification error: ${error.message}`, error);
      throw error;
    }
  }
  async generatePDF(order_id: string): Promise<Buffer | null> {
    try {
      const { result: buffer } = await generateTicketPDFWorkflow.run({
        input: {
          order: {
            id: order_id,
          },
          withPathSave: true,
        },
      });

      if (buffer?.result) {
        return Buffer.from(Object.values(buffer.result));
      }

      return null;
    } catch (error) {
      console.error(`Error generating PDF: ${error.message}`, error);
      return null;
    }
  }

  async getAttachments(
    notification: ExtendedNotificationDTO,
    orderDetails: ObtainOrderDetailsForEmailResult
  ) {
    let attachments: any[] = [];

    if (notification.data?.order_id) {
      const pdfBuffer = await this.generatePDF(notification.data?.order_id);

      if (pdfBuffer) {
        attachments.push({
          filename: getTicketFileName(
            notification.data?.order_id,
            orderDetails.customer.last_name
          ),
          content: pdfBuffer,
          contentType: "application/pdf",
        });
      }
    }

    return attachments;
  }

  async obtainOrderDetails(order_id: string) {
    const {
      data: [order],
    } = await this.queryService_.graph({
      entity: "order",
      fields: [
        "items.*",
        "summary.*",
        "metadata",
        "cart.metadata",
        "items.product.title",
        "items.product.tour.*",
        "customer.*",
        "ticket.attraction_name",
      ],
      filters: {
        id: order_id,
      },
    });

    const orderWithMetadata = order as unknown as OrderWithMetadata & {
      ticket: {
        attraction_name: string;
      };
    };

    return {
      ...orderWithMetadata,
      euroRate: orderWithMetadata.metadata.euro_rate,
      prepaidPercentage: orderWithMetadata.metadata.prepaid_percentage,
      ticket: {
        attraction_name: orderWithMetadata.ticket?.attraction_name || "",
      },
    } as unknown as ObtainOrderDetailsForEmailResult;
  }

  getBcc(notification: ExtendedNotificationDTO) {
    const templatesForAdminInBcc: NotificationTemplate[] = [
      "your-order-is-being-processed",
      "order-ticket-updated",
    ];

    if (templatesForAdminInBcc.includes(notification.template)) {
      return config.ADMIN_EMAIL;
    }

    return undefined;
  }

  async send(
    notification: ProviderSendNotificationDTO & ExtendedNotificationDTO
  ): Promise<ProviderSendNotificationResultsDTO> {
    try {
      const { to, subject, html, attachments } = await this.formatEmail(
        notification
      );

      const result = this.transporter.sendMail({
        from: `"wakacyjnepomysly.pl" <${process.env.SMTP_FROM}>`,
        to,
        bcc: this.getBcc(notification),
        subject,
        html,
        attachments,
      });

      return result;
    } catch (error) {
      console.error(`SMTP notification error: ${error.message}`, error);
      // Return a standardized error response
      return {
        success: false,
        error: error.message,
        messageId: null,
      } as ProviderSendNotificationResultsDTO;
    }
  }

  private async formatEmail(notification: ExtendedNotificationDTO) {
    const orderDetails = await this.obtainOrderDetails(
      notification.data?.order_id!
    );

    const obtainHtml = async () => {
      switch (notification.template) {
        case "admin-new-order":
          return {
            to: config.ADMIN_EMAIL,
            html: adminNewOrderTemplate({
              reservation_number:
                notification.data?.reservation_number || "N/A",
              orderDetails,
            }),
            shouldGeneratePDF: false,
          };

        case "admin-order-canceled":
          return {
            to: config.ADMIN_EMAIL,
            html: adminOrderCanceledTemplate({
              reservation_number:
                notification.data?.reservation_number || "N/A",
              orderDetails,
            }),
            shouldGeneratePDF: false,
          };
        case "admin-ticket-created":
          return {
            to: config.ADMIN_EMAIL,
            html: adminTicketCreatedTemplate({
              reservation_number:
                notification.data?.reservation_number || "N/A",
              orderDetails,
            }),
            shouldGeneratePDF: true,
          };
        case "admin-ticket-updated":
          return {
            to: config.ADMIN_EMAIL,
            html: adminTicketUpdatedTemplate({
              reservation_number:
                notification.data?.reservation_number || "N/A",
              orderDetails,
              update_reason: notification.data?.update_reason,
            }),
            shouldGeneratePDF: true,
          };
        case "your-order-has-been-canceled":
          return {
            html: orderCanceledTemplate({
              reservation_number:
                notification.data?.reservation_number || "N/A",
              attraction_name: notification.data?.attraction_name,
              date: notification.data?.date,
            }),
            shouldGeneratePDF: false,
          };
        case "attraction-tomorrow-reminder":
          return {
            html: orderDayBeforeReminderTemplate({
              reservation_number:
                notification.data?.reservation_number || "N/A",
              attraction_name: notification.data?.attraction_name,
              date: notification.data?.date,
            }),
            shouldGeneratePDF: false,
          };
        case "your-order-has-been-refunded":
          return {
            html: orderRefundedTemplate({
              reservation_number:
                notification.data?.reservation_number || "N/A",
              attraction_name: notification.data?.attraction_name,
              date: notification.data?.date,
            }),
            shouldGeneratePDF: false,
          };
        case "new-order":
          return {
            html: newOrderTemplate({
              reservation_number:
                notification.data?.reservation_number || "N/A",
              attraction_name: notification.data?.attraction_name,
              date: notification.data?.date,
              orderDetails,
            }),
            shouldGeneratePDF: false,
          };

        case "your-order-is-being-processed":
          return {
            to: notification.to,
            html: orderTicketCreatedTemplate({
              reservation_number:
                notification.data?.reservation_number || "N/A",
              attraction_name: notification.data?.attraction_name,
              date: notification.data?.date,
              order_id: notification.data?.order_id!,
              customer_last_name: orderDetails.customer.last_name,
            }),
            shouldGeneratePDF: true,
          };
        case "order-ticket-updated":
          return {
            to: notification.to,
            html: orderTicketUpdatedTemplate({
              reservation_number:
                notification.data?.reservation_number || "N/A",
              attraction_name: notification.data?.attraction_name,
              date: notification.data?.date,
              order_id: notification.data?.order_id!,
              customer_last_name: orderDetails.customer.last_name || null,
              update_reason: notification.data?.update_reason,
            }),
            shouldGeneratePDF: true,
          };
        case "waiting-for-your-feedback":
          return {
            html: waitingForFeedbackTemplate({
              reservation_number:
                notification.data?.reservation_number || "N/A",
              attraction_name: notification.data?.attraction_name,
              date: notification.data?.date,
            }),
            shouldGeneratePDF: false,
          };
        default:
          return undefined;
      }
    };

    const getSubject = () => {
      switch (notification.template) {
        case "admin-new-order":
          return "Nowe zamówienie w sklepie";
        case "admin-order-canceled":
          return "Zamówienie zostało anulowane";
        case "admin-ticket-created":
          return "Bilet został utworzony";
        case "admin-ticket-updated":
          return "Bilet został zaktualizowany";
        case "your-order-is-being-processed":
          return "Twoja atrakcja została potwierdzona!";
        case "your-order-has-been-canceled":
          return "Twoje zamówienie zostało anulowane";
        case "your-order-has-been-refunded":
          return "Twoje zamówienie zostało zwrócone";
        case "your-order-has-been-completed":
          return "Twoje zamówienie zostało zakończone";
        case "waiting-for-your-feedback":
          return "Czekamy na Twoją opinię";
        case "attraction-tomorrow-reminder":
          return "To już jutro! Czeka na Ciebie Twoja atrakcja!";
        case "new-order":
          return "Przyjęliśmy Twoje zamówienie do realizacji";
        case "order-ticket-updated":
          return "Twoja rezerwacja została zaktualizowana";

        default:
          return "Informacja o rezerwacji";
      }
    };

    const { html, shouldGeneratePDF, to } = (await obtainHtml()) || {};

    return {
      to: to || notification.to,
      subject: getSubject(),
      html,
      attachments: shouldGeneratePDF
        ? await this.getAttachments(notification, orderDetails)
        : [],
    };
  }
}

export default SMTPNotificationsService;
