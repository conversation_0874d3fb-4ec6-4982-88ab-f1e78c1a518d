import { NotificationDTO } from "@medusajs/types";

export type NotificationEventDataDTO = {
  customer_id: string;
  reservation_number: string;
  attraction_name?: string;
  date?: string;
  update_reason?: string;
  order_id?: string;
  ticket_id?: string;
  reason?: string;
};

export type ExtendedNotificationDTO = Partial<NotificationDTO> & {
  data: NotificationEventDataDTO;
  template: NotificationTemplate;
};

export type NotificationTemplate =
  | "admin-new-order"
  | "admin-order-canceled"
  | "admin-ticket-created"
  | "admin-ticket-updated"
  | "new-order"
  | "your-order-is-being-processed"
  | "your-order-has-been-canceled"
  | "your-order-has-been-refunded"
  | "your-order-has-been-completed"
  | "waiting-for-your-feedback"
  | "attraction-tomorrow-reminder"
  | "order-ticket-updated";
