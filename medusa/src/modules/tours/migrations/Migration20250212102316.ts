import { Migration } from '@mikro-orm/migrations';

export class Migration20250212102316 extends Migration {

  async up(): Promise<void> {
    this.addSql('alter table if exists "tour_start_places_by_date" drop constraint if exists "tour_start_places_by_date_tour_id_id_unique";');

    this.addSql('alter table if exists "tour_start_times" alter column "interval" type integer using ("interval"::integer);');
    this.addSql('alter table if exists "tour_start_times" alter column "interval" drop not null;');
  }

  async down(): Promise<void> {
    this.addSql('alter table if exists "tour_start_places_by_date" add constraint "tour_start_places_by_date_tour_id_id_unique" unique ("tour_id_id");');

    this.addSql('alter table if exists "tour_start_times" alter column "interval" type integer using ("interval"::integer);');
    this.addSql('alter table if exists "tour_start_times" alter column "interval" set not null;');
  }

}
