import { Migration } from '@mikro-orm/migrations';

export class Migration20250212131010 extends Migration {

  async up(): Promise<void> {
    this.addSql('alter table if exists "tour_food_options" add column if not exists "required" boolean not null default false;');
  }

  async down(): Promise<void> {
    this.addSql('alter table if exists "tour_food_options" drop column if exists "required";');
  }

}
