import { Migration } from "@mikro-orm/migrations";

export class Migration2024070210550 extends Migration {
  async up(): Promise<void> {
    // Create the new tour_available_start_places table
    this.addSql(
      `ALTER TABLE public.tour_pricing ALTER COLUMN prepaid_percentage TYPE double precision USING prepaid_percentage::double precision`
    );
  }

  async down(): Promise<void> {
    this.addSql(
      `ALTER TABLE public.tour_pricing ALTER COLUMN prepaid_percentage TYPE real USING prepaid_percentage::real`
    );
  }
}
