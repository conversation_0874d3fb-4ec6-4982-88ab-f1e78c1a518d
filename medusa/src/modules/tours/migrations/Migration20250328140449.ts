import { Migration } from "@mikro-orm/migrations";

export class Migration20250328140449 extends Migration {
  async up(): Promise<void> {
    // Remove foreign key constraint first
    this.addSql(
      'alter table if exists "tour_recommended_tours" drop constraint if exists "tour_recommended_tours_tour_id_id_foreign";'
    );

    // Drop the table after data has been migrated in the previous migration
    this.addSql('drop table if exists "tour_recommended_tours" cascade;');
  }

  async down(): Promise<void> {
    // Recreation of table would be handled manually if needed
    // This is just a placeholder for the down migration
    this.addSql(`
      -- This is a placeholder. The table would need to be recreated manually if needed.
      -- The schema would be:
      -- CREATE TABLE "tour_recommended_tours" (
      --   "id" text NOT NULL,
      --   "tour_id_id" text NOT NULL,
      --   "recommended_tour_id" text NOT NULL,
      --   "created_at" timestamptz NOT NULL DEFAULT now(),
      --   "updated_at" timestamptz NOT NULL DEFAULT now(),
      --   "deleted_at" timestamptz NULL,
      --   CONSTRAINT "tour_recommended_tours_pkey" PRIMARY KEY ("id")
      -- );
    `);
  }
}
