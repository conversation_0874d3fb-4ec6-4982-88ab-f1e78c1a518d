import { Migration } from '@mikro-orm/migrations';

export class Migration20250310115156 extends Migration {

  async up(): Promise<void> {
    this.addSql('alter table if exists "tour" add column if not exists "at_least_one_food_option_required" boolean not null default false;');
  }

  async down(): Promise<void> {
    this.addSql('alter table if exists "tour" drop column if exists "at_least_one_food_option_required";');
  }

}
