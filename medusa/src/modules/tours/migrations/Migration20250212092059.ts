import { Migration } from '@mikro-orm/migrations';

export class Migration20250212092059 extends Migration {

  async up(): Promise<void> {
    this.addSql('create table if not exists "tour_start_places_by_date" ("id" text not null, "tour_id_id" text not null, "place" text not null, "month" integer not null, "days" text[] null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "tour_start_places_by_date_pkey" primary key ("id"));');
    this.addSql('alter table if exists "tour_start_places_by_date" add constraint "tour_start_places_by_date_tour_id_id_unique" unique ("tour_id_id");');
    this.addSql('CREATE INDEX IF NOT EXISTS "IDX_tour_start_places_by_date_tour_id_id" ON "tour_start_places_by_date" (tour_id_id) WHERE deleted_at IS NULL;');
    this.addSql('CREATE INDEX IF NOT EXISTS "IDX_tour_start_places_by_date_deleted_at" ON "tour_start_places_by_date" (deleted_at) WHERE deleted_at IS NULL;');

    this.addSql('alter table if exists "tour_start_places_by_date" add constraint "tour_start_places_by_date_tour_id_id_foreign" foreign key ("tour_id_id") references "tour" ("id") on update cascade on delete cascade;');

    this.addSql('drop table if exists "tour_start_places" cascade;');

    this.addSql('alter table if exists "tour" add column if not exists "available_start_places" text[] null;');
  }

  async down(): Promise<void> {
    this.addSql('create table if not exists "tour_start_places" ("id" text not null, "tour_id_id" text not null, "place" text not null, "date_active_from" timestamptz not null, "date_active_to" timestamptz not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "tour_start_places_pkey" primary key ("id"));');
    this.addSql('alter table if exists "tour_start_places" add constraint "tour_start_places_tour_id_id_unique" unique ("tour_id_id");');
    this.addSql('CREATE INDEX IF NOT EXISTS "IDX_tour_start_places_tour_id_id" ON "tour_start_places" (tour_id_id) WHERE deleted_at IS NULL;');
    this.addSql('CREATE INDEX IF NOT EXISTS "IDX_tour_start_places_deleted_at" ON "tour_start_places" (deleted_at) WHERE deleted_at IS NULL;');

    this.addSql('alter table if exists "tour_start_places" add constraint "tour_start_places_tour_id_id_foreign" foreign key ("tour_id_id") references "tour" ("id") on update cascade on delete cascade;');

    this.addSql('drop table if exists "tour_start_places_by_date" cascade;');

    this.addSql('alter table if exists "tour" drop column if exists "available_start_places";');
  }

}
