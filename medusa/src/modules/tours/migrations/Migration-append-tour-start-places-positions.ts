import { Migration } from "@mikro-orm/migrations";

export class Migration20240702105919 extends Migration {
  async up(): Promise<void> {
    // First add the position column with default value 0
    this.addSql(
      `ALTER TABLE "tour_available_start_places" ADD COLUMN IF NOT EXISTS "position" integer NOT NULL DEFAULT 0;`
    );

    // Then update existing records with sequential positions based on tour_id
    this.addSql(`
      WITH indexed_places AS (
        SELECT id, tour_id_id, ROW_NUMBER() OVER (PARTITION BY tour_id_id ORDER BY id) - 1 as new_position
        FROM tour_available_start_places
      )
      UPDATE tour_available_start_places
      SET position = indexed_places.new_position
      FROM indexed_places
      WHERE tour_available_start_places.id = indexed_places.id;
    `);
  }

  async down(): Promise<void> {
    // Remove the position column if migration is reverted
    this.addSql(
      `ALTER TABLE "tour_available_start_places" DROP COLUMN IF EXISTS "position";`
    );
  }
}
