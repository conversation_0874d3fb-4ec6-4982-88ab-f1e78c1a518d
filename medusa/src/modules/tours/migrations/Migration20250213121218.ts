import { Migration } from '@mikro-orm/migrations';

export class Migration20250213121218 extends Migration {

  async up(): Promise<void> {
    this.addSql('alter table if exists "tour_tour_categories" drop constraint if exists "tour_tour_categories_tour_category_id_foreign";');

    this.addSql('drop table if exists "tour_category" cascade;');

    this.addSql('drop table if exists "tour_tour_categories" cascade;');
  }

  async down(): Promise<void> {
    this.addSql('create table if not exists "tour_category" ("id" text not null, "name" text not null, "image" text not null, "slug" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "tour_category_pkey" primary key ("id"));');
    this.addSql('CREATE INDEX IF NOT EXISTS "IDX_tour_category_deleted_at" ON "tour_category" (deleted_at) WHERE deleted_at IS NULL;');

    this.addSql('create table if not exists "tour_tour_categories" ("tour_id" text not null, "tour_category_id" text not null, constraint "tour_tour_categories_pkey" primary key ("tour_id", "tour_category_id"));');

    this.addSql('alter table if exists "tour_tour_categories" add constraint "tour_tour_categories_tour_id_foreign" foreign key ("tour_id") references "tour" ("id") on update cascade on delete cascade;');
    this.addSql('alter table if exists "tour_tour_categories" add constraint "tour_tour_categories_tour_category_id_foreign" foreign key ("tour_category_id") references "tour_category" ("id") on update cascade on delete cascade;');
  }

}
