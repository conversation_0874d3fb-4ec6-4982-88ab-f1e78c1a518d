import { Migration } from '@mikro-orm/migrations';

export class Migration20250715190840 extends Migration {

  async up(): Promise<void> {
    this.addSql('drop table if exists "tour_recommended_tours" cascade;');

    this.addSql('alter table if exists "tour" add column if not exists "recommended_tour_ids" text[] not null default \'{}\', add column if not exists "is_recommended" boolean not null default false, add column if not exists "is_bargain" boolean not null default false, add column if not exists "is_promotion" boolean not null default false, add column if not exists "promotion_price_from" text null;');
  }

  async down(): Promise<void> {
    this.addSql('create table if not exists "tour_recommended_tours" ("id" text not null, "tour_id_id" text not null, "recommended_tour_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "tour_recommended_tours_pkey" primary key ("id"));');
    this.addSql('CREATE INDEX IF NOT EXISTS "IDX_tour_recommended_tours_tour_id_id" ON "tour_recommended_tours" (tour_id_id) WHERE deleted_at IS NULL;');
    this.addSql('CREATE INDEX IF NOT EXISTS "IDX_tour_recommended_tours_deleted_at" ON "tour_recommended_tours" (deleted_at) WHERE deleted_at IS NULL;');

    this.addSql('alter table if exists "tour_recommended_tours" add constraint "tour_recommended_tours_tour_id_id_foreign" foreign key ("tour_id_id") references "tour" ("id") on update cascade on delete cascade;');

    this.addSql('alter table if exists "tour" drop column if exists "recommended_tour_ids";');
    this.addSql('alter table if exists "tour" drop column if exists "is_recommended";');
    this.addSql('alter table if exists "tour" drop column if exists "is_bargain";');
    this.addSql('alter table if exists "tour" drop column if exists "is_promotion";');
    this.addSql('alter table if exists "tour" drop column if exists "promotion_price_from";');
  }

}
