import { Migration } from '@mikro-orm/migrations';

export class Migration20250212134325 extends Migration {

  async up(): Promise<void> {
    this.addSql('create table if not exists "tour_blocked_dates_by_month" ("id" text not null, "tour_id_id" text not null, "month" integer not null, "dates" text null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "tour_blocked_dates_by_month_pkey" primary key ("id"));');
    this.addSql('CREATE INDEX IF NOT EXISTS "IDX_tour_blocked_dates_by_month_tour_id_id" ON "tour_blocked_dates_by_month" (tour_id_id) WHERE deleted_at IS NULL;');
    this.addSql('CREATE INDEX IF NOT EXISTS "IDX_tour_blocked_dates_by_month_deleted_at" ON "tour_blocked_dates_by_month" (deleted_at) WHERE deleted_at IS NULL;');

    this.addSql('alter table if exists "tour_blocked_dates_by_month" add constraint "tour_blocked_dates_by_month_tour_id_id_foreign" foreign key ("tour_id_id") references "tour" ("id") on update cascade;');
  }

  async down(): Promise<void> {
    this.addSql('drop table if exists "tour_blocked_dates_by_month" cascade;');
  }

}
