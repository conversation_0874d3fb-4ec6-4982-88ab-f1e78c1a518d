{"namespaces": ["public"], "name": "public", "tables": [{"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "name": {"name": "name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "city", "schema": "public", "indexes": [{"keyName": "IDX_city_deleted_at", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_city_deleted_at\" ON \"city\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "city_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "position": {"name": "position", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "is_active": {"name": "is_active", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "true", "mappedType": "boolean"}, "name": {"name": "name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "slug": {"name": "slug", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "product_pricing_variant": {"name": "product_pricing_variant", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'dynamic_age_groups'", "enumItems": ["dynamic_age_groups", "regular_variants", "boat_rental"], "mappedType": "enum"}, "description": {"name": "description", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "featured_image": {"name": "featured_image", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "organizator_mail": {"name": "organizator_mail", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "price_from": {"name": "price_from", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "dates_from_to": {"name": "dates_from_to", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "hourly_length": {"name": "hourly_length", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "gallery": {"name": "gallery", "type": "text[]", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "array"}, "cart_section_image": {"name": "cart_section_image", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "at_least_one_food_option_required": {"name": "at_least_one_food_option_required", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "tour", "schema": "public", "indexes": [{"keyName": "IDX_tour_deleted_at", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_deleted_at\" ON \"tour\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "tour_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}}, {"columns": {"city_id": {"name": "city_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "tour_id": {"name": "tour_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}}, "name": "city_tours", "schema": "public", "indexes": [{"keyName": "city_tours_pkey", "columnNames": ["city_id", "tour_id"], "composite": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"city_tours_city_id_foreign": {"constraintName": "city_tours_city_id_foreign", "columnNames": ["city_id"], "localTableName": "public.city_tours", "referencedColumnNames": ["id"], "referencedTableName": "public.city", "deleteRule": "cascade", "updateRule": "cascade"}, "city_tours_tour_id_foreign": {"constraintName": "city_tours_tour_id_foreign", "columnNames": ["tour_id"], "localTableName": "public.city_tours", "referencedColumnNames": ["id"], "referencedTableName": "public.tour", "deleteRule": "cascade", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "tour_id_id": {"name": "tour_id_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "place": {"name": "place", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "rich_text_content": {"name": "rich_text_content", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "position": {"name": "position", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "tour_available_start_places", "schema": "public", "indexes": [{"keyName": "IDX_tour_available_start_places_tour_id_id", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_available_start_places_tour_id_id\" ON \"tour_available_start_places\" (tour_id_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_tour_available_start_places_deleted_at", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_available_start_places_deleted_at\" ON \"tour_available_start_places\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "tour_available_start_places_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"tour_available_start_places_tour_id_id_foreign": {"constraintName": "tour_available_start_places_tour_id_id_foreign", "columnNames": ["tour_id_id"], "localTableName": "public.tour_available_start_places", "referencedColumnNames": ["id"], "referencedTableName": "public.tour", "deleteRule": "cascade", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "tour_id_id": {"name": "tour_id_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "date_from": {"name": "date_from", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "date_to": {"name": "date_to", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "only_one_day": {"name": "only_one_day", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "tour_blocked_dates", "schema": "public", "indexes": [{"keyName": "IDX_tour_blocked_dates_tour_id_id", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_blocked_dates_tour_id_id\" ON \"tour_blocked_dates\" (tour_id_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_tour_blocked_dates_deleted_at", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_blocked_dates_deleted_at\" ON \"tour_blocked_dates\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "tour_blocked_dates_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"tour_blocked_dates_tour_id_id_foreign": {"constraintName": "tour_blocked_dates_tour_id_id_foreign", "columnNames": ["tour_id_id"], "localTableName": "public.tour_blocked_dates", "referencedColumnNames": ["id"], "referencedTableName": "public.tour", "deleteRule": "cascade", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "tour_id_id": {"name": "tour_id_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "month": {"name": "month", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "days": {"name": "days", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "tour_blocked_dates_by_month", "schema": "public", "indexes": [{"keyName": "IDX_tour_blocked_dates_by_month_tour_id_id", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_blocked_dates_by_month_tour_id_id\" ON \"tour_blocked_dates_by_month\" (tour_id_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_tour_blocked_dates_by_month_deleted_at", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_blocked_dates_by_month_deleted_at\" ON \"tour_blocked_dates_by_month\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "tour_blocked_dates_by_month_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"tour_blocked_dates_by_month_tour_id_id_foreign": {"constraintName": "tour_blocked_dates_by_month_tour_id_id_foreign", "columnNames": ["tour_id_id"], "localTableName": "public.tour_blocked_dates_by_month", "referencedColumnNames": ["id"], "referencedTableName": "public.tour", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "tour_id_id": {"name": "tour_id_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "month": {"name": "month", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "days": {"name": "days", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "blocked_times": {"name": "blocked_times", "type": "text[]", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "array"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "tour_blocked_start_times_by_date", "schema": "public", "indexes": [{"keyName": "IDX_tour_blocked_start_times_by_date_tour_id_id", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_blocked_start_times_by_date_tour_id_id\" ON \"tour_blocked_start_times_by_date\" (tour_id_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_tour_blocked_start_times_by_date_deleted_at", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_blocked_start_times_by_date_deleted_at\" ON \"tour_blocked_start_times_by_date\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "tour_blocked_start_times_by_date_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"tour_blocked_start_times_by_date_tour_id_id_foreign": {"constraintName": "tour_blocked_start_times_by_date_tour_id_id_foreign", "columnNames": ["tour_id_id"], "localTableName": "public.tour_blocked_start_times_by_date", "referencedColumnNames": ["id"], "referencedTableName": "public.tour", "deleteRule": "cascade", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "tour_id_id": {"name": "tour_id_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "description": {"name": "description", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "calendar": {"name": "calendar", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "price": {"name": "price", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "how_to_book": {"name": "how_to_book", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "ticket": {"name": "ticket", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "specification_only_for_boats": {"name": "specification_only_for_boats", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "program": {"name": "program", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "details": {"name": "details", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "place": {"name": "place", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "tour_content_block", "schema": "public", "indexes": [{"columnNames": ["tour_id_id"], "composite": false, "keyName": "tour_content_block_tour_id_id_unique", "primary": false, "unique": true}, {"keyName": "IDX_tour_content_block_tour_id_id", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_content_block_tour_id_id\" ON \"tour_content_block\" (tour_id_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_tour_content_block_deleted_at", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_content_block_deleted_at\" ON \"tour_content_block\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "tour_content_block_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"tour_content_block_tour_id_id_foreign": {"constraintName": "tour_content_block_tour_id_id_foreign", "columnNames": ["tour_id_id"], "localTableName": "public.tour_content_block", "referencedColumnNames": ["id"], "referencedTableName": "public.tour", "deleteRule": "cascade", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "content_block_id_id": {"name": "content_block_id_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "question": {"name": "question", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "answer": {"name": "answer", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "position": {"name": "position", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "tour_faq", "schema": "public", "indexes": [{"keyName": "IDX_tour_faq_content_block_id_id", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_faq_content_block_id_id\" ON \"tour_faq\" (content_block_id_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_tour_faq_deleted_at", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_faq_deleted_at\" ON \"tour_faq\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "tour_faq_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"tour_faq_content_block_id_id_foreign": {"constraintName": "tour_faq_content_block_id_id_foreign", "columnNames": ["content_block_id_id"], "localTableName": "public.tour_faq", "referencedColumnNames": ["id"], "referencedTableName": "public.tour_content_block", "deleteRule": "cascade", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "tour_id_id": {"name": "tour_id_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "name": {"name": "name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "description": {"name": "description", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "price": {"name": "price", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "required": {"name": "required", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "tour_food_options", "schema": "public", "indexes": [{"keyName": "IDX_tour_food_options_tour_id_id", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_food_options_tour_id_id\" ON \"tour_food_options\" (tour_id_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_tour_food_options_deleted_at", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_food_options_deleted_at\" ON \"tour_food_options\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "tour_food_options_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"tour_food_options_tour_id_id_foreign": {"constraintName": "tour_food_options_tour_id_id_foreign", "columnNames": ["tour_id_id"], "localTableName": "public.tour_food_options", "referencedColumnNames": ["id"], "referencedTableName": "public.tour", "deleteRule": "cascade", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "tour_id_id": {"name": "tour_id_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "prepaid_percentage": {"name": "prepaid_percentage", "type": "real", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "float"}, "prepaid_percentage_high_season": {"name": "prepaid_percentage_high_season", "type": "real", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "float"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "tour_pricing", "schema": "public", "indexes": [{"columnNames": ["tour_id_id"], "composite": false, "keyName": "tour_pricing_tour_id_id_unique", "primary": false, "unique": true}, {"keyName": "IDX_tour_pricing_tour_id_id", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_pricing_tour_id_id\" ON \"tour_pricing\" (tour_id_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_tour_pricing_deleted_at", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_pricing_deleted_at\" ON \"tour_pricing\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "tour_pricing_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"tour_pricing_tour_id_id_foreign": {"constraintName": "tour_pricing_tour_id_id_foreign", "columnNames": ["tour_id_id"], "localTableName": "public.tour_pricing", "referencedColumnNames": ["id"], "referencedTableName": "public.tour", "deleteRule": "cascade", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "tour_id_id": {"name": "tour_id_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "recommended_tour_id": {"name": "recommended_tour_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "tour_recommended_tours", "schema": "public", "indexes": [{"keyName": "IDX_tour_recommended_tours_tour_id_id", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_recommended_tours_tour_id_id\" ON \"tour_recommended_tours\" (tour_id_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_tour_recommended_tours_deleted_at", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_recommended_tours_deleted_at\" ON \"tour_recommended_tours\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "tour_recommended_tours_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"tour_recommended_tours_tour_id_id_foreign": {"constraintName": "tour_recommended_tours_tour_id_id_foreign", "columnNames": ["tour_id_id"], "localTableName": "public.tour_recommended_tours", "referencedColumnNames": ["id"], "referencedTableName": "public.tour", "deleteRule": "cascade", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "tour_id_id": {"name": "tour_id_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "title": {"name": "title", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "description": {"name": "description", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "keywords": {"name": "keywords", "type": "text[]", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "array"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "tour_seo", "schema": "public", "indexes": [{"columnNames": ["tour_id_id"], "composite": false, "keyName": "tour_seo_tour_id_id_unique", "primary": false, "unique": true}, {"keyName": "IDX_tour_seo_tour_id_id", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_seo_tour_id_id\" ON \"tour_seo\" (tour_id_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_tour_seo_deleted_at", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_seo_deleted_at\" ON \"tour_seo\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "tour_seo_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"tour_seo_tour_id_id_foreign": {"constraintName": "tour_seo_tour_id_id_foreign", "columnNames": ["tour_id_id"], "localTableName": "public.tour_seo", "referencedColumnNames": ["id"], "referencedTableName": "public.tour", "deleteRule": "cascade", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "tour_id_id": {"name": "tour_id_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "place": {"name": "place", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "month": {"name": "month", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "days": {"name": "days", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "tour_start_places_by_date", "schema": "public", "indexes": [{"keyName": "IDX_tour_start_places_by_date_tour_id_id", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_start_places_by_date_tour_id_id\" ON \"tour_start_places_by_date\" (tour_id_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_tour_start_places_by_date_deleted_at", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_start_places_by_date_deleted_at\" ON \"tour_start_places_by_date\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "tour_start_places_by_date_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"tour_start_places_by_date_tour_id_id_foreign": {"constraintName": "tour_start_places_by_date_tour_id_id_foreign", "columnNames": ["tour_id_id"], "localTableName": "public.tour_start_places_by_date", "referencedColumnNames": ["id"], "referencedTableName": "public.tour", "deleteRule": "cascade", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "tour_id_id": {"name": "tour_id_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "start": {"name": "start", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "stop": {"name": "stop", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "interval": {"name": "interval", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "use_manual_start_times": {"name": "use_manual_start_times", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "manual_start_times": {"name": "manual_start_times", "type": "text[]", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "array"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "tour_start_times", "schema": "public", "indexes": [{"columnNames": ["tour_id_id"], "composite": false, "keyName": "tour_start_times_tour_id_id_unique", "primary": false, "unique": true}, {"keyName": "IDX_tour_start_times_tour_id_id", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_start_times_tour_id_id\" ON \"tour_start_times\" (tour_id_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_tour_start_times_deleted_at", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_start_times_deleted_at\" ON \"tour_start_times\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "tour_start_times_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"tour_start_times_tour_id_id_foreign": {"constraintName": "tour_start_times_tour_id_id_foreign", "columnNames": ["tour_id_id"], "localTableName": "public.tour_start_times", "referencedColumnNames": ["id"], "referencedTableName": "public.tour", "deleteRule": "cascade", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "tour_id_id": {"name": "tour_id_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "map": {"name": "map", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "meeting_place": {"name": "meeting_place", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "meeting_time": {"name": "meeting_time", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "attraction_time": {"name": "attraction_time", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "additional_meeting_details": {"name": "additional_meeting_details", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "intermediary_name": {"name": "intermediary_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "organizer_name": {"name": "organizer_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "organizer_contact": {"name": "organizer_contact", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "ship_or_transport_description": {"name": "ship_or_transport_description", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "what_to_bring": {"name": "what_to_bring", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "where_to_park": {"name": "where_to_park", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "additional_info": {"name": "additional_info", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "link_to_download": {"name": "link_to_download", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "meeting_image": {"name": "meeting_image", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "tour_ticket", "schema": "public", "indexes": [{"columnNames": ["tour_id_id"], "composite": false, "keyName": "tour_ticket_tour_id_id_unique", "primary": false, "unique": true}, {"keyName": "IDX_tour_ticket_tour_id_id", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_ticket_tour_id_id\" ON \"tour_ticket\" (tour_id_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_tour_ticket_deleted_at", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_tour_ticket_deleted_at\" ON \"tour_ticket\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "tour_ticket_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"tour_ticket_tour_id_id_foreign": {"constraintName": "tour_ticket_tour_id_id_foreign", "columnNames": ["tour_id_id"], "localTableName": "public.tour_ticket", "referencedColumnNames": ["id"], "referencedTableName": "public.tour", "deleteRule": "cascade", "updateRule": "cascade"}}}]}