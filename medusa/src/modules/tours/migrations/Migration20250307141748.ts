import { Migration } from '@mikro-orm/migrations';

export class Migration20250307141748 extends Migration {

  async up(): Promise<void> {
    this.addSql('alter table if exists "tour_start_times" add column if not exists "use_manual_start_times" boolean not null default false, add column if not exists "manual_start_times" text[] null;');
  }

  async down(): Promise<void> {
    this.addSql('alter table if exists "tour_start_times" drop column if exists "use_manual_start_times";');
    this.addSql('alter table if exists "tour_start_times" drop column if exists "manual_start_times";');
  }

}
