import { Migration } from '@mikro-orm/migrations';

export class Migration20250212100224 extends Migration {

  async up(): Promise<void> {
    this.addSql('alter table if exists "tour_start_places_by_date" alter column "days" type text using ("days"::text);');
  }

  async down(): Promise<void> {
    this.addSql('alter table if exists "tour_start_places_by_date" alter column "days" type text[] using ("days"::text[]);');
  }

}
