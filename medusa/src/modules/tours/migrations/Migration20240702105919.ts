import { Migration } from "@mikro-orm/migrations";

export class Migration20240702105919 extends Migration {
  async up(): Promise<void> {
    // Create the new tour_available_start_places table
    this.addSql(
      `CREATE TABLE "tour_available_start_places" (
        "id" text NOT NULL,
        "tour_id_id" text NOT NULL,
        "place" text NOT NULL,
        "rich_text_content" jsonb,
        "created_at" timestamptz NOT NULL DEFAULT now(),
        "updated_at" timestamptz NOT NULL DEFAULT now(),
        "deleted_at" timestamptz,
        CONSTRAINT "tour_available_start_places_pkey" PRIMARY KEY ("id")
      )`
    );

    // Add foreign key constraint
    this.addSql(
      `ALTER TABLE "tour_available_start_places" ADD CONSTRAINT "tour_available_start_places_tour_id_foreign" 
      FOREIGN KEY ("tour_id_id") REFERENCES "tour" ("id") ON DELETE CASCADE;`
    );

    // Transfer existing data
    this.addSql(
      `INSERT INTO "tour_available_start_places" (id, tour_id_id, place, rich_text_content)
      SELECT 
        gen_random_uuid(), 
        id AS tour_id, 
        unnest(available_start_places) AS place,
        NULL AS rich_text_content
      FROM "tour"
      WHERE available_start_places IS NOT NULL AND array_length(available_start_places, 1) > 0;`
    );

    // Remove the old column
    this.addSql(`ALTER TABLE "tour" DROP COLUMN "available_start_places";`);
  }
  async down(): Promise<void> {
    // Revert changes (this is a simplified version and may not fully restore the original state)
    this.addSql(
      `ALTER TABLE "tour" ADD COLUMN "available_start_places" text[];`
    );
    this.addSql(`DROP TABLE IF EXISTS "tour_available_start_places" CASCADE;`);
  }
}
