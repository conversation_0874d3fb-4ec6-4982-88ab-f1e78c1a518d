import { Migration } from "@mikro-orm/migrations";

export class Migration20250328140448 extends Migration {
  async up(): Promise<void> {
    // Ensure the recommended_tour_ids column exists on tour table as a text array (default empty array)
    this.addSql(
      `ALTER TABLE "tour" ADD COLUMN IF NOT EXISTS "recommended_tour_ids" text[] NOT NULL DEFAULT '{}';`
    );

    // Migrate data from tour_recommended_tours to the new array field
    this.addSql(`
      WITH grouped_recommendations AS (
        SELECT 
          tour_id_id, 
          array_agg(recommended_tour_id) as tour_ids
        FROM 
          tour_recommended_tours
        WHERE 
          deleted_at IS NULL
        GROUP BY 
          tour_id_id
      )
      UPDATE 
        tour
      SET 
        recommended_tour_ids = grouped_recommendations.tour_ids
      FROM 
        grouped_recommendations
      WHERE 
        tour.id = grouped_recommendations.tour_id_id;
    `);

    // Don't drop the table here - retain it until we're sure the migration works
    // The table will be removed in a subsequent migration or during schema cleanup
  }

  async down(): Promise<void> {
    // If we need to rollback, we would restore data from recommended_tour_ids back to tour_recommended_tours
    // But this would be complex and potentially lossy, so it's better to manually handle if needed

    // Just remove the column from tour table
    this.addSql(
      `ALTER TABLE "tour" DROP COLUMN IF EXISTS "recommended_tour_ids";`
    );
  }
}
