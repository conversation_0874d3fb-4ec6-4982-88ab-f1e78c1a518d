import { Migration } from '@mikro-orm/migrations';

export class Migration20250320102950 extends Migration {

  async up(): Promise<void> {
    this.addSql('create table if not exists "tour_blocked_start_times_by_date" ("id" text not null, "tour_id_id" text not null, "month" integer not null, "days" text null, "blocked_times" text[] not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "tour_blocked_start_times_by_date_pkey" primary key ("id"));');
    this.addSql('CREATE INDEX IF NOT EXISTS "IDX_tour_blocked_start_times_by_date_tour_id_id" ON "tour_blocked_start_times_by_date" (tour_id_id) WHERE deleted_at IS NULL;');
    this.addSql('CREATE INDEX IF NOT EXISTS "IDX_tour_blocked_start_times_by_date_deleted_at" ON "tour_blocked_start_times_by_date" (deleted_at) WHERE deleted_at IS NULL;');

    this.addSql('alter table if exists "tour_blocked_start_times_by_date" add constraint "tour_blocked_start_times_by_date_tour_id_id_foreign" foreign key ("tour_id_id") references "tour" ("id") on update cascade on delete cascade;');

    this.addSql('alter table if exists "tour_available_start_places" add column if not exists "position" integer not null default 0;');
  }

  async down(): Promise<void> {
    this.addSql('drop table if exists "tour_blocked_start_times_by_date" cascade;');

    this.addSql('alter table if exists "tour_available_start_places" drop column if exists "position";');
  }

}
