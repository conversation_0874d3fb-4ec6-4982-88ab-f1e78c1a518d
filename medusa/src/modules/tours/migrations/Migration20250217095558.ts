import { Migration } from '@mikro-orm/migrations';

export class Migration20250217095558 extends Migration {

  async up(): Promise<void> {
    this.addSql('alter table if exists "tour_pricing" alter column "prepaid_percentage" type real using ("prepaid_percentage"::real);');
    this.addSql('alter table if exists "tour_pricing" alter column "prepaid_percentage_high_season" type real using ("prepaid_percentage_high_season"::real);');
  }

  async down(): Promise<void> {
    this.addSql('alter table if exists "tour_pricing" alter column "prepaid_percentage" type integer using ("prepaid_percentage"::integer);');
    this.addSql('alter table if exists "tour_pricing" alter column "prepaid_percentage_high_season" type integer using ("prepaid_percentage_high_season"::integer);');
  }

}
