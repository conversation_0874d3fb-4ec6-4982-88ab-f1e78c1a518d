import { Migration } from "@mikro-orm/migrations";

export class Migration20250212082404 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'create table if not exists "city" ("id" text not null, "name" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "city_pkey" primary key ("id"));'
    );
    this.addSql(
      'CREATE INDEX IF NOT EXISTS "IDX_city_deleted_at" ON "city" (deleted_at) WHERE deleted_at IS NULL;'
    );

    this.addSql(
      'create table if not exists "tour" ("id" text not null, "is_active" boolean not null default true, "name" text not null, "slug" text not null, "product_pricing_variant" text check ("product_pricing_variant" in (\'dynamic_age_groups\', \'regular_variants\', \'boat_rental\')) not null default \'dynamic_age_groups\', "description" text null, "featured_image" text null, "organizator_mail" text null, "price_from" text null, "dates_from_to" text null, "hourly_length" text null, "gallery" text[] null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "tour_pkey" primary key ("id"));'
    );
    this.addSql(
      'CREATE INDEX IF NOT EXISTS "IDX_tour_deleted_at" ON "tour" (deleted_at) WHERE deleted_at IS NULL;'
    );

    this.addSql(
      'create table if not exists "city_tours" ("city_id" text not null, "tour_id" text not null, constraint "city_tours_pkey" primary key ("city_id", "tour_id"));'
    );

    this.addSql(
      'create table if not exists "tour_blocked_dates" ("id" text not null, "tour_id_id" text not null, "date_from" timestamptz null, "date_to" timestamptz null, "only_one_day" boolean not null default false, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "tour_blocked_dates_pkey" primary key ("id"));'
    );
    this.addSql(
      'CREATE INDEX IF NOT EXISTS "IDX_tour_blocked_dates_tour_id_id" ON "tour_blocked_dates" (tour_id_id) WHERE deleted_at IS NULL;'
    );
    this.addSql(
      'CREATE INDEX IF NOT EXISTS "IDX_tour_blocked_dates_deleted_at" ON "tour_blocked_dates" (deleted_at) WHERE deleted_at IS NULL;'
    );

    this.addSql(
      'create table if not exists "tour_category" ("id" text not null, "name" text not null, "image" text not null, "slug" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "tour_category_pkey" primary key ("id"));'
    );
    this.addSql(
      'CREATE INDEX IF NOT EXISTS "IDX_tour_category_deleted_at" ON "tour_category" (deleted_at) WHERE deleted_at IS NULL;'
    );

    this.addSql(
      'create table if not exists "tour_tour_categories" ("tour_id" text not null, "tour_category_id" text not null, constraint "tour_tour_categories_pkey" primary key ("tour_id", "tour_category_id"));'
    );

    this.addSql(
      'create table if not exists "tour_content_block" ("id" text not null, "tour_id_id" text not null, "description" jsonb null, "calendar" jsonb null, "price" jsonb null, "how_to_book" jsonb null, "ticket" jsonb null, "specification_only_for_boats" jsonb null, "program" jsonb null, "details" jsonb null, "place" jsonb null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "tour_content_block_pkey" primary key ("id"));'
    );
    this.addSql(
      'alter table if exists "tour_content_block" add constraint "tour_content_block_tour_id_id_unique" unique ("tour_id_id");'
    );
    this.addSql(
      'CREATE INDEX IF NOT EXISTS "IDX_tour_content_block_tour_id_id" ON "tour_content_block" (tour_id_id) WHERE deleted_at IS NULL;'
    );
    this.addSql(
      'CREATE INDEX IF NOT EXISTS "IDX_tour_content_block_deleted_at" ON "tour_content_block" (deleted_at) WHERE deleted_at IS NULL;'
    );

    this.addSql(
      'create table if not exists "tour_faq" ("id" text not null, "content_block_id_id" text not null, "question" text not null, "answer" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "tour_faq_pkey" primary key ("id"));'
    );
    this.addSql(
      'CREATE INDEX IF NOT EXISTS "IDX_tour_faq_content_block_id_id" ON "tour_faq" (content_block_id_id) WHERE deleted_at IS NULL;'
    );
    this.addSql(
      'CREATE INDEX IF NOT EXISTS "IDX_tour_faq_deleted_at" ON "tour_faq" (deleted_at) WHERE deleted_at IS NULL;'
    );

    this.addSql(
      'create table if not exists "tour_food_options" ("id" text not null, "tour_id_id" text not null, "name" text not null, "description" text not null, "price" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "tour_food_options_pkey" primary key ("id"));'
    );
    this.addSql(
      'CREATE INDEX IF NOT EXISTS "IDX_tour_food_options_tour_id_id" ON "tour_food_options" (tour_id_id) WHERE deleted_at IS NULL;'
    );
    this.addSql(
      'CREATE INDEX IF NOT EXISTS "IDX_tour_food_options_deleted_at" ON "tour_food_options" (deleted_at) WHERE deleted_at IS NULL;'
    );

    this.addSql(
      'create table if not exists "tour_pricing" ("id" text not null, "tour_id_id" text not null, "prepaid_percentage" integer null, "prepaid_percentage_high_season" integer null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "tour_pricing_pkey" primary key ("id"));'
    );
    this.addSql(
      'alter table if exists "tour_pricing" add constraint "tour_pricing_tour_id_id_unique" unique ("tour_id_id");'
    );
    this.addSql(
      'CREATE INDEX IF NOT EXISTS "IDX_tour_pricing_tour_id_id" ON "tour_pricing" (tour_id_id) WHERE deleted_at IS NULL;'
    );
    this.addSql(
      'CREATE INDEX IF NOT EXISTS "IDX_tour_pricing_deleted_at" ON "tour_pricing" (deleted_at) WHERE deleted_at IS NULL;'
    );

    this.addSql(
      'create table if not exists "tour_recommended_tours" ("id" text not null, "tour_id_id" text not null, "recommended_tour_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "tour_recommended_tours_pkey" primary key ("id"));'
    );
    this.addSql(
      'CREATE INDEX IF NOT EXISTS "IDX_tour_recommended_tours_tour_id_id" ON "tour_recommended_tours" (tour_id_id) WHERE deleted_at IS NULL;'
    );
    this.addSql(
      'CREATE INDEX IF NOT EXISTS "IDX_tour_recommended_tours_deleted_at" ON "tour_recommended_tours" (deleted_at) WHERE deleted_at IS NULL;'
    );

    this.addSql(
      'create table if not exists "tour_seo" ("id" text not null, "tour_id_id" text not null, "title" text null, "description" text null, "keywords" text[] null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "tour_seo_pkey" primary key ("id"));'
    );
    this.addSql(
      'alter table if exists "tour_seo" add constraint "tour_seo_tour_id_id_unique" unique ("tour_id_id");'
    );
    this.addSql(
      'CREATE INDEX IF NOT EXISTS "IDX_tour_seo_tour_id_id" ON "tour_seo" (tour_id_id) WHERE deleted_at IS NULL;'
    );
    this.addSql(
      'CREATE INDEX IF NOT EXISTS "IDX_tour_seo_deleted_at" ON "tour_seo" (deleted_at) WHERE deleted_at IS NULL;'
    );

    this.addSql(
      'create table if not exists "tour_ticket" ("id" text not null, "tour_id_id" text not null, "map" text null, "meeting_place" jsonb null, "meeting_time" jsonb null, "additional_meeting_details" jsonb null, "intermediary_name" text null, "organizer_name" text null, "organizer_contact" text null, "ship_or_transport_description" text null, "what_to_bring" text null, "where_to_park" text null, "additional_info" jsonb null, "link_to_download" text null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "tour_ticket_pkey" primary key ("id"));'
    );
    this.addSql(
      'alter table if exists "tour_ticket" add constraint "tour_ticket_tour_id_id_unique" unique ("tour_id_id");'
    );
    this.addSql(
      'CREATE INDEX IF NOT EXISTS "IDX_tour_ticket_tour_id_id" ON "tour_ticket" (tour_id_id) WHERE deleted_at IS NULL;'
    );
    this.addSql(
      'CREATE INDEX IF NOT EXISTS "IDX_tour_ticket_deleted_at" ON "tour_ticket" (deleted_at) WHERE deleted_at IS NULL;'
    );

    this.addSql(
      'create table if not exists "tour_ticket_start_places" ("id" text not null, "tour_id_id" text not null, "place" text not null, "date_active_from" timestamptz not null, "date_active_to" timestamptz not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "tour_ticket_start_places_pkey" primary key ("id"));'
    );
    this.addSql(
      'alter table if exists "tour_ticket_start_places" add constraint "tour_ticket_start_places_tour_id_id_unique" unique ("tour_id_id");'
    );
    this.addSql(
      'CREATE INDEX IF NOT EXISTS "IDX_tour_ticket_start_places_tour_id_id" ON "tour_ticket_start_places" (tour_id_id) WHERE deleted_at IS NULL;'
    );
    this.addSql(
      'CREATE INDEX IF NOT EXISTS "IDX_tour_ticket_start_places_deleted_at" ON "tour_ticket_start_places" (deleted_at) WHERE deleted_at IS NULL;'
    );

    this.addSql(
      'create table if not exists "tour_ticket_start_times" ("id" text not null, "tour_id_id" text not null, "start" text not null, "stop" text not null, "interval" integer not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "tour_ticket_start_times_pkey" primary key ("id"));'
    );
    this.addSql(
      'alter table if exists "tour_ticket_start_times" add constraint "tour_ticket_start_times_tour_id_id_unique" unique ("tour_id_id");'
    );
    this.addSql(
      'CREATE INDEX IF NOT EXISTS "IDX_tour_ticket_start_times_tour_id_id" ON "tour_ticket_start_times" (tour_id_id) WHERE deleted_at IS NULL;'
    );
    this.addSql(
      'CREATE INDEX IF NOT EXISTS "IDX_tour_ticket_start_times_deleted_at" ON "tour_ticket_start_times" (deleted_at) WHERE deleted_at IS NULL;'
    );

    this.addSql(
      'alter table if exists "city_tours" add constraint "city_tours_city_id_foreign" foreign key ("city_id") references "city" ("id") on update cascade on delete cascade;'
    );
    this.addSql(
      'alter table if exists "city_tours" add constraint "city_tours_tour_id_foreign" foreign key ("tour_id") references "tour" ("id") on update cascade on delete cascade;'
    );

    this.addSql(
      'alter table if exists "tour_blocked_dates" add constraint "tour_blocked_dates_tour_id_id_foreign" foreign key ("tour_id_id") references "tour" ("id") on update cascade on delete cascade;'
    );

    this.addSql(
      'alter table if exists "tour_tour_categories" add constraint "tour_tour_categories_tour_id_foreign" foreign key ("tour_id") references "tour" ("id") on update cascade on delete cascade;'
    );
    this.addSql(
      'alter table if exists "tour_tour_categories" add constraint "tour_tour_categories_tour_category_id_foreign" foreign key ("tour_category_id") references "tour_category" ("id") on update cascade on delete cascade;'
    );

    this.addSql(
      'alter table if exists "tour_content_block" add constraint "tour_content_block_tour_id_id_foreign" foreign key ("tour_id_id") references "tour" ("id") on update cascade on delete cascade;'
    );

    this.addSql(
      'alter table if exists "tour_faq" add constraint "tour_faq_content_block_id_id_foreign" foreign key ("content_block_id_id") references "tour_content_block" ("id") on update cascade on delete cascade;'
    );

    this.addSql(
      'alter table if exists "tour_food_options" add constraint "tour_food_options_tour_id_id_foreign" foreign key ("tour_id_id") references "tour" ("id") on update cascade on delete cascade;'
    );

    this.addSql(
      'alter table if exists "tour_pricing" add constraint "tour_pricing_tour_id_id_foreign" foreign key ("tour_id_id") references "tour" ("id") on update cascade on delete cascade;'
    );

    this.addSql(
      'alter table if exists "tour_recommended_tours" add constraint "tour_recommended_tours_tour_id_id_foreign" foreign key ("tour_id_id") references "tour" ("id") on update cascade on delete cascade;'
    );

    this.addSql(
      'alter table if exists "tour_seo" add constraint "tour_seo_tour_id_id_foreign" foreign key ("tour_id_id") references "tour" ("id") on update cascade on delete cascade;'
    );

    this.addSql(
      'alter table if exists "tour_ticket" add constraint "tour_ticket_tour_id_id_foreign" foreign key ("tour_id_id") references "tour" ("id") on update cascade on delete cascade;'
    );

    this.addSql(
      'alter table if exists "tour_ticket_start_places" add constraint "tour_ticket_start_places_tour_id_id_foreign" foreign key ("tour_id_id") references "tour" ("id") on update cascade on delete cascade;'
    );

    this.addSql(
      'alter table if exists "tour_ticket_start_times" add constraint "tour_ticket_start_times_tour_id_id_foreign" foreign key ("tour_id_id") references "tour" ("id") on update cascade on delete cascade;'
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table if exists "city_tours" drop constraint if exists "city_tours_city_id_foreign";'
    );

    this.addSql(
      'alter table if exists "city_tours" drop constraint if exists "city_tours_tour_id_foreign";'
    );

    this.addSql(
      'alter table if exists "tour_blocked_dates" drop constraint if exists "tour_blocked_dates_tour_id_id_foreign";'
    );

    this.addSql(
      'alter table if exists "tour_tour_categories" drop constraint if exists "tour_tour_categories_tour_id_foreign";'
    );

    this.addSql(
      'alter table if exists "tour_content_block" drop constraint if exists "tour_content_block_tour_id_id_foreign";'
    );

    this.addSql(
      'alter table if exists "tour_food_options" drop constraint if exists "tour_food_options_tour_id_id_foreign";'
    );

    this.addSql(
      'alter table if exists "tour_pricing" drop constraint if exists "tour_pricing_tour_id_id_foreign";'
    );

    this.addSql(
      'alter table if exists "tour_recommended_tours" drop constraint if exists "tour_recommended_tours_tour_id_id_foreign";'
    );

    this.addSql(
      'alter table if exists "tour_seo" drop constraint if exists "tour_seo_tour_id_id_foreign";'
    );

    this.addSql(
      'alter table if exists "tour_ticket" drop constraint if exists "tour_ticket_tour_id_id_foreign";'
    );

    this.addSql(
      'alter table if exists "tour_ticket_start_places" drop constraint if exists "tour_ticket_start_places_tour_id_id_foreign";'
    );

    this.addSql(
      'alter table if exists "tour_ticket_start_times" drop constraint if exists "tour_ticket_start_times_tour_id_id_foreign";'
    );

    this.addSql(
      'alter table if exists "tour_tour_categories" drop constraint if exists "tour_tour_categories_tour_category_id_foreign";'
    );

    this.addSql(
      'alter table if exists "tour_faq" drop constraint if exists "tour_faq_content_block_id_id_foreign";'
    );

    this.addSql('drop table if exists "city" cascade;');

    this.addSql('drop table if exists "tour" cascade;');

    this.addSql('drop table if exists "city_tours" cascade;');

    this.addSql('drop table if exists "tour_blocked_dates" cascade;');

    this.addSql('drop table if exists "tour_category" cascade;');

    this.addSql('drop table if exists "tour_tour_categories" cascade;');

    this.addSql('drop table if exists "tour_content_block" cascade;');

    this.addSql('drop table if exists "tour_faq" cascade;');

    this.addSql('drop table if exists "tour_food_options" cascade;');

    this.addSql('drop table if exists "tour_pricing" cascade;');

    this.addSql('drop table if exists "tour_recommended_tours" cascade;');

    this.addSql('drop table if exists "tour_seo" cascade;');

    this.addSql('drop table if exists "tour_ticket" cascade;');

    this.addSql('drop table if exists "tour_ticket_start_places" cascade;');

    this.addSql('drop table if exists "tour_ticket_start_times" cascade;');
  }
}
