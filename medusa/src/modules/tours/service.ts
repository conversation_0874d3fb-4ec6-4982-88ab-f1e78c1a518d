import {
  InjectTransaction<PERSON>anager,
  MedusaContext,
  MedusaService,
} from "@medusajs/framework/utils";
import {
  Tour,
  TourContentBlock,
  TourTicket,
  TourPricing,
  TourSeo,
  TourBlockedDates,
  TourFoodOptions,
  TourFaq,
  TourStartTimes,
  TourStartPlacesByDate,
  TourBlockedDatesByMonth,
  TourAvailableStartPlaces,
  TourBlockedStartTimesByDate,
} from "./models/tour";

import { EntityManager } from "@mikro-orm/knex";
import { InjectManager } from "@medusajs/framework/utils";

import { City } from "./models/city";
import { Context } from "@medusajs/framework/types";

type UpdateTourPositionInput = {
  id: string;
  position: number;
};

type UpdateTourFaqPositionInput = {
  tourId: string;
  updates: {
    id: string;
    position: number;
  }[];
};

// Define the Tour entity type
type TourEntity = {
  id: string;
  position: number;
  is_active: boolean;
  name: string;
  slug: string;
  description?: string;
  featured_image?: string;
  price_from?: string;
  promotion_price_from?: string;
  hourly_length?: string;
  dates_from_to?: string;
  is_recommended?: boolean;
  is_bargain?: boolean;
  is_promotion?: boolean;
  cities: {
    id: string;
    name: string;
  }[];
  content_blocks?: any;
  tour_seo?: any;
};

class TourService extends MedusaService({
  Tour,
  TourContentBlock,
  TourTicket,
  TourPricing,
  TourSeo,
  TourBlockedDates,
  TourFoodOptions,
  TourFaq,
  TourStartTimes,
  TourStartPlacesByDate,
  TourBlockedDatesByMonth,
  City,
  TourAvailableStartPlaces,
  TourBlockedStartTimesByDate,
}) {
  @InjectTransactionManager()
  protected async updateTourPositions_(
    updates: UpdateTourPositionInput[],
    @MedusaContext() sharedContext?: Context<EntityManager>
  ): Promise<void> {
    const transactionManager = sharedContext?.transactionManager;

    if (!transactionManager) {
      throw new Error("Transaction manager not found");
    }

    for (const update of updates) {
      await transactionManager.nativeUpdate(
        "tour",
        { id: update.id },
        { position: update.position }
      );
    }
  }

  @InjectTransactionManager()
  protected async returnMatchingToursByQuery_(
    query: string,
    @MedusaContext() sharedContext?: Context<EntityManager>
  ): Promise<TourEntity[]> {
    const transactionManager = sharedContext?.transactionManager;

    if (!transactionManager) {
      throw new Error("Transaction manager not found");
    }

    // Function to normalize text by removing diacritics (Polish and Croatian characters)
    const normalizeText = (text: string): string => {
      return (
        text
          .toLowerCase()
          .normalize("NFD")
          .replace(/[\u0300-\u036f]/g, "") // Remove combining diacritical marks
          // Additional replacements for specific Polish and Croatian characters
          .replace(/ą/g, "a")
          .replace(/ć/g, "c")
          .replace(/ę/g, "e")
          .replace(/ł/g, "l")
          .replace(/ń/g, "n")
          .replace(/ó/g, "o")
          .replace(/ś/g, "s")
          .replace(/ź/g, "z")
          .replace(/ż/g, "z")
          .replace(/č/g, "c")
          .replace(/ć/g, "c")
          .replace(/đ/g, "d")
          .replace(/š/g, "s")
          .replace(/ž/g, "z")
      );
    };

    const normalizedQuery = normalizeText(query);

    // First, get the matching tours
    const toursResult = await transactionManager.execute(
      `
      SELECT DISTINCT t.* FROM tour t
      WHERE 
        t.is_active IS TRUE AND t.deleted_at IS NULL AND (
          LOWER(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
            t.name, 'ą', 'a'), 'ć', 'c'), 'ę', 'e'), 'ł', 'l'), 'ń', 'n'), 'ó', 'o'), 'ś', 's'), 'ź', 'z'), 'ż', 'z'), 'č', 'c'), 'đ', 'd'), 'š', 's'), 'ž', 'z'), 'ć', 'c')) LIKE ?
          OR LOWER(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
            COALESCE(t.description, ''), 'ą', 'a'), 'ć', 'c'), 'ę', 'e'), 'ł', 'l'), 'ń', 'n'), 'ó', 'o'), 'ś', 's'), 'ź', 'z'), 'ż', 'z'), 'č', 'c'), 'đ', 'd'), 'š', 's'), 'ž', 'z'), 'ć', 'c')) LIKE ?
        )
      ORDER BY t.position ASC, t.name ASC
    `,
      [`%${normalizedQuery}%`, `%${normalizedQuery}%`]
    );

    // If no tours found, return empty array
    if (!toursResult || toursResult.length === 0) {
      return [];
    }

    // Get tour IDs for fetching cities
    const tourIds = toursResult.map((tour: any) => tour.id);

    // Get cities for all tours in one query
    const citiesResult = await transactionManager.execute(
      `
      SELECT 
        ct.tour_id,
        c.id,
        c.name
      FROM city_tours ct
      JOIN city c ON ct.city_id = c.id
      WHERE ct.tour_id IN (${tourIds.map(() => "?").join(",")})
      ORDER BY ct.tour_id, c.name
    `,
      tourIds
    );

    // Group cities by tour_id
    const citiesByTourId: { [key: string]: any[] } = {};
    citiesResult.forEach((cityRow: any) => {
      if (!citiesByTourId[cityRow.tour_id]) {
        citiesByTourId[cityRow.tour_id] = [];
      }
      citiesByTourId[cityRow.tour_id].push({
        id: cityRow.id,
        name: cityRow.name,
      });
    });

    // Combine tours with their cities
    const toursWithCities = toursResult.map((tour: any) => ({
      ...tour,
      cities: citiesByTourId[tour.id] || [],
    }));

    console.log(toursWithCities.filter((tour) => !tour.is_active));

    return toursWithCities as TourEntity[];
  }

  @InjectManager()
  async updateTourPositions(
    updates: UpdateTourPositionInput[],
    @MedusaContext() sharedContext?: Context<EntityManager>
  ): Promise<void> {
    await this.updateTourPositions_(updates, sharedContext);
  }

  @InjectManager()
  async searchToursByQuery(
    query: string,
    @MedusaContext() sharedContext?: Context<EntityManager>
  ): Promise<TourEntity[]> {
    return await this.returnMatchingToursByQuery_(query, sharedContext);
  }
}

export default TourService;
