import { model } from "@medusajs/framework/utils";
import { City } from "./city";
import { productTypes } from "src/admin/components/tours/schemas";

export const Tour = model
  .define("tour", {
    id: model.id().primaryKey(),
    position: model.number().default(0),
    is_active: model.boolean().default(true),
    name: model.text(),
    slug: model.text(),
    product_pricing_variant: model
      .enum(productTypes.Enum)
      .default("dynamic_age_groups"),
    description: model.text().nullable(),
    featured_image: model.text().nullable(),
    organizator_mail: model.text().nullable(),
    price_from: model.text().nullable(),
    dates_from_to: model.text().nullable(),
    hourly_length: model.text().nullable(),
    gallery: model.array().nullable(),
    cart_section_image: model.text().nullable(),
    at_least_one_food_option_required: model.boolean().default(false),
    recommended_tour_ids: model.array().default([]),
    is_recommended: model.boolean().default(false),
    is_bargain: model.boolean().default(false),
    is_promotion: model.boolean().default(false),
    promotion_price_from: model.text().nullable(),
    available_start_places: model.hasMany(() => TourAvailableStartPlaces, {
      mappedBy: "tour_id",
    }),
    start_times: model
      .hasOne(() => TourStartTimes, {
        mappedBy: "tour_id",
      })
      .nullable(),
    start_places_by_date: model.hasMany(() => TourStartPlacesByDate, {
      mappedBy: "tour_id",
    }),
    blocked_dates_by_month: model.hasMany(() => TourBlockedDatesByMonth, {
      mappedBy: "tour_id",
    }),
    blocked_start_times: model.hasMany(() => TourBlockedStartTimesByDate, {
      mappedBy: "tour_id",
    }),
    content_blocks: model.hasOne(() => TourContentBlock, {
      mappedBy: "tour_id",
    }),
    food_options: model.hasMany(() => TourFoodOptions, {
      mappedBy: "tour_id",
    }),
    blocked_dates: model.hasMany(() => TourBlockedDates, {
      mappedBy: "tour_id",
    }),
    tour_seo: model.hasOne(() => TourSeo, {
      mappedBy: "tour_id",
    }),
    pricing: model.hasOne(() => TourPricing, {
      mappedBy: "tour_id",
    }),
    ticket: model.hasOne(() => TourTicket, {
      mappedBy: "tour_id",
    }),
    cities: model.manyToMany(() => City, {
      mappedBy: "tours",
    }),
  })
  .cascades({
    delete: [
      "blocked_dates",
      "content_blocks",
      "food_options",
      "pricing",
      "ticket",
      "tour_seo",
      "start_times",
      "start_places_by_date",
      "available_start_places",
      "blocked_start_times",
    ],
  });

export const TourAvailableStartPlaces = model.define(
  "tour_available_start_places",
  {
    id: model.id().primaryKey(),
    tour_id: model.belongsTo(() => Tour, {
      mappedBy: "available_start_places",
    }),
    place: model.text(),
    rich_text_content: model.json().nullable(),
    position: model.number().default(0),
  }
);

export const TourContentBlock = model
  .define("tour_content_block", {
    id: model.id().primaryKey(),
    tour_id: model.belongsTo(() => Tour, {
      mappedBy: "content_blocks",
    }),
    description: model.json().nullable(),
    calendar: model.json().nullable(),
    price: model.json().nullable(),
    how_to_book: model.json().nullable(),
    ticket: model.json().nullable(),
    specification_only_for_boats: model.json().nullable(),
    program: model.json().nullable(),
    details: model.json().nullable(),
    place: model.json().nullable(),
    faq: model.hasMany(() => TourFaq, {
      mappedBy: "content_block_id",
    }),
  })
  .cascades({
    delete: ["faq"],
  });

export const TourFaq = model.define("tour_faq", {
  id: model.id().primaryKey(),
  content_block_id: model.belongsTo(() => TourContentBlock, {
    mappedBy: "faq",
  }),
  question: model.text(),
  answer: model.text(),
  position: model.number().default(0),
});

export const TourFoodOptions = model.define("tour_food_options", {
  id: model.id().primaryKey(),
  tour_id: model.belongsTo(() => Tour, {
    mappedBy: "food_options",
  }),
  name: model.text(),
  description: model.text(),
  price: model.text(),
  required: model.boolean().default(false),
});

export const TourBlockedDatesByMonth = model.define(
  "tour_blocked_dates_by_month",
  {
    id: model.id().primaryKey(),
    tour_id: model.belongsTo(() => Tour, {
      mappedBy: "blocked_dates_by_month",
    }),
    month: model.number(),
    days: model.text().nullable(),
  }
);

export const TourBlockedDates = model.define("tour_blocked_dates", {
  id: model.id().primaryKey(),
  tour_id: model.belongsTo(() => Tour, {
    mappedBy: "blocked_dates",
  }),
  date_from: model.dateTime().nullable(),
  date_to: model.dateTime().nullable(),
  only_one_day: model.boolean().default(false),
});

export const TourSeo = model.define("tour_seo", {
  id: model.id().primaryKey(),
  tour_id: model.belongsTo(() => Tour, {
    mappedBy: "tour_seo",
  }),
  title: model.text().nullable(),
  description: model.text().nullable(),
  keywords: model.array().nullable(),
});

export const TourPricing = model.define("tour_pricing", {
  id: model.id().primaryKey(),
  tour_id: model.belongsTo(() => Tour, {
    mappedBy: "pricing",
  }),
  prepaid_percentage: model.float().nullable(),
  prepaid_percentage_high_season: model.float().nullable(),
});

export const TourTicket = model.define("tour_ticket", {
  id: model.id().primaryKey(),
  tour_id: model.belongsTo(() => Tour, {
    mappedBy: "ticket",
  }),
  map: model.text().nullable(),
  meeting_place: model.json().nullable(),
  meeting_time: model.json().nullable(),
  attraction_time: model.json().nullable(),
  additional_meeting_details: model.json().nullable(),
  intermediary_name: model.text().nullable(),
  organizer_name: model.text().nullable(),
  organizer_contact: model.text().nullable(),
  ship_or_transport_description: model.text().nullable(),
  what_to_bring: model.text().nullable(),
  where_to_park: model.text().nullable(),
  additional_info: model.json().nullable(),
  link_to_download: model.text().nullable(),

  meeting_image: model.text().nullable(),
});

export const TourStartTimes = model.define("tour_start_times", {
  id: model.id().primaryKey(),
  tour_id: model.belongsTo(() => Tour, {
    mappedBy: "start_times",
  }),
  start: model.text(),
  stop: model.text(),
  interval: model.number().nullable(),
  use_manual_start_times: model.boolean().default(false),
  manual_start_times: model.array().nullable(),
});

export const TourStartPlacesByDate = model.define("tour_start_places_by_date", {
  id: model.id().primaryKey(),
  tour_id: model.belongsTo(() => Tour, {
    mappedBy: "start_places_by_date",
  }),
  place: model.text(),
  month: model.number(),
  days: model.text().nullable(),
});

export const TourBlockedStartTimesByDate = model.define(
  "tour_blocked_start_times_by_date",
  {
    id: model.id().primaryKey(),
    tour_id: model.belongsTo(() => Tour, {
      mappedBy: "blocked_start_times",
    }),
    month: model.number(),
    days: model.text().nullable(),
    blocked_times: model.array(),
  }
);
