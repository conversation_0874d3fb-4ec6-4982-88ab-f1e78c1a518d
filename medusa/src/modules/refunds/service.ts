import {
  ContainerRegistration<PERSON><PERSON><PERSON>,
  InjectTransactionManager,
  MedusaContext,
  MedusaService,
} from "@medusajs/framework/utils";
import { EntityManager } from "@mikro-orm/knex";
import { InjectManager } from "@medusajs/framework/utils";
import { Context } from "@medusajs/framework/types";
import { container } from "@medusajs/framework";
import { updatePaymentCollectionAfterOrderLineItemChangeWorkflow } from "src/workflows/update-payment-collection-after-order-line-item-change-workflow";
import { ORDER_SUMMARY_MODULE } from "src/modules/order-summary";
import { Refund } from "./models/refund";
import { ORDER_TRANSACTION_MODULE } from "../order-transaction";
import OrderTransactionService from "../order-transaction/service";

export type RefundRecord = {
  id: string;
  amount: number;
  currency_code: string;
  reason?: string;
  created_at: string;
  reference_id?: string;
  order_id: string;
};

type CancelRefundInput = {
  orderId: string;
  refundId: string;
};

type RetrieveRefundsInput = {
  orderId: string;
};

class RefundsService extends MedusaService({
  Refund,
}) {
  @InjectTransactionManager()
  protected async cancelRefund_(
    input: CancelRefundInput,
    @MedusaContext() sharedContext?: Context<EntityManager>
  ): Promise<{
    success: boolean;
    message: string;
    originalRefund?: RefundRecord;
    revertedAmount?: number;
    summaryUpdates?: any;
    error?: string;
  }> {
    const transactionManager = sharedContext?.transactionManager;

    if (!transactionManager) {
      throw new Error("Transaction manager not found");
    }

    const { orderId, refundId } = input;
    const query = container.resolve(ContainerRegistrationKeys.QUERY);

    try {
      // Get the refund transaction to verify it exists and is a refund
      const { data: transactions } = await query.graph({
        entity: "order_transaction",
        fields: ["*"],
        filters: {
          id: refundId,
          order_id: orderId,
        },
      });

      if (!transactions || transactions.length === 0) {
        return {
          success: false,
          error: "Refund not found",
          message: "Refund not found",
        };
      }

      const refundTransaction = transactions[0];

      // Verify it's actually a refund (negative amount)
      if (refundTransaction.amount >= 0) {
        return {
          success: false,
          error: "Transaction is not a refund",
          message: "Transaction is not a refund",
        };
      }

      const refundAmount = Math.abs(refundTransaction.amount);

      // Get current order summary
      const { data: orderData } = await query.graph({
        entity: "order",
        fields: ["*", "ticket.id", "summary.*", "payment_collections.*"],
        filters: {
          id: orderId,
        },
      });

      if (!orderData || orderData.length === 0) {
        throw new Error("Order not found");
      }

      const order = orderData[0];
      const currentSummary = order.summary;

      if (!currentSummary) {
        throw new Error("Order summary not found");
      }

      const orderTransactionService: OrderTransactionService =
        container.resolve(ORDER_TRANSACTION_MODULE);

      await orderTransactionService.markOrderTransactionAsRevoked({
        orderTransactionId: refundTransaction.id,
      });

      // Store initial summary state
      const initialSummary = {
        refunded_total: (currentSummary as any).refunded_total || 0,
        transaction_total: (currentSummary as any).transaction_total || 0,
        pending_difference: (currentSummary as any).pending_difference || 0,
        paid_total: (currentSummary as any).paid_total || 0,
      };

      const { data: paymentData } = await query.graph({
        entity: "payment",
        fields: ["*"],
        filters: {
          payment_collection_id: order.payment_collections?.[0]?.id,
        },
      });

      if (!paymentData || paymentData.length === 0) {
        throw new Error("Payment not found for order");
      }

      const payment = paymentData[0];

      // Step 1: Issue a "negative refund" using the workflow
      await updatePaymentCollectionAfterOrderLineItemChangeWorkflow.run({
        container: container,
        input: {
          payment_id: payment.id,
          calculated_refund_amount: -refundAmount,
          order_id: orderId,
        },
        logOnError: true,
      });

      // Get updated summary after workflow
      const { data: orderDataAfterWorkflow } = await query.graph({
        entity: "order",
        fields: ["*", "summary.*"],
        filters: {
          id: orderId,
        },
      });

      const summaryAfterWorkflow = orderDataAfterWorkflow[0]?.summary;
      const workflowTransactionTotal =
        (summaryAfterWorkflow as any)?.transaction_total ||
        initialSummary.transaction_total;

      // Step 2: Update the OrderSummary
      const orderSummaryService = container.resolve(ORDER_SUMMARY_MODULE);

      const currentRefundedTotal = initialSummary.refunded_total;
      const currentPendingDifference = initialSummary.pending_difference;

      const newRefundedTotal = Math.max(0, currentRefundedTotal - refundAmount);
      const newTransactionTotal = workflowTransactionTotal;
      const newPaidTotal = initialSummary.paid_total;
      const newPendingDifference = currentPendingDifference - refundAmount;

      await orderSummaryService.updateOrderSummary({
        orderId: orderId,
        refunded_total: newRefundedTotal,
        transaction_total: newTransactionTotal,
        pending_difference: newPendingDifference,
        paid_total: newPaidTotal,
      });

      return {
        success: true,
        message: "Refund successfully revoked",
        originalRefund: {
          id: refundTransaction.id,
          amount: Math.abs(refundTransaction.amount),
          currency_code: refundTransaction.currency_code,
          created_at: refundTransaction.created_at.toString(),
          reference_id: refundTransaction.reference_id,
          order_id: refundTransaction.order_id,
        },
        revertedAmount: refundAmount,
        summaryUpdates: {
          oldRefundedTotal: initialSummary.refunded_total,
          newRefundedTotal: newRefundedTotal,
          oldTransactionTotal: initialSummary.transaction_total,
          newTransactionTotal: workflowTransactionTotal,
          oldPendingDifference: initialSummary.pending_difference,
          newPendingDifference: newPendingDifference,
          oldPaidTotal: initialSummary.paid_total,
          newPaidTotal: initialSummary.paid_total,
        },
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        message: `Failed to revoke refund: ${error.message}`,
      };
    }
  }

  @InjectManager()
  async cancelRefund(
    input: CancelRefundInput,
    @MedusaContext() sharedContext?: Context<EntityManager>
  ): Promise<{
    success: boolean;
    message: string;
    originalRefund?: RefundRecord;
    revertedAmount?: number;
    summaryUpdates?: any;
    error?: string;
  }> {
    return await this.cancelRefund_(input, sharedContext);
  }

  async retrieveRefunds(input: RetrieveRefundsInput): Promise<{
    refunds: RefundRecord[];
    total: number;
  }> {
    const { orderId } = input;
    const query = container.resolve(ContainerRegistrationKeys.QUERY);

    try {
      // Get all transactions for the order
      const { data: transactions } = await query.graph({
        entity: "order_transaction",
        fields: ["*"],
        filters: {
          order_id: orderId,
          reference: "refund",
        },
      });

      // Filter for refunds (negative amounts) and exclude revoked ones
      const refunds = transactions
        .filter((transaction: any) => transaction.amount < 0)
        .map((transaction: any) => ({
          id: transaction.id,
          amount: Math.abs(transaction.amount),
          currency_code: transaction.currency_code,
          created_at: transaction.created_at,
          reference_id: transaction.reference_id,
          order_id: transaction.order_id,
        }))
        .sort(
          (a: RefundRecord, b: RefundRecord) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );

      return {
        refunds,
        total: refunds.length,
      };
    } catch (error: any) {
      console.error("Error retrieving refunds:", error);
      return {
        refunds: [],
        total: 0,
      };
    }
  }
}

export default RefundsService;
