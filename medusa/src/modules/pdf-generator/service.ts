import puppeteer from "puppeteer";

type ModuleOptions = {
  templatePath?: string;
  outputPath?: string;
};

class PdfGeneratorService {
  private options: ModuleOptions;

  constructor(options: ModuleOptions) {
    this.options = options || {};
  }

  async generatePDFfromHTML(htmlContent: string, outputPath?: string) {
    const browser = await puppeteer.launch({
      headless: true,
      env: {
        TZ: "Europe/Warsaw",
        ...process.env,
      },
      args: ["--no-sandbox"],
    });
    const page = await browser.newPage();
    await page.setContent(htmlContent);

    const pdfBuffer = await page.pdf({
      path: outputPath,
      format: "A4",
      margin: {
        top: "0px",
        right: "0px",
        bottom: "0px",
        left: "0px",
      },
      printBackground: true,
      displayHeaderFooter: false,
    });

    await browser.close();

    return pdfBuffer;
  }
}

export default PdfGeneratorService;
