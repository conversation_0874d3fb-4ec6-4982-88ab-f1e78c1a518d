import { InferTypeOf } from "@medusajs/framework/types";
import { Ticket } from "./models/ticket";
import { OrderWithMetadata } from "src/admin/types";

export type TicketDTO = {
  id: string;
  reservation_number: number;

  customer_name: string | null;
  customer_email: string | null;
  customer_phone: string | null;
  customer_address: string | null;
  customer_city: string | null;
  customer_zip: string | null;
  customer_country: string | null;

  map: string | null;
  meeting_place: Record<string, unknown> | null;
  meeting_time: Record<string, unknown> | null;
  attraction_time: Record<string, unknown> | null;
  additional_meeting_details: Record<string, unknown> | null;
  intermediary_name: string | null;
  organizer_name: string | null;
  organizer_contact: string | null;
  ship_or_transport_description: string | null;
  what_to_bring: string | null;
  where_to_park: string | null;
  additional_info: Record<string, unknown> | null;
  link_to_download: string | null;
  meeting_image: string | null;
  start_place: {
    id: string;
    place: string;
    rich_text_content: string | null;
  } | null;
  selected_date: string | null;
  date_from: string | null;
  date_to: string | null;
  start_time: string | null;
  selected_food_options:
    | {
        id: string;
        quantity: number;
        required: boolean;
        name: string;
      }[]
    | null;
  attraction_id: string | null;
  attraction_name: string | null;
  product_type: string | null;

  amount_paid: number;
  total_to_pay: number;

  update_reason: string | null;
  issue_refund?: boolean;
  updated_at?: string;
  created_at?: string;
  deleted_at?: string;
};

export type Ticket = InferTypeOf<typeof Ticket>;

export type EmailTemplateOrderDetails = {
  product_name: string;
  attraction_date: string;
  selected_attraction_place_title: string;
  selected_food_options: {
    name: string;
    quantity: number;
  }[];
  already_paid_amount: number;
  left_to_pay_in_cash: number;
  note?: string;
};

export type ObtainOrderDetailsForEmailResult = {
  id: string;
  metadata: OrderWithMetadata["metadata"];
  ticket: {
    attraction_name: string;
  };
  items: Array<{
    id: string;
    title: string;
    subtitle: string;
    thumbnail: string;
    variant_id: string;
    product_id: string;
    product_title: string;
    product_description: string;
    product_subtitle: string | null;
    product_type: string | null;
    product_type_id: string | null;
    product_collection: string | null;
    product_handle: string;
    variant_sku: string | null;
    variant_barcode: string | null;
    variant_title: string;
    variant_option_values: unknown | null;
    requires_shipping: boolean;
    is_discountable: boolean;
    is_tax_inclusive: boolean;
    raw_compare_at_unit_price: unknown | null;
    raw_unit_price: {
      value: string;
      precision: number;
    };
    is_custom_price: boolean;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
    compare_at_unit_price: number | null;
    unit_price: number;
    quantity: number;
    raw_quantity: {
      value: string;
      precision: number;
    };
    detail: {
      id: string;
      order_id: string;
      version: number;
      item_id: string;
      raw_unit_price: {
        value: string;
        precision: number;
      };
      raw_compare_at_unit_price: unknown | null;
      raw_quantity: {
        value: string;
        precision: number;
      };
      raw_fulfilled_quantity: {
        value: string;
        precision: number;
      };
      raw_delivered_quantity: {
        value: string;
        precision: number;
      };
      raw_shipped_quantity: {
        value: string;
        precision: number;
      };
      raw_return_requested_quantity: {
        value: string;
        precision: number;
      };
      raw_return_received_quantity: {
        value: string;
        precision: number;
      };
      raw_return_dismissed_quantity: {
        value: string;
        precision: number;
      };
      raw_written_off_quantity: {
        value: string;
        precision: number;
      };
      metadata: unknown | null;
      created_at: string;
      updated_at: string;
      deleted_at: string | null;
      unit_price: number;
      compare_at_unit_price: number | null;
      quantity: number;
      fulfilled_quantity: number;
      delivered_quantity: number;
      shipped_quantity: number;
      return_requested_quantity: number;
      return_received_quantity: number;
      return_dismissed_quantity: number;
      written_off_quantity: number;
    };
    product: {
      title: string;
      id: string;
      tour: {
        id: string;
        position: number;
        is_active: boolean;
        name: string;
        slug: string;
        product_pricing_variant: string;
        description: string;
        featured_image: string;
        organizator_mail: string;
        price_from: string;
        dates_from_to: string;
        hourly_length: string;
        gallery: string[];
        cart_section_image: string;
        at_least_one_food_option_required: boolean;
        start_times: {
          id: string;
        };
        content_blocks: {
          id: string;
        };
        tour_seo: {
          id: string;
        };
        pricing: {
          id: string;
        };
        ticket: {
          id: string;
        };
        created_at: string;
        updated_at: string;
        deleted_at: string | null;
      };
    };
  }>;
  summary: {
    paid_total: number;
    difference_sum: number;
    raw_paid_total: {
      value: string;
      precision: number;
    };
    refunded_total: number;
    accounting_total: number;
    credit_line_total: number;
    transaction_total: number;
    pending_difference: number;
    raw_difference_sum: {
      value: string;
      precision: number;
    };
    raw_refunded_total: {
      value: string;
      precision: number;
    };
    current_order_total: number;
    original_order_total: number;
    raw_accounting_total: {
      value: string;
      precision: number;
    };
    raw_credit_line_total: {
      value: string;
      precision: number;
    };
    raw_transaction_total: {
      value: string;
      precision: number;
    };
    raw_pending_difference: {
      value: string;
      precision: number;
    };
    raw_current_order_total: {
      value: string;
      precision: number;
    };
    raw_original_order_total: {
      value: string;
      precision: number;
    };
  };
  customer: {
    id: string;
    company_name: string | null;
    first_name: string | null;
    last_name: string | null;
    email: string | null;
    phone: string | null;
    has_account: boolean;
    metadata: Record<string, unknown> | null;
    created_by: string | null;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
  };
  cart: {
    metadata: {
      phone: string;
      start_time: string | null;
      date_to: string | undefined;
      date_from: string | undefined;
      amount_of_days: number | undefined;
      start_place: {
        id: string;
        place: string;
        rich_text_content: string | null;
      } | null;
      product_type: string;
      attraction_id: string;
      selected_date: string;
      attraction_name: string;
      selected_food_options: Array<{
        id: string;
        name: string;
        price: string;
        quantity: number;
        required: boolean;
        created_at: string;
        deleted_at: string | null;
        tour_id_id: string;
        updated_at: string;
        description: string;
      }> | null;
    };
    id: string;
  };
  euroRate: number;
  prepaidPercentage: number;
};
