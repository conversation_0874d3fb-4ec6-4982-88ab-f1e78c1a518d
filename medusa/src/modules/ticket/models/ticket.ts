import { model } from "@medusajs/framework/utils";
import { InferTypeOf } from "@medusajs/types";
import { TourTicket } from "src/modules/tours/models/tour";

type TourTicketFields = InferTypeOf<typeof TourTicket>;

export const Ticket = model.define("ticket", {
  id: model.id().primaryKey(),
  tour_id: model.text().nullable(),
  reservation_number: model.number().unique(),

  customer_name: model.text().nullable(),
  customer_email: model.text().nullable(),
  customer_phone: model.text().nullable(),
  customer_address: model.text().nullable(),
  customer_city: model.text().nullable(),
  customer_zip: model.text().nullable(),
  customer_country: model.text().nullable(),

  map: model.text().nullable(),
  meeting_place: model.json().nullable(),
  meeting_time: model.json().nullable(),
  meeting_image: model.text().nullable(),
  attraction_time: model.json().nullable(),
  additional_meeting_details: model.json().nullable(),
  intermediary_name: model.text().nullable(),
  organizer_name: model.text().nullable(),
  organizer_contact: model.text().nullable(),
  ship_or_transport_description: model.text().nullable(),
  what_to_bring: model.text().nullable(),
  where_to_park: model.text().nullable(),
  additional_info: model.json().nullable(),
  link_to_download: model.text().nullable(),

  start_place: model.json().nullable(),
  selected_date: model.text().nullable(),
  date_from: model.text().nullable(),
  date_to: model.text().nullable(),
  start_time: model.text().nullable(),
  selected_food_options: model.json().nullable(),
  attraction_id: model.text().nullable(),
  attraction_name: model.text().nullable(),
  product_type: model.text().nullable(),

  amount_paid: model.float().default(0),
  total_to_pay: model.float().default(0),
} as Partial<Record<keyof TourTicketFields, any>>);
