import { Migration } from '@mikro-orm/migrations';

export class Migration20250306134229 extends Migration {

  async up(): Promise<void> {
    this.addSql('alter table if exists "order_ticket" add column if not exists "reservation_number" integer not null, add column if not exists "customer_name" text null, add column if not exists "customer_email" text null, add column if not exists "customer_phone" text null, add column if not exists "customer_address" text null, add column if not exists "customer_city" text null, add column if not exists "customer_zip" text null, add column if not exists "customer_country" text null;');
    this.addSql('CREATE UNIQUE INDEX IF NOT EXISTS "IDX_order_ticket_reservation_number_unique" ON "order_ticket" (reservation_number) WHERE deleted_at IS NULL;');
  }

  async down(): Promise<void> {
    this.addSql('drop index if exists "IDX_order_ticket_reservation_number_unique";');
    this.addSql('alter table if exists "order_ticket" drop column if exists "reservation_number";');
    this.addSql('alter table if exists "order_ticket" drop column if exists "customer_name";');
    this.addSql('alter table if exists "order_ticket" drop column if exists "customer_email";');
    this.addSql('alter table if exists "order_ticket" drop column if exists "customer_phone";');
    this.addSql('alter table if exists "order_ticket" drop column if exists "customer_address";');
    this.addSql('alter table if exists "order_ticket" drop column if exists "customer_city";');
    this.addSql('alter table if exists "order_ticket" drop column if exists "customer_zip";');
    this.addSql('alter table if exists "order_ticket" drop column if exists "customer_country";');
  }

}
