import { Migration } from '@mikro-orm/migrations';

export class Migration20250314120121 extends Migration {

  async up(): Promise<void> {
    this.addSql('alter table if exists "ticket" add column if not exists "tour_id" text null, add column if not exists "meeting_image" text null;');
  }

  async down(): Promise<void> {
    this.addSql('alter table if exists "ticket" drop column if exists "tour_id";');
    this.addSql('alter table if exists "ticket" drop column if exists "meeting_image";');
  }

}
