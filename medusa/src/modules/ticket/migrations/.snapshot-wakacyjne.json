{"namespaces": ["public"], "name": "public", "tables": [{"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "tour_id": {"name": "tour_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "reservation_number": {"name": "reservation_number", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "customer_name": {"name": "customer_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "customer_email": {"name": "customer_email", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "customer_phone": {"name": "customer_phone", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "customer_address": {"name": "customer_address", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "customer_city": {"name": "customer_city", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "customer_zip": {"name": "customer_zip", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "customer_country": {"name": "customer_country", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "map": {"name": "map", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "meeting_place": {"name": "meeting_place", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "meeting_time": {"name": "meeting_time", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "meeting_image": {"name": "meeting_image", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "attraction_time": {"name": "attraction_time", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "additional_meeting_details": {"name": "additional_meeting_details", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "intermediary_name": {"name": "intermediary_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "organizer_name": {"name": "organizer_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "organizer_contact": {"name": "organizer_contact", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "ship_or_transport_description": {"name": "ship_or_transport_description", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "what_to_bring": {"name": "what_to_bring", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "where_to_park": {"name": "where_to_park", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "additional_info": {"name": "additional_info", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "link_to_download": {"name": "link_to_download", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "start_place": {"name": "start_place", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "selected_date": {"name": "selected_date", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "date_from": {"name": "date_from", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "date_to": {"name": "date_to", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "start_time": {"name": "start_time", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "selected_food_options": {"name": "selected_food_options", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "attraction_id": {"name": "attraction_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "attraction_name": {"name": "attraction_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "product_type": {"name": "product_type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "amount_paid": {"name": "amount_paid", "type": "real", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "float"}, "total_to_pay": {"name": "total_to_pay", "type": "real", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "float"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "ticket", "schema": "public", "indexes": [{"keyName": "IDX_ticket_reservation_number_unique", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE UNIQUE INDEX IF NOT EXISTS \"IDX_ticket_reservation_number_unique\" ON \"ticket\" (reservation_number) WHERE deleted_at IS NULL"}, {"keyName": "IDX_ticket_deleted_at", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_ticket_deleted_at\" ON \"ticket\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "ticket_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}}]}