import { Migration } from '@mikro-orm/migrations';

export class Migration20250317124906 extends Migration {

  async up(): Promise<void> {
    this.addSql('alter table if exists "ticket" add column if not exists "start_place" jsonb null, add column if not exists "selected_date" text null, add column if not exists "date_from" text null, add column if not exists "date_to" text null, add column if not exists "start_time" text null, add column if not exists "selected_food_options" jsonb null, add column if not exists "attraction_id" text null, add column if not exists "attraction_name" text null, add column if not exists "product_type" text null;');
  }

  async down(): Promise<void> {
    this.addSql('alter table if exists "ticket" drop column if exists "start_place";');
    this.addSql('alter table if exists "ticket" drop column if exists "selected_date";');
    this.addSql('alter table if exists "ticket" drop column if exists "date_from";');
    this.addSql('alter table if exists "ticket" drop column if exists "date_to";');
    this.addSql('alter table if exists "ticket" drop column if exists "start_time";');
    this.addSql('alter table if exists "ticket" drop column if exists "selected_food_options";');
    this.addSql('alter table if exists "ticket" drop column if exists "attraction_id";');
    this.addSql('alter table if exists "ticket" drop column if exists "attraction_name";');
    this.addSql('alter table if exists "ticket" drop column if exists "product_type";');
  }

}
