import { Migration } from '@mikro-orm/migrations';

export class Migration20250317125700 extends Migration {

  async up(): Promise<void> {
    this.addSql('alter table if exists "ticket" add column if not exists "amount_paid" real not null default 0, add column if not exists "total_to_pay" real not null default 0;');
  }

  async down(): Promise<void> {
    this.addSql('alter table if exists "ticket" drop column if exists "amount_paid";');
    this.addSql('alter table if exists "ticket" drop column if exists "total_to_pay";');
  }

}
