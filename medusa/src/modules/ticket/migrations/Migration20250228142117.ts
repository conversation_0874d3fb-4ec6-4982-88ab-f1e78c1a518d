import { Migration } from '@mikro-orm/migrations';

export class Migration20250228142117 extends Migration {

  async up(): Promise<void> {
    this.addSql('create table if not exists "order_ticket" ("id" text not null, "map" text null, "meeting_place" jsonb null, "meeting_time" jsonb null, "attraction_time" jsonb null, "additional_meeting_details" jsonb null, "intermediary_name" text null, "organizer_name" text null, "organizer_contact" text null, "ship_or_transport_description" text null, "what_to_bring" text null, "where_to_park" text null, "additional_info" jsonb null, "link_to_download" text null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "order_ticket_pkey" primary key ("id"));');
    this.addSql('CREATE INDEX IF NOT EXISTS "IDX_order_ticket_deleted_at" ON "order_ticket" (deleted_at) WHERE deleted_at IS NULL;');
  }

  async down(): Promise<void> {
    this.addSql('drop table if exists "order_ticket" cascade;');
  }

}
