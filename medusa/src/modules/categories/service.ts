import {
  Inject<PERSON>anager,
  InjectTransactionManager,
  MedusaContext,
  MedusaInternalService,
  MedusaService,
} from "@medusajs/framework/utils";
import { EntityManager } from "@mikro-orm/knex";
import { Context, DAL } from "@medusajs/framework/types";
import { Category } from "./models/category";

type UpdateCategoryPositionInput = {
  id: string;
  rank: number;
};

class CustomProductCategoryService extends MedusaService({
  Category,
}) {
  protected baseRepository_: DAL.RepositoryService;

  @InjectTransactionManager()
  protected async updateCategoryPositions_(
    updates: UpdateCategoryPositionInput[],
    @MedusaContext() sharedContext?: Context<EntityManager>
  ): Promise<void> {
    const transactionManager = sharedContext?.transactionManager;

    if (!transactionManager) {
      throw new Error("Transaction manager not found");
    }

    for (const update of updates) {
      await transactionManager.nativeUpdate(
        "product_category",
        { id: update.id },
        { rank: update.rank }
      );
    }
  }
  @InjectManager()
  async updateCategoryPositions(
    updates: UpdateCategoryPositionInput[],
    @MedusaContext() sharedContext?: Context<EntityManager>
  ): Promise<void> {
    await this.updateCategoryPositions_(updates, sharedContext);
  }
}

export default CustomProductCategoryService;
