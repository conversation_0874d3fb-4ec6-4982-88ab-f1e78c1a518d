import {
  MedusaService,
  InjectTransactionManager,
  MedusaContext,
} from "@medusajs/framework/utils";
import GuidePost from "./models/guide-post";
import { EntityManager } from "@mikro-orm/knex";
import { Context } from "@medusajs/framework/types";

export type UpdateGuidePositionInput = {
  id: string;
  position: number;
};

class GuideService extends MedusaService({
  GuidePost,
}) {
  @InjectTransactionManager()
  protected async updateGuidePositions_(
    updates: UpdateGuidePositionInput[],
    @MedusaContext() sharedContext?: Context<EntityManager>
  ): Promise<void> {
    const transactionManager = sharedContext?.transactionManager;

    if (!transactionManager) {
      throw new Error("Transaction manager not found");
    }

    for (const update of updates) {
      await transactionManager.nativeUpdate(
        "guide_post",
        { id: update.id },
        { position: update.position }
      );
    }
  }

  async updateGuidePositions(
    updates: UpdateGuidePositionInput[],
    @MedusaContext() sharedContext?: Context<EntityManager>
  ): Promise<void> {
    await this.updateGuidePositions_(updates, sharedContext);
  }
}

export default GuideService;
