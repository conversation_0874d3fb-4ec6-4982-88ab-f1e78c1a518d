import { model } from "@medusajs/framework/utils";

const GuidePost = model.define("guide_post", {
  id: model.id().primaryKey(),
  position: model.number().default(0),
  name: model.text().searchable(),
  seo_title: model.text().default(""),
  og_title: model.text().default(""),
  content: model.json(),
  featured_image: model.text(),
  preview_image: model.text(),
  slug: model.text().unique().searchable(),
  meta_description: model.text(),
  tags: model.array().default([]), //Defaults to array of strings
  is_published: model.boolean().default(false),
  related_guides: model.array().default([]),
});

export default GuidePost;
