import { Migration } from '@mikro-orm/migrations';

export class Migration20250131135058 extends Migration {

  async up(): Promise<void> {
    this.addSql('create table if not exists "guide_post" ("id" text not null, "name" text not null, "seo_title" text not null default \'\', "og_title" text not null default \'\', "content" jsonb not null, "featured_image" text not null, "preview_image" text not null, "slug" text not null, "meta_description" text not null, "tags" text[] not null default \'{}\', "is_published" boolean not null default false, "related_guides" text[] not null default \'{}\', "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "guide_post_pkey" primary key ("id"));');
    this.addSql('CREATE UNIQUE INDEX IF NOT EXISTS "IDX_guide_post_slug_unique" ON "guide_post" (slug) WHERE deleted_at IS NULL;');
    this.addSql('CREATE INDEX IF NOT EXISTS "IDX_guide_post_deleted_at" ON "guide_post" (deleted_at) WHERE deleted_at IS NULL;');
  }

  async down(): Promise<void> {
    this.addSql('drop table if exists "guide_post" cascade;');
  }

}
