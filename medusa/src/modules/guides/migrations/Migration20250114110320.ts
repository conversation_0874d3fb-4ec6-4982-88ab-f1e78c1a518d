import { Migration } from '@mikro-orm/migrations';

export class Migration20250114110320 extends Migration {

  async up(): Promise<void> {
    this.addSql('alter table if exists "guide_post" add column if not exists "slug" text not null, add column if not exists "meta_description" text not null, add column if not exists "tags" text[] not null;');
    this.addSql('CREATE UNIQUE INDEX IF NOT EXISTS "IDX_guide_post_slug_unique" ON "guide_post" (slug) WHERE deleted_at IS NULL;');
  }

  async down(): Promise<void> {
    this.addSql('drop index if exists "IDX_guide_post_slug_unique";');
    this.addSql('alter table if exists "guide_post" drop column if exists "slug";');
    this.addSql('alter table if exists "guide_post" drop column if exists "meta_description";');
    this.addSql('alter table if exists "guide_post" drop column if exists "tags";');
  }

}
