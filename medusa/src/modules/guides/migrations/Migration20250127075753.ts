import { Migration } from "@mikro-orm/migrations";

export class Migration20250127075753 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table if exists "guide_post" add column if not exists "seo_title" text not null default \'\', add column if not exists "og_title" text not null default \'\';'
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table if exists "guide_post" drop column if exists "seo_title";'
    );
    this.addSql(
      'alter table if exists "guide_post" drop column if exists "og_title";'
    );
  }
}
