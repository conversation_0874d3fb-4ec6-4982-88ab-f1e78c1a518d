{"namespaces": ["public"], "name": "public", "tables": [{"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "name": {"name": "name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "seo_title": {"name": "seo_title", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "''", "mappedType": "text"}, "og_title": {"name": "og_title", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "''", "mappedType": "text"}, "content": {"name": "content", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "json"}, "featured_image": {"name": "featured_image", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "preview_image": {"name": "preview_image", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "slug": {"name": "slug", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "meta_description": {"name": "meta_description", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "tags": {"name": "tags", "type": "text[]", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'{}'", "mappedType": "array"}, "is_published": {"name": "is_published", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "related_guides": {"name": "related_guides", "type": "text[]", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'{}'", "mappedType": "array"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "guide_post", "schema": "public", "indexes": [{"keyName": "IDX_guide_post_slug_unique", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE UNIQUE INDEX IF NOT EXISTS \"IDX_guide_post_slug_unique\" ON \"guide_post\" (slug) WHERE deleted_at IS NULL"}, {"keyName": "IDX_guide_post_deleted_at", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_guide_post_deleted_at\" ON \"guide_post\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "guide_post_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}}]}