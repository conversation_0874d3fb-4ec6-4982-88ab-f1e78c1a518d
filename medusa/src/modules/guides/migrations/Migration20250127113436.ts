import { Migration } from '@mikro-orm/migrations';

export class Migration20250127113436 extends Migration {

  async up(): Promise<void> {
    this.addSql('alter table if exists "guide_post" add column if not exists "related_guides" text[] not null default \'{}\';');
  }

  async down(): Promise<void> {
    this.addSql('alter table if exists "guide_post" drop column if exists "related_guides";');
  }

}
