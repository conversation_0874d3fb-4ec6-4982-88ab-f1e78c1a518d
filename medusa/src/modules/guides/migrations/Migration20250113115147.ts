import { Migration } from '@mikro-orm/migrations';

export class Migration20250113115147 extends Migration {

  async up(): Promise<void> {
    this.addSql('create table if not exists "guide_post" ("id" text not null, "name" text not null, "content" jsonb not null, "featured_image" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "guide_post_pkey" primary key ("id"));');
    this.addSql('CREATE INDEX IF NOT EXISTS "IDX_guide_post_deleted_at" ON "guide_post" (deleted_at) WHERE deleted_at IS NULL;');

    this.addSql('drop table if exists "blog_post" cascade;');
  }

  async down(): Promise<void> {
    this.addSql('create table if not exists "blog_post" ("id" text not null, "name" text not null, "content" jsonb not null, "featured_image" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "blog_post_pkey" primary key ("id"));');
    this.addSql('CREATE INDEX IF NOT EXISTS "IDX_blog_post_deleted_at" ON "blog_post" (deleted_at) WHERE deleted_at IS NULL;');

    this.addSql('drop table if exists "guide_post" cascade;');
  }

}
