import { Migration } from "@mikro-orm/migrations";

export class Migration20250201000000 extends Migration {
  async up(): Promise<void> {
    this.addSql(
      'alter table if exists "guide_post" add column if not exists "position" integer not null default 0;'
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table if exists "guide_post" drop column if exists "position";'
    );
  }
}
