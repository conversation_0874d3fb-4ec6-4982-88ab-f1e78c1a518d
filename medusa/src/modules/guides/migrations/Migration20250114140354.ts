import { Migration } from '@mikro-orm/migrations';

export class Migration20250114140354 extends Migration {

  async up(): Promise<void> {
    this.addSql('alter table if exists "guide_post" add column if not exists "preview_image" text not null, add column if not exists "is_published" boolean not null default false;');
  }

  async down(): Promise<void> {
    this.addSql('alter table if exists "guide_post" drop column if exists "preview_image";');
    this.addSql('alter table if exists "guide_post" drop column if exists "is_published";');
  }

}
