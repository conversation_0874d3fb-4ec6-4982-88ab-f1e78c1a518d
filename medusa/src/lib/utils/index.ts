export const removeDateInParentheses = (text: string | undefined): string => {
  if (!text) {
    return "";
  }

  // Define a more specific regex pattern to match date ranges like (1 wrz 2025 - 4 paź 2025)
  // This explicitly looks for digits, followed by month abbreviation, followed by year
  const datePattern =
    /\(\d{1,2} [a-zA-Zżźćńółęąś]{2,3} \d{4} - \d{1,2} [a-zA-Zżźćńółęąś]{2,3} \d{4}\)/g;

  // Replace all matches of the date pattern with an empty string
  const result = text.replace(datePattern, "");

  // Remove any extra spaces left after replacement
  return result.replace(/\s+/g, " ").trim();
};

export const getTicketFileName = (
  order_id: string,
  customer_last_name: string | null
) => {
  return customer_last_name
    ? customer_last_name + "_" + order_id + "_wakacyjnepomysly.pdf"
    : order_id + "_wakacyjnepomysly.pdf";
};

export const sortByAgeGroup = <T extends { title: string }>(array: T[]) => {
  // Create a function to determine the min age from the name
  const getMinAge = (name: string) => {
    // Extract the first number found in the string
    const match = name.match(/\d+/g);
    if (!match) return -1; // Use -1 for items with no numbers
    return parseInt(match[0]);
  };

  // Sort the array based on the min age in each name
  return array.sort((a, b) => {
    const ageA = getMinAge(a.title);
    const ageB = getMinAge(b.title);

    // If either item has no number, move it to the end
    if (ageA === -1 && ageB === -1) return 0;
    if (ageA === -1) return 1; // Move 'a' to the end
    if (ageB === -1) return -1; // Move 'b' to the end

    // Sort by descending order (older ages first)
    return ageB - ageA; // This puts higher numbers (older age groups) first
  });
};

export const getOrderSummaryDifference = (orderSummaries: any[]): number => {
  if (!orderSummaries || orderSummaries.length < 2) {
    console.log("Need at least 2 versions to compare");
    return 0;
  }

  // Sort summaries by version
  const sortedSummaries = [...orderSummaries].sort(
    (a, b) => a.version - b.version
  );

  // Get the last version and the previous version
  const lastVersion = sortedSummaries[sortedSummaries.length - 1];
  const previousVersion = sortedSummaries[sortedSummaries.length - 2];

  // Extract the difference_sum values
  const lastDiffSum = lastVersion?.totals?.difference_sum ?? 0;
  const prevDiffSum = previousVersion?.totals?.difference_sum ?? 0;

  console.log(
    `Last version (v${lastVersion.version}): difference_sum = ${lastDiffSum}`
  );
  console.log(
    `Previous version (v${previousVersion.version}): difference_sum = ${prevDiffSum}`
  );

  // Return the difference_sum from the last version
  return lastDiffSum;
};

// Helper function to normalize dates for comparison
export function getDatePart(dateString: string): string {
  // Extract only the date part (YYYY-MM-DD) from any date string format
  if (!dateString) return "";

  // If it's already in DD.MM.YYYY format (Polish format), convert to YYYY-MM-DD
  if (dateString.includes(".")) {
    const parts = dateString.split(".");
    if (parts.length === 3) {
      const [day, month, year] = parts;
      return `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
    }
  }

  // If it's a standard ISO string, just extract the date part
  if (dateString.includes("T")) {
    return dateString.split("T")[0];
  }

  // If it's already just a date (YYYY-MM-DD), return as is
  return dateString;
}
