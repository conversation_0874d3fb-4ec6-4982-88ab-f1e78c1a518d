import { Order } from ".medusa/types/query-entry-points";
import { formatDate } from "src/admin/lib/util/string-dates";
import { config } from "src/config";
import { removeDateInParentheses, sortByAgeGroup } from "src/lib/utils";
import ticket from "src/modules/ticket";

export type TicketTemplateData = {
  id: string;
  customer_name: string;
  customer_email: string;
  customer_phone: string;
  meeting_place?: string;
  meeting_time?: string;
  meeting_image?: string;
  attraction_time?: string;
  additional_meeting_details?: string;
  intermediary_name?: string;
  organizer_name?: string;
  organizer_contact?: string;
  ship_or_transport_description?: string;
  what_to_bring?: string;
  where_to_park?: string;
  additional_info?: string;
  link_to_download?: string;
  pending_difference?: number;
  start_place?: {
    place: string;
    position: number;
    rich_text_content?: string;
  };
  selected_date?: string;
  date_from?: string | null;
  date_to?: string | null;
  start_time?: string;
  selected_food_options?: Array<{
    id: string;
    name: string;
    price: string;
    quantity: number;
    required: boolean;
    description: string;
  }>;
  attraction_id?: string;
  attraction_name?: string;
  map?: string;
  items?: Order["items"];

  reservation_number?: string;

  amount_paid: number;
  total_to_pay: number;
};

export const getTicketTemplate = (ticket: TicketTemplateData) => {
  // Helper function to ensure text is black

  const ensureNotEmptyPTag = (html: string | undefined | null): string => {
    if (!html) return "";

    if (html === "<p></p>") {
      return "";
    }

    return html;
  };

  const ensureBlackText = (html: string | undefined | null): string => {
    if (!html) return "";
    if (html === "<p></p>") return "";

    let verifiedHtml = ensureNotEmptyPTag(html);

    // if html contains <a/> with inline style color in it overwrite it with 000000
    const aTags = verifiedHtml?.match(
      /<a[^>]*style="color:([^;]+);[^>]*>.*?<\/a>/g
    );

    if (aTags) {
      aTags.forEach((tag) => {
        verifiedHtml = verifiedHtml?.replace(
          tag,
          tag.replace(/color:([^;]+);/, "color: #222;")
        );
      });
    }

    return /* HTML */ `<div class="unified_received_html">
      ${verifiedHtml}
    </div>`;
  };

  // Extract food options counts
  const foodOptions =
    ticket.selected_food_options
      ?.map((option) => {
        return {
          name: option.name,
          quantity: option.quantity,
        };
      })
      .filter((option) => option.quantity > 0) || [];

  // Generate food options HTML
  const generateFoodOptionsHtml = () => {
    if (!foodOptions.length) {
      return "-";
    }

    return foodOptions
      .map((option) => {
        return /* HTML */ `
          <li class="info-list__item">
            <span class="section__value">
              ${removeDateInParentheses(option.name)}</span
            >
            <span class="semibold">${option.quantity}</span>
          </li>
        `;
      })
      .join("");
  };

  // Prepare HTML content
  const whatToBringHtml = ensureBlackText(ticket.what_to_bring);
  const additionalInfoHtml = ensureBlackText(ticket.additional_info);
  const organizerNameHtml = ensureBlackText(ticket.organizer_name);
  const intermediaryNameHtml = ensureBlackText(ticket.intermediary_name);
  const additionalMeetingDetailsHtml = ensureBlackText(
    ticket.additional_meeting_details
  );

  const sanitizedLinkToDownload = (() => {
    if (!ticket.link_to_download) {
      return config.MEDUSA_STOREFRONT_URL + "/poradniki";
    }
    // Extract href content from HTML if it's HTML markup
    const hrefMatch = ticket.link_to_download.match(/href="([^"]*)"/);
    if (hrefMatch) {
      return hrefMatch[1];
    }

    // If it's just a plain URL, return it as is
    return ticket.link_to_download;
  })();

  const shipDescriptionHtml = ensureBlackText(
    ticket.ship_or_transport_description
  );

  const isNotEmptyParagraphTag = (content: string | undefined | null) => {
    if (!content) return false;
    return content !== "<p></p>";
  };

  const whereToParkHtml = ensureBlackText(ticket.where_to_park);
  const mapHtml = false;
  //  ensureNotEmptyPTag(ticket?.map || null);

  // Fixed time variable to handle HTML content properly
  const time =
    (ticket.start_time && isNotEmptyParagraphTag(ticket.start_time)
      ? ensureBlackText(ticket.start_time)
      : null) ||
    (ticket.attraction_time && isNotEmptyParagraphTag(ticket.attraction_time)
      ? ensureBlackText(ticket.attraction_time)
      : null) ||
    "";

  const date = ticket.selected_date
    ? ensureBlackText(formatDate(ticket.selected_date))
    : "";
  const date_from = ticket.date_from
    ? ensureBlackText(formatDate(ticket.date_from))
    : "";
  const date_to = ticket.date_to
    ? ensureBlackText(formatDate(ticket.date_to))
    : "";

  // Extract other ticket details
  const fullName = ticket.customer_name;
  const reservationNumber =
    ticket.reservation_number?.toString().padStart(4, "0") || "";
  const contactPhone = ensureBlackText(ticket.organizer_contact);
  const attractionName = ticket.attraction_name;

  // Default values for missing data
  const price = ticket.total_to_pay;
  const paidAmount = ticket.amount_paid;
  const pendingDifference = ticket.pending_difference;

  const formatPrice = (price: number | undefined) => {
    if (price === 0) return "0";
    if (!price) return "";
    return price.toFixed(2).replace(".", ",");
  };

  const totalParticipants = ticket.items?.reduce(
    (acc, item) => acc + (item?.quantity ?? 0),
    0
  );

  const totalFoodOptions = ticket.selected_food_options?.reduce(
    (acc, option) => acc + (option.quantity ?? 0),
    0
  );

  const items = ticket.items?.map((item) => {
    return {
      title: item?.title || "",
      count: item?.quantity || 0,
    };
  });

  const extractPhoneNumber = (phoneString: string) => {
    // Extract only the digits, plus signs, and dashes from the phone string
    const numberOnly = phoneString.replace(/[^\d+\-\s]/g, "").trim();

    // Remove any text in parentheses and surrounding whitespace
    const trimmedNumber = numberOnly.replace(/\s*\(.*?\)\s*/g, "");

    // Remove any remaining whitespace
    return trimmedNumber.replace(/\s+/g, "");
  };

  const generateItemsHtml = () => {
    if (!items?.length) {
      return /* HTML */ `
        <li class="info-list__item">
          <span class="info-list__number">1.</span>
          <span class="section__value">Ryba</span>
          <span class="semibold">0</span>
        </li>
      `;
    }

    const sortedItems = sortByAgeGroup(items);

    // if item.title is repeating, increase the count
    const uniqueItems = sortedItems.reduce((acc, item) => {
      const existingItem = acc.find((i) => i.title === item.title);
      if (existingItem) {
        existingItem.count += item?.count ?? 0;
      } else {
        acc.push(item);
      }
      return acc;
    }, [] as { title: string; count: number }[]);

    return uniqueItems
      .map((option, index) => {
        return /* HTML */ `
          <li class="info-list__item">
            <span class="section__value">
              ${removeDateInParentheses(option.title)}</span
            >
            <span class="semibold">${option.count}</span>
          </li>
        `;
      })
      .join("");
  };

  const generateDateHtml = () => {
    if (date) {
      return /* HTML */ `
        <span class="section__label section__label--alignment"
          >Data atrakcji</span
        >
        <div class="section__value">${date}</div>
      `;
    }

    if (date_from && date_to) {
      return /* HTML */ `
        <span class="section__label section__label--alignment"
          >Data atrakcji od - do</span
        >
        <div class="section__value">${date_from} - ${date_to}</div>
      `;
    }
  };

  // Format meeting place for display
  const meetingPlaceDisplay =
    ticket.start_place?.rich_text_content &&
    isNotEmptyParagraphTag(ticket.start_place?.rich_text_content)
      ? ensureBlackText(ticket.start_place?.rich_text_content)
      : ensureBlackText(ticket.meeting_place) || "";

  const meetingTimeDisplay = ensureBlackText(ticket.meeting_time) || "";

  const styles = /* CSS */ `
  @import url("https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap");

  :root {
    --color-primary: #f8b832;
    --color-dark: #222222;
    --color-text: #222222;
    --color-light: #ffffff;
    --color-gray: #f5f5f5;
    --color-border: #e0e0e0;
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    --border-radius: 0.35rem;
  }

  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "Plus Jakarta Sans", sans-serif;
  }


  html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
  }


  body {
    color: var(--color-text);
    line-height: 1.6;
    font-size: 85%;
    display: flex;
    flex-direction: column;
    height: 100%;
  }
    
  .main-content {
    flex: 1;
    background-color: var(--color-light);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }


  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-xl);
  }

  /* Header */
  .header {
    background-color: var(--color-primary);
    padding: var(--spacing-lg) 0;
  }

  .header__logo {
    display: flex;
    align-items: center;
    padding: var(--spacing-md) 0;
  }

  .header__logo-img {
    height: 25px;
  }

  /* Hero/Reservation Section */
  .hero {
    background-color: var(--color-primary);
    padding: var(--spacing-xl) 0;
  }

  .hero__content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
  }

  .hero__title {
    font-size: 2rem;
    line-height: 1.2;
    color: var(--color-dark);
    text-wrap: balance;
    flex: 1;
  }

  .hero__stats {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .hero__number {
    margin-top: var(--spacing-xs);
    display: inline-block;
    background-color: var(--color-dark);
    color: var(--color-light);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    font-size: 1.2rem;
    font-weight: bold;
  }

  .hero__label {
    display: block;
    font-size: 1rem;
    margin-top: var(--spacing-xs);
    text-align: center;
    font-weight: 600;
    color: var(--color-dark);
  }

  /* Sections */
  .section {
    padding: var(--spacing-lg) 0;
    border-bottom: 1px solid var(--color-border);

    &:last-child {
      border-bottom: none;
    }
  }

  .section--without-border {
    border-bottom: none;
  }

  .section__header-wrapper {
    margin-top: -2.7rem;
    display: flex;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
  }

  .section__header {
    background-color: var(--color-dark);
    color: var(--color-light);
    height: fit-content;
    padding: var(--spacing-sm) var(--spacing-md);
    display: inline-block;
    border-radius: var(--border-radius);
    text-align: center;
  }

  .section__content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    margin-bottom: var(--spacing-lg);
    gap: var(--spacing-md);
  }
    
  .section__content--2col {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-2xl) var(--spacing-3xl);
  }

  .section__item {
    margin-bottom: var(--spacing-xs);
    break-inside: avoid;
  }
    
  .section__item--between {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .section__item-2 {
    grid-column: span 2;
  }

  .section__label {
    font-size: 0.9rem;
    color: var(--color-dark);
    display: block;
    margin-top: var(--spacing-sm);
  }

  .section__value {
    font-weight: bold;
  }

  .section__value--nowrap {
    white-space: nowrap;
  }
    
  .section__value--title {
    margin-bottom: var(--spacing-xs);
  }

  .section__item--span2 {
    grid-column: span 2;
  }

  .section__item--flex {
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    gap: var(--spacing-xl);
  }

  .section__value--large {
    font-size: 1.5rem;
    font-weight: 600;
  }

  .section__value--medium {
    font-size: 1.25rem;
    font-weight: 600;
  }

  a {
    color: var(--color-dark);
    text-decoration: underline;
    font-weight: 600;
  }
    
  ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
  }
    

  li {
    padding: 0;
    margin: 0;
  }

  .separator {
    width: 100%;
    height: 1px;
    background-color: var(--color-border);
    margin: var(--spacing-lg) 0;
  }
  
  .flex-column {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: flex-start;
  }

  /* Organizer Info */
  .organizer {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
    padding: var(--spacing-lg) 0;
  }

  .organizer__name {
    font-size: 1.5rem;
    font-weight: bold;
  }
    
  .organizer__item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    break-inside: avoid;
  }

  /* Image Section */
  .location-image {
    max-width: 100%;
    border-radius: var(--border-radius);
    margin: var(--spacing-lg) auto;
  }

  /* Lists */
  .info-list {
    list-style-type: none;
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
  }
    
  .info-list--grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap-column: var(--spacing-md);
    gap-row: var(--spacing-sm) !important;
  }

  .info-list__item {
    display: flex;
    flex-wrap: wrap;
    margin-left: 0;
    padding-left: 0;
  }

  .info-list__item .section__value {
    font-weight: normal;
    margin-right: var(--spacing-sm);
  }

  .semibold {
    font-weight: 600;
  }

  .info-list__number {
    margin-left: var(--spacing-sm);
    font-weight: bold;

    &:first-child {
      margin-left: 0;
      padding-left: 0;
      margin-right: var(--spacing-sm);
    }
  }

  /* Footer */
  .footer {
    margin-top: auto; /* This pushes the footer to the bottom */
    background-color: var(--color-dark);
    color: var(--color-light);
    padding: var(--spacing-3xl) 0 var(--spacing-2xl) 0;
    width: 100%;
    break-inside: avoid;
  }

  .footer__content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .footer__logo {
    display: flex;
    align-items: center;
  }

  .footer__logo-img {
    height: 50px;
    margin-right: var(--spacing-sm);
  }

  .footer__social {
    display: flex;
    justify-content: end;
    color: var(--color-light);
    gap: var(--spacing-xl);
  }

  .footer__social a {
    color: var(--color-light);
  }

  .footer__social-icon {
    width: auto;
    height: 20px;
  }

  .footer__button {
    background-color: var(--color-dark);
    color: var(--color-light);
    border: 1px solid var(--color-light);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius);
    cursor: pointer;
    text-decoration: none;
  }

  .unified_received_html  {
    font-size: 0.875rem;
    color: var(--color-text);
  }
    
  .unified_received_html:has(a) * {
    color: var(--color-text) !important;
  }
  
  @page:first {
    margin: 0; /* No margin on the first page */
  }

  @page {
    margin-top: 10mm; /* Margin for all subsequent pages */
  }
  
  .section__label--alignment {
    margin-bottom: 0.75rem;
  }

  `;

  return /* HTML */ `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8" />
      </head>
      <body>
        <style>
          ${styles}
        </style>
        <header class="header">
          <div class="container">
            <div class="header__logo">
              <img src=${config.MEDUSA_API_ENDPOINT_URL}/static/common/logo.png
              alt="wakacyjnepomysly.pl" class="header__logo-img" />
            </div>
            <div class="hero">
              <div class="hero__content">
                <h1 class="hero__title">${attractionName}</h1>

                <div class="hero__stats">
                  <div class="hero__number">${reservationNumber}</div>
                  <span class="hero__label">nr rezerwacji</span>
                </div>
              </div>
            </div>
          </div>
        </header>

        <!-- Main Content -->
        <main class="container main-content">
          <!-- Organizer Section -->
          <section class="section">
            <div class="section__header-wrapper">
              <div class="section__header">Dla organizatora</div>
            </div>

            <div class="section__content section__content--2col">
              <div class="section__item">
                <span class="section__label">Imię i Nazwisko</span>
                <h3 class="section__value--large">${fullName}</h3>
              </div>

              <div class="section__item--flex" style="align-items: flex-start;">
                <div class="section__item">${generateDateHtml()}</div>

                <div class="section__item">
                  ${time
                    ? /* HTML */ `
                        <span class="section__label section__label--alignment"
                          >Godzina atrakcji</span
                        >
                        <div class="section__value">${time}</div>
                      `
                    : ""}
                </div>
              </div>
            </div>

            <div class="section__content section__content--2col">
              <div class="section__item section__item">
                <div class="section__item section__item--between">
                  <span class="section__label">Cena</span>
                  <div class="section__value--large section__value--nowrap">
                    ${formatPrice(price)} €
                  </div>
                </div>
                <div class="section__item--flex">
                  <div class="section__item section__item--between">
                    <span class="section__label">Opłacono</span>
                    <div class="section__value--medium section__value--nowrap">
                      ${formatPrice(paidAmount)} €
                    </div>
                  </div>
                  <div class="section__item section__item--between">
                    <span class="section__label">Pozostało do zapłaty</span>
                    <div class="section__value--medium section__value--nowrap">
                      ${formatPrice(pendingDifference)} €
                    </div>
                  </div>
                </div>
              </div>

              <div class="section__item">
                <div class="section__item--flex" style="gap: .5rem">
                  <span class="section__label">Uczestnicy</span>
                  <!-- <div class="section__value">(${totalParticipants})</div> -->
                </div>

                <ul class="info-list info-list--grid">
                  ${generateItemsHtml()}
                </ul>
              </div>
            </div>

            <div class="section__content section__content--2col">
              <div class="section__item section__item">
                <span class="section__label">Miejsce rozpoczęcia</span>
                <div class="section__value">
                  ${ticket.start_place?.place
                    ? `<div class="section__value">${ensureBlackText(
                        ticket.start_place.place
                      )}</div>`
                    : ""}
                </div>
                <div class="section__value">${meetingPlaceDisplay}</div>
              </div>

              ${ticket.selected_food_options?.some(
                (option) => option.quantity > 0
              )
                ? /* HTML */ `
                    <div class="section__item">
                      <div class="section__item--flex" style="gap: .5rem">
                        <span class="section__label">Lunch</span>
                        <span class="section__value"
                          >(${totalFoodOptions})</span
                        >
                      </div>
                      <ul class="info-list">
                        ${generateFoodOptionsHtml()}
                      </ul>
                    </div>
                  `
                : ""}
            </div>
          </section>

          <!-- Client Section -->
          <section class="section section--without-border">
            <div class="section__header-wrapper">
              <div class="section__header">Dla klienta</div>
            </div>

            <div class="section__content section__content--2col">
              <div class="organizer__item section__item">
                <span class="section__label">Nazwa organizatora</span>
                <h2 class="organizer__name">${organizerNameHtml}</h2>
              </div>

              <div class="organizer__item section__item">
                <span class="section__label">Nazwa pośrednika</span>
                <h2 class="organizer__name">${intermediaryNameHtml}</h2>
              </div>

              ${time
                ? /* HTML */ ` <div class="organizer__item section__item">
                    <span class="section__label">Godzina atrakcji</span>
                    <div class="section__value">${time}</div>
                  </div>`
                : ""}
              ${contactPhone
                ? /* HTML */ ` <div class="organizer__item section__item">
                    <span class="section__label">Kontakt</span>
                    <div class="section__value">
                      <a href="tel:${extractPhoneNumber(contactPhone)}"
                        >${contactPhone}</a
                      >
                    </div>
                  </div>`
                : ""}
            </div>

            <div class="separator"></div>

            <div class="section__content section__content--2col">
              <div class="flex-column">
                ${meetingPlaceDisplay
                  ? /* HTML */ ` <div class="section__item">
                      <h3 class="section__value--medium section__value--title">
                        Miejsce zbiórki
                      </h3>
                      ${ticket.start_place?.place
                        ? `<div class="section__value">${ensureBlackText(
                            ticket.start_place.place
                          )}</div>`
                        : ""}
                      <div>${meetingPlaceDisplay}</div>
                    </div>`
                  : ""}
                ${additionalInfoHtml
                  ? /* HTML */ ` <div class="section__item">
                      <h3 class="section__value section__value--title">
                        Dodatkowe informacje
                      </h3>
                      <div>${additionalInfoHtml}</div>
                    </div>`
                  : ""}
                ${whereToParkHtml
                  ? /* HTML */ ` <div class="section__item">
                      <h3 class="section__value section__value--title">
                        Gdzie zaparkować?
                      </h3>
                      <div>${whereToParkHtml}</div>
                    </div>`
                  : ""}
              </div>

              <div class="flex-column">
                ${meetingTimeDisplay
                  ? /* HTML */ ` <div class="section__item">
                      <h3 class="section__value--medium section__value--title">
                        Godzina zbiórki
                      </h3>
                      <div>${meetingTimeDisplay}</div>
                    </div>`
                  : ""}
                ${shipDescriptionHtml
                  ? /* HTML */ ` <div class="section__item">
                      <h3 class="section__value section__value--title">
                        Nazwa statku
                      </h3>
                      <div>${shipDescriptionHtml}</div>
                    </div>`
                  : ""}
                ${whatToBringHtml
                  ? /* HTML */ `
                      <div class="section__item">
                        <h3 class="section__value section__value--title">
                          Co zabrać ze sobą?
                        </h3>
                        <div>${whatToBringHtml}</div>
                      </div>
                    `
                  : ""}
                ${mapHtml
                  ? /* HTML */ ` <div class="section__item">
                      <h3 class="section__value section__value--title">
                        Link do mapy
                      </h3>
                      <div>${mapHtml}</div>
                    </div>`
                  : ""}
                ${additionalMeetingDetailsHtml
                  ? /* HTML */ ` <div class="section__item">
                      <h3 class="section__value section__value--title">
                        Dodatkowe informacje o zbiórce
                      </h3>
                      <div>${additionalMeetingDetailsHtml}</div>
                    </div>`
                  : ""}
              </div>
            </div>

            <div class="section__content"></div>
          </section>
        </main>

        <!-- Footer -->
        <footer class="footer">
          <div class="container">
            <div class="footer__content">
              <div class="footer__logo">
                <a href="${config.MEDUSA_STOREFRONT_URL}" target="_blank">
                  <img
                  src=${config.MEDUSA_API_ENDPOINT_URL}/static/common/logo_bottom.png
                  alt="wakacyjnepomysly.pl" class="footer__logo-img" />
                </a>
              </div>
              <div style="text-align: center">
                <a
                  href="${sanitizedLinkToDownload}"
                  target="_blank"
                  class="footer__button"
                  >Sprawdź porady o okolicy</a
                >
              </div>
            </div>
          </div>
        </footer>

        <!-- Meeting Image -->
        <section class="container">
          ${ticket?.meeting_image
            ? /* HTML */ `<img
                src=${ticket.meeting_image}
                alt="meeting image"
                class="location-image"
              />`
            : ""}
        </section>
      </body>
    </html>
  `;
};
