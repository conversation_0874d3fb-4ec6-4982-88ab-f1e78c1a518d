import { config } from "src/config";
import { getSharedLayout, MAIL_COLORS } from "./shared-layout";
import { getTicketFileName } from "src/lib/utils";

export const orderTicketCreatedTemplate = (data: {
  reservation_number: string;
  attraction_name?: string;
  date?: string;
  order_id: string;
  customer_last_name: string | null;
}) => {
  const ticketFileName = getTicketFileName(data.order_id, data.customer_last_name);

  const mainContent = /* HTML */ `
    <h2>Cześć!</h2>

    <p>Atrakcja została potwierdzona u organizatora!</p>

    <p>
      W załączniku znajduje się bilet, który należy okazać w dniu atrakcji w
      telefonie (nie trzeba drukować biletu).
    </p>

    <p style="font-weight: bold;">
      Ważne! Zapoznaj się z informacjami o zbiórce oraz przeczytaj o ważnych
      kwestiach dotyczących atrakcji. Na zbiórkę nie można się sp<PERSON>, więc
      przygotuj się wcześniej sprawdzając miejsce startu.
    </p>

    <a
      href="${config.MEDUSA_API_ENDPOINT_URL}/static/tickets/${ticketFileName}"
      style="display: inline-block; background-color: ${MAIL_COLORS.primary}; color: ${MAIL_COLORS.buttonText}; padding: 10px 20px; text-decoration: none; border-radius: 4px; font-weight: bold; margin: 15px 0;"
      >Kliknij i pobierz bilet</a
    >
  `;

  return getSharedLayout({
    attractionName: data.attraction_name,
    reservationNumber: data.reservation_number,
    date: data.date,
    title: "Twoja atrakcja została potwierdzona!",
    mainContent,
  });
};
