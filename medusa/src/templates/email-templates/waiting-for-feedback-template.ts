import { getSharedLayout, MAIL_COLORS } from "./shared-layout";

export const waitingForFeedbackTemplate = (data: {
  reservation_number: string;
  attraction_name?: string;
  date?: string;
}) => {
  const mainContent = /* HTML */ `
    <h2><PERSON><PERSON><PERSON><PERSON>!</h2>
    
    <p><PERSON><PERSON>, że atrakcja się Tobie podobała! Będziemy bardzo wdzięczni za pozostawienie opinii o naszej pracy podczas rezerwacji. Bardzo nam zależy na informacji, czy proces rezerwacji oraz poradniki na naszej stronie pomogły w spokojnym zaplanowaniu cudownego dnia w Chorwacji!</p>
    
    <p>Recenzję można zostawić w sekcji "opinie" na naszej stronie na FB:</p>
    <a href="https://bit.ly/wakacyjnepomysly" style="color: ${MAIL_COLORS.primary};">https://bit.ly/wakacyjnepomysly</a></p>
    
    <p>Zd<PERSON><PERSON>cia z atrakcji mile widziane!<br>
    Z góry dziękujemy! Pozwala to nam się rozwijać.<br>
  `;

  return getSharedLayout({
    reservationNumber: data.reservation_number,
    date: data.date,
    mainContent,
    title: "Czekamy na Twoją opinię",
  });
};
