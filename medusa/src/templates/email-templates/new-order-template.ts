import { generateContentTable } from "./generate-table";
import { getSharedLayout } from "./shared-layout";
import { ObtainOrderDetailsForEmailResult } from "src/modules/ticket/types";
export const newOrderTemplate = (data: {
  reservation_number: string;
  attraction_name?: string;
  date?: string;
  orderDetails: ObtainOrderDetailsForEmailResult;
}) => {
  const mainContent = /* HTML */ `
    <h2>C<PERSON>ść!</h2>

    <p>
      Otrzymaliśmy Twoje zamówienie. Przystępujemy do jego realizacji. Jak tylko
      potwierdzimy Twoją atrakcję z organizatorem, w kolejnej wiadomości
      otrzymasz bilet.
    </p>

    <p>
      <PERSON>wykle potwierdzamy atrakcje do 12h, aczkolwiek w niektórych sytuacjach
      ten czas może się wydłużyć. W razie ewentualnych pytań zawsze możesz do
      nas napisać na nasz messenger, kt<PERSON>ry znajdziesz na naszej stronie w
      zakładce kontakt.
    </p>

    ${generateContentTable(data.orderDetails, false)}

    <p>
      To nie jest faktura dla celów podatku VAT. <br />
      Pamiętaj, że ten dokument jest wyłącznie dowodem dokonania płatności.
    </p>
  `;

  return getSharedLayout({
    attractionName: data.attraction_name,
    reservationNumber: data.reservation_number,
    date: data.date,
    mainContent,
    title: "Przyjęliśmy Twoje zamówienie do realizacji",
  });
};
