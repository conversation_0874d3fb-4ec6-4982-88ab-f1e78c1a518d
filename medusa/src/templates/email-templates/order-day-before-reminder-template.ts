import { getSharedLayout } from "./shared-layout";
export const orderDayBeforeReminderTemplate = (data: {
  reservation_number: string;
  attraction_name?: string;
  date?: string;
}) => {
  const mainContent = /* HTML */ `
    <h2><PERSON><PERSON><PERSON><PERSON>!</h2>

    <p>
      P<PERSON>ypominamy, że już jutro czeka na Ciebie zarezerwowana atrakcja:
      ${data.attraction_name}! <PERSON><PERSON><PERSON><PERSON> pewni, że będzie to niezapomniane
      doświadczenie.
    </p>

    <p>
      Zapoznaj się jeszcze raz z przesłanym wcześniej biletem i pamiętaj, aby
      być na zbiórce punktualnie!
    </p>

    <p>
      Pozdrawiamy!<br />
      <a href="https://www.wakacyjnepomysly.pl">www.wakacyjnepomysly.pl</a>
    </p>
  `;

  return getSharedLayout({
    attractionName: data.attraction_name,
    reservationNumber: data.reservation_number,
    date: data.date,
    mainContent,
    title: `To już jutro! <br/> Czeka na Ciebie Twoja atrakcja!`,
  });
};
