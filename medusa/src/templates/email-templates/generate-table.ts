import { formatDate } from "src/admin/lib/util/string-dates";
import { ObtainOrderDetailsForEmailResult } from "src/modules/ticket/types";
import { removeDateInParentheses, sortByAgeGroup } from "src/lib/utils";

export const generateContentTable = (
  data: ObtainOrderDetailsForEmailResult,
  viewedAsAdmin: boolean = false
) => {
  const EXCHANGE_RATE = data.euroRate;

  const CALCULATE_PLN_PRICE = (price: number) => {
    return (price * EXCHANGE_RATE).toFixed(2);
  };

  const SORTED_ITEMS = sortByAgeGroup(data.items);

  const GROUPED_ITEMS = Object.values(
    SORTED_ITEMS.reduce((acc, item) => {
      const name = item.title;

      if (!acc[name]) {
        acc[name] = {
          title: name,
          quantity: 0,
        };
      }

      acc[name].quantity += item.quantity;

      return acc;
    }, {} as Record<string, { title: string; quantity: number }>)
  );

  const conditionalSelectedDate = () => {
    if (data.cart.metadata.date_from && data.cart.metadata.date_to) {
      return `${formatDate(data.cart.metadata.date_from)} - ${formatDate(
        data.cart.metadata.date_to
      )}`;
    }

    return formatDate(data.cart.metadata.selected_date);
  };

  const OrderDetails = {
    tour_name: data.ticket?.attraction_name,
    selected_date: conditionalSelectedDate(),
    start_place: data.cart.metadata.start_place?.place,
    start_time: data.cart.metadata.start_time,
    selected_food_options: data.cart.metadata.selected_food_options?.map(
      (option) => ({ name: option.name, quantity: option.quantity })
    ),
    euro_values: {
      order_value: data.summary.accounting_total,
      prepaid_amount: data.summary.transaction_total,
      on_site_amount: data.summary.pending_difference,
    },
    pln_values: {
      order_value: CALCULATE_PLN_PRICE(data.summary.accounting_total),
      prepaid_amount: CALCULATE_PLN_PRICE(data.summary.transaction_total),
      on_site_amount: CALCULATE_PLN_PRICE(data.summary.pending_difference),
    },
    exchange_rate: data.euroRate,
    prepaid_percentage: data.prepaidPercentage,

    additional_customer_note: data.metadata?.additional_customer_note,

    customer: {
      name: `${data.customer.first_name} ${data.customer.last_name}`,
      email: data.customer.email,
      phone: data.customer.phone,
    },
  };

  const itemsQuantity = data.items?.reduce(
    (acc, item) => acc + item.quantity,
    0
  );

  const generateFoodOptionsHtml = () => {
    return OrderDetails.selected_food_options
      ?.map((option) => {
        return /* HTML */ `
          <div style="margin-bottom: 10px;">
            ${option.name}
            <span style="font-weight: bold;"> - ${option.quantity}</span>
          </div>
        `;
      })
      .join("");
  };

  const generateItemsHtml = () => {
    return GROUPED_ITEMS.map((item) => {
      return /* HTML */ `
        <div style="margin-bottom: 10px;">
          ${removeDateInParentheses(item.title)}
          <span style="font-weight: bold;"> - ${item.quantity}</span>
        </div>
      `;
    }).join("");
  };

  return /* HTML */ `
    <table
      border="0"
      cellpadding="0"
      cellspacing="0"
      width="100%"
      style="border-collapse: collapse;"
    >
      <tr>
        <td>
          <!-- Product Table -->
          <table
            border="0"
            cellpadding="0"
            cellspacing="0"
            width="100%"
            style="border-collapse: collapse; border: 1px solid #dddddd;"
          >
            <!-- Header Row -->
            <tr>
              <th
                style="padding: 10px; text-align: left; background-color: #f8f8f8; border: 1px solid #dddddd; font-weight: bold; width: 60%;"
              >
                Produkt
              </th>
              <th
                style="padding: 10px; text-align: center; background-color: #f8f8f8; border: 1px solid #dddddd; font-weight: bold; width: 15%;"
              >
                Ilość
              </th>
              <th
                style="padding: 10px; text-align: right; background-color: #f8f8f8; border: 1px solid #dddddd; font-weight: bold; width: 25%;"
              >
                Cena
              </th>
            </tr>

            <!-- Product Row -->
            <tr>
              <td
                style="padding: 10px; border: 1px solid #dddddd; vertical-align: top;"
              >
                <div style="font-weight: normal; margin-bottom: 10px;">
                  ${OrderDetails.tour_name}
                </div>

                <div style="margin-top: 15px;">
                  <div
                    style="font-weight: bold; margin-bottom: 5px; margin-top: 15px;"
                  >
                    Data atrakcji:
                  </div>
                  <div style="margin-bottom: 10px;">
                    ${OrderDetails.selected_date}
                  </div>

                  <!-- <div
                    style="font-weight: bold; margin-bottom: 5px; margin-top: 15px;"
                  >
                    Liczba uczestników:
                  </div>
                  <div style="margin-bottom: 5px;">${itemsQuantity}</div>
                  -->

                  ${OrderDetails.start_place
                    ? `
                                <div style="font-weight: bold; margin-bottom: 5px; margin-top: 15px;">Miejsce atrakcji:</div>
                                <div style="margin-bottom: 10px;">
                                ${OrderDetails.start_place}
                                </div>
                                `
                    : ""}
                  ${OrderDetails.start_time
                    ? `
                                <div style="font-weight: bold; margin-bottom: 5px; margin-top: 15px;">Godzina atrakcji:</div>
                                <div style="margin-bottom: 10px;">
                                ${OrderDetails.start_time}
                                </div>
                                `
                    : ""}
                  ${OrderDetails.selected_food_options &&
                  OrderDetails.selected_food_options.length > 0
                    ? `
                                <div style="font-weight: bold; margin-bottom: 5px; margin-top: 15px;">Lunch:</div>
                                ${generateFoodOptionsHtml()}
                                `
                    : ""}

                  <div
                    style="font-weight: bold; margin-bottom: 5px; margin-top: 15px;"
                  >
                    Uczestnicy:
                  </div>
                  ${generateItemsHtml()}
                  ${OrderDetails.additional_customer_note
                    ? /* HTML */ `
                        <div
                          style="font-weight: bold; margin-bottom: 5px; margin-top: 15px;"
                        >
                          Uwagi klienta:
                        </div>
                        <div style="margin-bottom: 10px;">
                          ${OrderDetails.additional_customer_note}
                        </div>
                      `
                    : ""}
                </div>
              </td>
              <td
                style="padding: 10px; border: 1px solid #dddddd; text-align: center; vertical-align: top;"
              >
                1
              </td>
              <td
                style="padding: 10px; border: 1px solid #dddddd; text-align: right; vertical-align: top;"
              >
                <div style="display: block;">
                  ${viewedAsAdmin
                    ? OrderDetails.pln_values.order_value
                    : OrderDetails.euro_values.order_value}
                  ${viewedAsAdmin ? "zł" : "EUR"}
                </div>
              </td>
            </tr>

            <!-- Deposit Row -->
            <tr>
              <td style="padding: 10px; border: 1px solid #dddddd;">
                Metoda płatności:
              </td>
              <td style="padding: 10px; border: 1px solid #dddddd;"></td>
              <td
                style="padding: 10px; border: 1px solid #dddddd; text-align: right;"
              >
                Tpay
              </td>
            </tr>

            <tr>
              <td style="padding: 10px; border: 1px solid #dddddd;">
                Zaliczka:
              </td>
              <td style="padding: 10px; border: 1px solid #dddddd;"></td>
              <td
                style="padding: 10px; border: 1px solid #dddddd; text-align: right;"
              >
                <div style="display: block;">
                  ${viewedAsAdmin
                    ? OrderDetails.pln_values.prepaid_amount
                    : OrderDetails.euro_values.prepaid_amount}
                  ${viewedAsAdmin ? "zł" : "EUR"}
                </div>
              </td>
            </tr>

            <!-- On site payment row -- only if on_site_amount is greater than 0 -->
            ${OrderDetails.euro_values.on_site_amount > 0
              ? `
                    <tr>
                        <td style="padding: 10px; border: 1px solid #dddddd;">Do zapłaty na miejscu:</td>
                        <td style="padding: 10px; border: 1px solid #dddddd;"></td>
                        <td style="padding: 10px; border: 1px solid #dddddd; text-align: right;">
                        <div style="display: block;">
                        ${
                          viewedAsAdmin
                            ? OrderDetails.pln_values.on_site_amount
                            : OrderDetails.euro_values.on_site_amount
                        } 
                        ${viewedAsAdmin ? "zł" : "EUR"}
                        </div> 
                        </td>
                    </tr>
                    `
              : ""}
          </table>
        </td>
      </tr>
    </table>

    ${viewedAsAdmin
      ? /* HTML */ `
          <table
            border="0"
            cellpadding="0"
            cellspacing="0"
            style="margin-top: 20px;"
          >
            <tr>
              <td
                style="padding: 20px 0; border-top: 1px solid #dddddd; border-bottom: 1px solid #dddddd;"
              >
                <table
                  border="0"
                  cellpadding="0"
                  cellspacing="0"
                  style="background-color: #ffffff;"
                >
                  <tr>
                    <td>
                      <div>
                        ${OrderDetails.selected_date
                          ? `
                                <div style="font-weight: bold; margin-bottom: 5px; margin-top: 15px;">DAY:</div>
                                <div style="margin-bottom: 10px;">
                                ${OrderDetails.selected_date}
                                </div>
                                `
                          : ""}
                        ${OrderDetails.start_time
                          ? `
                                <div style="font-weight: bold; margin-bottom: 5px; margin-top: 15px;">TIME:</div>
                                <div style="margin-bottom: 10px;">
                                ${OrderDetails.start_time}
                                </div>
                                `
                          : ""}

                        <div
                          style="font-weight: bold; margin-bottom: 5px; margin-top: 15px;"
                        >
                          CLIENTS:
                        </div>

                        ${generateItemsHtml()}

                        <div>
                          <div
                            style="font-weight: bold; margin-bottom: 5px; margin-top: 15px;"
                          >
                            CUSTOMER:
                          </div>
                          <div style="margin-bottom: 10px;">
                            ${OrderDetails.customer.name}
                          </div>
                        </div>
                        <div>
                          <div style="padding-bottom: 5px;">
                            <a href="tel:${OrderDetails.customer.phone}"
                              >${OrderDetails.customer.phone}</a
                            >
                          </div>
                        </div>
                        <div>
                          <div style="padding-bottom: 5px;">
                            <a href="mailto:${OrderDetails.customer.email}"
                              >${OrderDetails.customer.email}</a
                            >
                          </div>
                        </div>
                      </div>
                    </td>
                  </tr>

                  <!-- Price info -->
                  <tr style="margin-top: 15px;">
                    <td style="padding-top: 20px;">
                      <table
                        border="0"
                        cellpadding="0"
                        cellspacing="0"
                        width="100%"
                        style="margin-bottom: 10px;"
                      >
                        <tr>
                          <td width="180">TOTAL</td>
                          <td style="font-weight: bold; text-align: right;">
                            ${OrderDetails.euro_values.order_value} EUR
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>

                  <!-- Deposit -->
                  <tr>
                    <td>
                      <table
                        border="0"
                        cellpadding="0"
                        cellspacing="0"
                        width="100%"
                        style="margin-bottom: 10px;"
                      >
                        <tr>
                          <td width="180">PAID</td>
                          <td style="font-weight: bold; text-align: right;">
                            ${OrderDetails.euro_values.prepaid_amount} EUR
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>

                  <!-- Remaining payment -->
                  <tr>
                    <td>
                      <table
                        border="0"
                        cellpadding="0"
                        cellspacing="0"
                        width="100%"
                      >
                        <tr>
                          <td width="180" style="vertical-align: top;">REST</td>
                          <td
                            style="font-weight: bold; text-align: right; vertical-align: top;"
                          >
                            ${OrderDetails.euro_values.on_site_amount} EUR
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>

                  <tr>
                    <td style="padding-top: 5px;">
                      ${OrderDetails.selected_food_options &&
                      OrderDetails.selected_food_options.length > 0
                        ? `
                                <div style="font-weight: bold; margin-bottom: 5px; margin-top: 15px;">LUNCH:</div>
                                ${generateFoodOptionsHtml()}
                                `
                        : ""}
                      ${OrderDetails.start_place
                        ? `
                                <div style="font-weight: bold; margin-bottom: 5px; margin-top: 15px;">START:</div>
                                <div style="margin-bottom: 10px;">
                                ${OrderDetails.start_place}
                                </div>
                                `
                        : ""}
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        `
      : ""}
  `;
};
