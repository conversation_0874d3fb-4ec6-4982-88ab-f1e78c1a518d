import { getSharedLayout } from "../shared-layout";
import { OrderDTO } from "@medusajs/types";
import { generateContentTable } from "../generate-table";
import { ObtainOrderDetailsForEmailResult } from "src/modules/ticket/types";
import { formatDate } from "src/admin/lib/util/string-dates";

export const adminOrderCanceledTemplate = (data: {
  reservation_number: string;
  attraction_name?: string;
  date?: string;
  orderDetails: ObtainOrderDetailsForEmailResult;
}) => {
  const orderDetails = data.orderDetails;

  const conditionalSelectedDate = () => {

    if (orderDetails.cart.metadata.date_from && orderDetails.cart.metadata.date_to) {
      return `${formatDate(orderDetails.cart.metadata.date_from)} - ${formatDate(orderDetails.cart.metadata.date_to)}`;
    }

    return formatDate(orderDetails.cart.metadata.selected_date);
  };
  
  const formattedData = {
    customer: {
      name: `${orderDetails.customer.first_name} ${orderDetails.customer.last_name}`,
      email: orderDetails.customer.email,
    },
    attraction: {
      name: orderDetails.items.at(0)?.product.tour?.name,
      selected_date: conditionalSelectedDate(),
    },
  };

  const mainContent = /* HTML */ `
    <table
      style="width: 100%; max-width: 600px; border-collapse: collapse; margin-bottom: 20px;"
    >
      <tr>
        <td
          style="padding-bottom: 8px; width: 100px; font-size: 14px; color: #666; font-weight: 500; padding-right: 10px;"
        >
          <p style="margin: 0;">Data atrakcji:</p>
        </td>
        <td
          style="padding-bottom: 8px; font-size: 14px; font-weight: 500; display: flex; align-items: center;"
        >
          <p style="margin: 0;">${formattedData.attraction.selected_date}</p>
          <span style="margin-left: 10px; display: inline-block;">
            <svg
              width="20"
              height="23"
              viewBox="0 0 20 23"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19 9.50708H1M19 12.0071V8.30708C19 6.62692 19 5.78684 18.673 5.14511C18.3854 4.58062 17.9265 4.12168 17.362 3.83406C16.7202 3.50708 15.8802 3.50708 14.2 3.50708H5.8C4.11984 3.50708 3.27976 3.50708 2.63803 3.83406C2.07354 4.12168 1.6146 4.58062 1.32698 5.14511C1 5.78684 1 6.62692 1 8.30708V16.7071C1 18.3873 1 19.2273 1.32698 19.8691C1.6146 20.4336 2.07354 20.8925 2.63803 21.1801C3.27976 21.5071 4.11984 21.5071 5.8 21.5071H10M14 1.50708V5.50708M6 1.50708V5.50708M12.5 18.5071L14.5 20.5071L19 16.0071"
                stroke="black"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </span>
        </td>
      </tr>

      <tr>
        <td colspan="2" style="padding-top: 10px;">
          <p style="margin: 0; font-size: 14px; color: #666;">
            Zamówienie [#${data.reservation_number}] zostało anulowane.
          </p>
        </td>
      </tr>

      <tr>
        <td colspan="2" style="padding-bottom: 20px;">
          <div
            style="border: 1px solid #eaeaea; border-radius: 12px; padding: 20px; margin-top: 10px;"
          >
            <table style="width: 100%;">
              <tr>
                <td
                  style="font-size: 16px; white-space: nowrap; width: 1%; padding-right: 10px; font-weight: 600;"
                >
                  <p style="margin: 0;">${formattedData.customer.name}</p>
                </td>
                <td style="font-size: 14px; color: #666; text-align: left;">
                  <p style="margin: 0;">
                    <a href="mailto:${formattedData.customer.email}">
                      ${formattedData.customer.email}
                    </a>
                  </p>
                </td>
              </tr>
            </table>
          </div>
        </td>
      </tr>
    </table>

    ${generateContentTable(data.orderDetails, true)}
  `;

  return getSharedLayout({
    reservationNumber: data.reservation_number,
    date: data.date,
    mainContent,
    title: "Zamówienie zostało anulowane",
    adminTemplate: true,
  });
};
