import { config } from "src/config";

const COLORS = {
  primary: "#F6B332",
  text: "#333",
  secondary: "#F6B332",
  background: "#fff",
  border: "#e0e0e0",
  buttonText: "#000",
  buttonBackground: "#F6B332",
  reservationBackground: "#000",
  reservationText: "#fff",
};
export { COLORS as MAIL_COLORS };

export const getSharedLayout = (content: {
  attractionName?: string;
  reservationNumber?: string;
  date?: string;
  mainContent?: string;
  title?: string;
  adminTemplate?: boolean;
}) => {
  const v2 = /* HTML */ `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>${content.title || "Wakacyjne Pomysły - Potwierdzenie"}</title>
        <style>
          /* Use web-safe fonts instead of imported Google fonts */
          body {
            font-family: Arial, "Helvetica Neue", Helvetica, sans-serif;
            margin: 0;
            padding: 0;
            color: ${COLORS.text};
            line-height: 1.6;
          }

          h2 {
            font-size: 20px;
            margin-bottom: 15px;
          }

          .text-black {
            color: ${COLORS.buttonText};
          }

          /* Container with fixed width */
          .container {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            border: 1px solid ${COLORS.border};
            border-radius: 8px;
          }

          /* Header using tables instead of flex */
          .header {
            background-color: ${COLORS.primary};
            padding: 20px 30px;
            color: ${COLORS.text};
          }

          .header table {
            width: 100%;
            border-collapse: collapse;
          }

          .header td {
            vertical-align: middle;
          }

          .logo {
            text-align: left;
            font-weight: bold;
          }

          .header__logo-img {
            height: 25px;
            width: auto;
          }

          .date {
            text-align: right;
          }

          /* Confirmation section */
          .confirmation {
            background-color: ${COLORS.primary};
            padding: 20px 30px;
            color: ${COLORS.buttonText};
            position: relative;
          }

          .confirmation h1 {
            font-size: 22px;
            margin: 0 0 10px 0;
          }

          .attraction-name {
            font-size: 16px;
            margin: 0;
          }

          /* Reservation number using tables */
          .reservation-number-container table {
            width: 100%;
            margin-top: 15px;
          }

          .reservation-number {
            background-color: ${COLORS.reservationBackground};
            color: ${COLORS.reservationText};
            padding: 8px 24px;
            border-radius: 8px;
            font-weight: bold;
            font-size: 18px;
            display: inline-block;
          }

          /* Content */
          .content {
            padding: 40px 30px;
            background-color: ${COLORS.background};
          }

          /* Footer */
          .footer {
            padding: 20px 30px;
            text-align: center;
            border-top: 1px solid ${COLORS.border};
          }

          /* Social links */
          .social-links {
            margin-top: 20px;
            text-align: center;
          }

          .social-icon {
            height: 24px;
            width: auto;
            margin: 0 7px;
          }

          .button {
            display: inline-block;
            background-color: ${COLORS.primary};
            color: ${COLORS.buttonText};
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            margin: 15px 0;
          }

          .company {
            margin-top: 30px;
            color: ${COLORS.text};
            font-size: 14px;
            text-align: center;
          }

          .logo-icon {
            vertical-align: middle;
            margin-right: 5px;
            display: inline-block;
          }

          .logo-icon img {
            height: 24px;
            width: auto;
          }

          a {
            color: ${COLORS.primary};
            text-decoration: underline;
          }

          .label {
            font-size: 12px;
          }

          .bold {
            font-weight: bold;
          }

          .semibold {
            font-weight: 600;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <!-- Header with table layout -->
          <div class="header">
            <table cellpadding="0" cellspacing="0" border="0" width="100%">
              <tr>
                <td class="logo" align="left">
                  <img
                    src="${config.MEDUSA_API_ENDPOINT_URL}/static/common/logo.png"
                    alt="wakacyjnepomysly.pl"
                    class="header__logo-img"
                  />
                </td>
                <!--
                <td class="date text-black" align="right">
                  ${new Date().toLocaleDateString("pl-PL", {
                  day: "2-digit",
                  month: "2-digit",
                  year: "numeric",
                })}
                </td>
                -->
              </tr>
            </table>
          </div>

          <!-- Confirmation section -->
          <div class="confirmation">
            <h1>${content.title || "Twoja atrakcja została potwierdzona!"}</h1>
            ${content.attractionName
              ? `<p class="attraction-name">${content.attractionName}</p>`
              : "<p></p>"}
            <div
              class="reservation-number-container"
              style="margin-bottom: -50px"
            >
              <table cellpadding="0" cellspacing="0" border="0" width="100%">
                <tr>
                  <td align="right">
                    <div class="reservation-number">
                      ${content.reservationNumber?.toString().padStart(4, "0")}
                    </div>
                  </td>
                </tr>
                <tr style="margin-top: 5px;">
                  <td align="right">
                    <p class="text-black label" style="font-size: 14px;">
                      Nr rezerwacji
                    </p>
                  </td>
                </tr>
              </table>
            </div>
          </div>

          <!-- Content -->
          <div class="content">${content.mainContent}</div>

          <!-- Footer -->
          <div class="footer">
            ${content.adminTemplate
              ? ""
              : /* HTML */ `
                  <p>Serdecznie pozdrawiamy</p>
                  <a
                    href="${config.MEDUSA_STOREFRONT_URL}"
                    style="color: ${COLORS.primary};"
                    >www.wakacyjnepomysly.pl</a
                  >
                `}
            <!-- Social links using a table -->
            <div class="social-links">
              <table cellpadding="0" cellspacing="0" border="0" align="center">
                <tr>
                  <td>
                    <a
                      href="https://www.facebook.com/wakacyjnepomysly"
                      class="social-icon"
                    >
                      <img
                        src="${config.MEDUSA_API_ENDPOINT_URL}/static/common/fb_black.png"
                        alt="Facebook"
                      />
                    </a>
                  </td>
                  <td width="15"></td>
                  <td>
                    <a
                      href="https://www.instagram.com/wakacyjnepomysly.pl"
                      class="social-icon"
                    >
                      <img
                        src="${config.MEDUSA_API_ENDPOINT_URL}/static/common/ig_black.png"
                        alt="Instagram"
                      />
                    </a>
                  </td>
                  <td width="15"></td>
                  <td>
                    <a href="https://m.me/wakacyjnepomysly" class="social-icon">
                      <img
                        src="${config.MEDUSA_API_ENDPOINT_URL}/static/common/msg_black.png"
                        alt="Messager"
                      />
                    </a>
                  </td>
                </tr>
              </table>
            </div>

            <!-- Company info -->
            <div class="company" style="margin-top: 30px; text-align: center;">
              <p style="margin-bottom: 10px;">LWS Group</p>
              <img
                src="${config.MEDUSA_API_ENDPOINT_URL}/static/common/sygnet.png"
                alt="LWS Group"
                style="display: block; margin: 0 auto;"
              />
            </div>
          </div>
        </div>
      </body>
    </html>
  `;

  return v2;
};
