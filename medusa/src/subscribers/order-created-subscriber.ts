import { SubscriberArgs, SubscriberConfig } from "@medusajs/framework";
import { ContainerRegistrationKeys, Modules } from "@medusajs/framework/utils";
import { ExtendedNotificationDTO } from "src/modules/smtp/types";
import { createTicketWorkflow } from "src/workflows/create-ticket-workflow";
import { config as configEnv } from "src/config";
async function orderCreatedHandler({
  event,
  container,
}: SubscriberArgs<{ id: string }>) {
  const orderId = event.data.id;

  const orderService = container.resolve(Modules.ORDER);
  const order = await orderService.retrieveOrder(orderId);

  const query = container.resolve(ContainerRegistrationKeys.QUERY);

  await createTicketWorkflow.run({
    input: {
      order,
    },
  });

  // const { data } = await query.graph({
  //   entity: "order",
  //   fields: ["items.product.tour.*"],
  //   filters: {
  //     id: {
  //       $eq: orderId,
  //     },
  //   },
  // });

  //   const targetOrder = data.at(0);

  //   const targetTour = targetOrder?.items?.at(0)?.product?.tour?.name;

  //   const notificationModuleService = container.resolve(Modules.NOTIFICATION);

  //   await notificationModuleService.createNotifications({
  //     to: configEnv.ADMIN_EMAIL.join(","),
  //     channel: "email",
  //     template: "admin-new-order",
  //     resource_type: "admin-new-order",
  //     data: {
  //       order_id: orderId,
  //       customer_id: order.customer_id!,
  //       reservation_number: order.display_id.toString(),
  //       attraction_name: targetTour,
  //     },
  //   } satisfies ExtendedNotificationDTO);

  //   await notificationModuleService.createNotifications({
  //     to: order.email!,
  //     channel: "email",
  //     template: "new-order",
  //     resource_type: "new-order",
  //     data: {
  //       order_id: orderId,
  //       customer_id: order.customer_id!,
  //       reservation_number: order.display_id.toString(),
  //       attraction_name: targetTour,
  //     },
  //   } satisfies ExtendedNotificationDTO);

  //   // await orderService.registerFulfillment({
  //   //   order_id: orderId,

  //   //   items: order.items!.map((item) => ({
  //   //     id: item.id,
  //   //     quantity: item.quantity,
  //   //   })),
  //   // });
  // }
}
export default orderCreatedHandler;

export const config: SubscriberConfig = {
  event: "order.placed",
};
