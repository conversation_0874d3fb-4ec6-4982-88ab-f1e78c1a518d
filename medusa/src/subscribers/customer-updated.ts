import { Modules } from "@medusajs/framework/utils";
import { SubscriberArgs, type SubscriberConfig } from "@medusajs/medusa";
import { createTicketWorkflow } from "src/workflows/create-ticket-workflow";

export default async function userCreated<PERSON>andler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string }>) {
  const eventBusService = container.resolve(Modules.EVENT_BUS);

  const { result } = await createTicketWorkflow.run({
    input: {
      order: {
        id: "order_01JPJ4FTKAQ341QAYQS9ERA4DJ",
      },
      additional_data: {},
    },
  });

  console.log("TRIGGERED CUSTOMER UPDATED SUBSCRIBER");
  try {
    await eventBusService.emit({
      name: "order.ticket.created",
      data: {
        customer_id: data.id,
        order_id: "order_01JPJ4FTKAQ341QAYQS9ERA4DJ",
        ticket_id: result.result.ticket.id,
      },
    });
  } catch (error) {
    console.error({ error });
  }

  console.log("TRIGGERED ORDER TICKET CREATED EVENT");
  // data: {
  //   id: data.id,
  //   order_id: "55",
  //   ticket_id: "88",
  // },
}

export const config: SubscriberConfig = {
  event: "customer.updated",
};
