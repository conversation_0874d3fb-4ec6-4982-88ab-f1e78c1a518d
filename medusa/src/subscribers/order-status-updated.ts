import { logger, SubscriberArgs, SubscriberConfig } from "@medusajs/framework";
import { Mo<PERSON>les, OrderStatus } from "@medusajs/framework/utils";
import { OrderStatusDTO } from "src/admin/widgets/order-status/types";
import {
  NotificationEventDataDTO,
  NotificationTemplate,
} from "src/modules/smtp/types";
import { config as configEnv } from "../config";

export type OrderStatusUpdatedPayload = {
  id: string;
  status: OrderStatus;
  email_to: string;
  metadata_status: OrderStatusDTO["order_status"];
  data: NotificationEventDataDTO;
};

const getMatchingTemplate = (
  status: OrderStatusDTO["order_status"]
): NotificationTemplate => {
  switch (status) {
    case "new_order":
      return "new-order";
    case "during_realization":
      return "your-order-is-being-processed";
    case "canceled":
      return "your-order-has-been-canceled";
    case "refunded":
      return "your-order-has-been-refunded";
    case "feedback_requested":
      return "waiting-for-your-feedback";
    case "completed":
      return "your-order-has-been-completed";

    default:
      throw new Error(`No template found for status: ${status}`);
  }
};

export default async function orderUpdatedHandler({
  event: { data },
  container,
}: SubscriberArgs<OrderStatusUpdatedPayload>) {
  try {
    if (!data?.id) {
      throw new Error("No order ID provided in event data");
    }

    const STATUSES_WITHOUT_NOTIFICATION: OrderStatusDTO["order_status"][] = [
      "completed",
    ];

    if (STATUSES_WITHOUT_NOTIFICATION.includes(data.metadata_status)) {
      return;
    }

    const notificationModuleService = container.resolve(Modules.NOTIFICATION);

    const template = getMatchingTemplate(data.metadata_status);

    // Canceled order is only submitted to admin
    if (template === "your-order-has-been-canceled") {
      await notificationModuleService.createNotifications({
        to: configEnv.ADMIN_EMAIL.join(","),
        channel: "email",
        template: "admin-order-canceled",
        data: {
          reservation_number: data.data.reservation_number,
          customer_id: data.data.customer_id,
          attraction_name: data.data.attraction_name,
          date: data.data.date,
          order_id: data.id,
        } satisfies NotificationEventDataDTO,
      });
    }

    if (template === "new-order") {
      await notificationModuleService.createNotifications({
        to: configEnv.ADMIN_EMAIL.join(","),
        channel: "email",
        template: "admin-new-order",
        data: {
          order_id: data.id,
          customer_id: data.data.customer_id,
          reservation_number: data.data.reservation_number,
          attraction_name: data.data.attraction_name,
        } satisfies NotificationEventDataDTO,
      });
    }

    await notificationModuleService.createNotifications({
      to: data.email_to,
      channel: "email",
      template,
      resource_type: template,
      data: {
        customer_id: data.data.customer_id,
        reservation_number: data.data.reservation_number,
        attraction_name: data.data.attraction_name,
        date: data.data.date,
        order_id: data.id,
      } as NotificationEventDataDTO,
    });
  } catch (error) {
    logger.error(error);
  }
}

export const config: SubscriberConfig = {
  event: "order.status.updated",
};
