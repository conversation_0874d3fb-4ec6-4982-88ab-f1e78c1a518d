import { SubscriberArgs, SubscriberConfig } from "@medusajs/framework";
import { ContainerRegistrationKeys, Modules } from "@medusajs/framework/utils";
import { config as configEnv } from "src/config";
import { ExtendedNotificationDTO } from "src/modules/smtp/types";
async function orderCreatedHandler({
  event,
  container,
}: SubscriberArgs<{ id: string }>) {
  const orderId = event.data.id;

  // sleep for 3 seconds to ensure order is being updated before emitting event
  await new Promise((resolve) => setTimeout(resolve, 3000));

  const orderService = container.resolve(Modules.ORDER);
  const order = await orderService.retrieveOrder(orderId);

  const query = container.resolve(ContainerRegistrationKeys.QUERY);

  const { data } = await query.graph({
    entity: "order",
    fields: ["ticket.attraction_name"],
    filters: {
      id: {
        $eq: orderId,
      },
    },
  });

  const targetOrder = data.at(0);

  const targetTour = targetOrder?.ticket?.attraction_name;

  const notificationModuleService = container.resolve(Modules.NOTIFICATION);

  await notificationModuleService.createNotifications({
    to: configEnv.ADMIN_EMAIL.join(","),
    channel: "email",
    template: "admin-new-order",
    resource_type: "admin-new-order",
    data: {
      order_id: orderId,
      customer_id: order.customer_id!,
      reservation_number: order.display_id.toString(),
      attraction_name: targetTour || "",
    },
  } satisfies ExtendedNotificationDTO);

  await notificationModuleService.createNotifications({
    to: order.email!,
    channel: "email",
    template: "new-order",
    resource_type: "new-order",
    data: {
      order_id: orderId,
      customer_id: order.customer_id!,
      reservation_number: order.display_id.toString(),
      attraction_name: targetTour || "",
    },
  } satisfies ExtendedNotificationDTO);
}

export default orderCreatedHandler;

export const config: SubscriberConfig = {
  event: "order-payment-captured",
};
