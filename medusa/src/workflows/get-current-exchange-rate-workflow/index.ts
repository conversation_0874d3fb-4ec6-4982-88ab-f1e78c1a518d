import {
  createWorkflow,
  WorkflowResponse,
} from "@medusajs/framework/workflows-sdk";

import {
  fetchExchangeRateStep,
  TFetchExchangeRateInput,
} from "../obtain-exchange-rate/steps/fetch-exchange-rate-step";

export const getCurrentExchangeRateWorkflow = createWorkflow(
  "get-current-exchange-rate",
  (input: TFetchExchangeRateInput) => {
    const exchangeRate = fetchExchangeRateStep(input);

    return new WorkflowResponse({
      exchangeRate,
    });
  }
);
