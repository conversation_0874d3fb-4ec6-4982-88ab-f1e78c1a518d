import { container } from "@medusajs/framework";
import { ContainerRegistrationKeys, Modules } from "@medusajs/framework/utils";
import { createStep, StepResponse } from "@medusajs/framework/workflows-sdk";

type UpdateCartByCustomerWorkflowStepInput = {
  customer_id: string;
  cart_id: string;
};

export const updateCartByCustomerWorkflowStep = createStep(
  "update-cart-by-customer-workflow-step",
  async (input: UpdateCartByCustomerWorkflowStepInput) => {
    const cartService = container.resolve(Modules.CART);

    const customerDetails = await container
      .resolve(Modules.CUSTOMER)
      .retrieveCustomer(input.customer_id);

    const updatedCart = await cartService.updateCarts(input.cart_id, {
      customer_id: input.customer_id,
      email: customerDetails.email,
      metadata: {
        phone: customerDetails.phone,
      },
    });

    return new StepResponse({ updatedCart });
  }
);
