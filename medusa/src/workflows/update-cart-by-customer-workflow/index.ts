import {
  createWorkflow,
  WorkflowResponse,
} from "@medusajs/framework/workflows-sdk";
import { updateCartByCustomerWorkflowStep } from "./steps/update-cart-by-customer-workflow-step";

type UpdateCartByCustomerWorkflowInput = {
  customer_id: string;
  cart_id: string;
};

export const updateCartByCustomerWorkflow = createWorkflow(
  "update-cart-by-customer-workflow",
  (input: UpdateCartByCustomerWorkflowInput) => {
    const response = updateCartByCustomerWorkflowStep({
      customer_id: input.customer_id,
      cart_id: input.cart_id,
    });

    return new WorkflowResponse({
      response,
    });
  }
);
