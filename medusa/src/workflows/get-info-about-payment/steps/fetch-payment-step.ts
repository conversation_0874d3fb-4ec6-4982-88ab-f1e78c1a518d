import { container } from "@medusajs/framework";
import { ContainerRegistrationKeys } from "@medusajs/framework/utils";
import { createStep, StepResponse } from "@medusajs/framework/workflows-sdk";
type WorkflowInput = {
  payment_colletions_id: string;
};

type PaymentInfo = {
  data: {
    additional_description?: string;
  };
};

export const fetchPaymentStep = createStep(
  "fetch-payment-step",
  async ({ payment_colletions_id }: WorkflowInput) => {
    const query = container.resolve(ContainerRegistrationKeys.QUERY);

    const { data: payment } = await query.graph({
      entity: "payment",
      fields: ["*", "payment_collection.*"],
      filters: {
        payment_collection_id: {
          $eq: payment_colletions_id,
        },
      },
    });

    const paymentInfo = payment?.at(0) as unknown as PaymentInfo;

    return new StepResponse({ paymentInfo });
  }
);
