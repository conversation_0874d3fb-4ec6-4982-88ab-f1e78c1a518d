import {
  createWorkflow,
  WorkflowResponse,
} from "@medusajs/framework/workflows-sdk";
import { fetchPaymentStep } from "./steps/fetch-payment-step";

type WorkflowInput = {
  payment_colletions_id: string;
};

const getInfoAboutPaymentWorkflow = createWorkflow(
  "get-info-about-payment",
  (input: WorkflowInput) => {
    const data = fetchPaymentStep(input);
    return new WorkflowResponse(data);
  }
);

export default getInfoAboutPaymentWorkflow;
