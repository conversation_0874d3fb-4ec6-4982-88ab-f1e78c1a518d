import { updateProductsWorkflow } from "@medusajs/medusa/core-flows";
import { updateTourFromProductWorkflow, UpdateTourFromProductWorkflowInput } from "../update-tour-from-product";

updateProductsWorkflow.hooks.productsUpdated(
  async ({ products, additional_data }, { container }) => {
    const workflow = updateTourFromProductWorkflow(container);
    
    for (const product of products) {
      await workflow.run({
        input: {
          product,
          additional_data,
        } as UpdateTourFromProductWorkflowInput,
      });
    }
  }
);
