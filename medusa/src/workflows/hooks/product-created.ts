import { createProductsWorkflow } from "@medusajs/medusa/core-flows";
import {
  createTourFromProductWorkflow,
  CreateTourFromProductWorkflowInput,
} from "src/workflows/create-tour-from-product";

createProductsWorkflow.hooks.productsCreated(
  async ({ products, additional_data }, { container }) => {
    const workflow = createTourFromProductWorkflow(container);

    for (const product of products) {
      await workflow.run({
        input: {
          product,
          additional_data,
        } as CreateTourFromProductWorkflowInput,
      });
    }
  }
);


