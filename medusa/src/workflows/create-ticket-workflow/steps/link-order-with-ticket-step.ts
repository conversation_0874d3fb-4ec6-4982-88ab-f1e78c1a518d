import { container } from "@medusajs/framework";
import { ContainerRegistrationKeys, Modules } from "@medusajs/framework/utils";
import { createStep, StepResponse } from "@medusajs/framework/workflows-sdk";
import { ILinkModule } from "@medusajs/types";
import { TICKET_MODULE } from "src/modules/ticket";

type LinkOrderWithTicketStepInput = {
  order_id: string;
  ticket_id: string;
};

export const linkOrderWithTicketStep = createStep(
  "link-order-with-ticket-step",
  async (input: LinkOrderWithTicketStepInput) => {
    const link = container.resolve(ContainerRegistrationKeys.LINK);

    await link.create([
      {
        [Modules.ORDER]: {
          order_id: input.order_id,
        },
        [TICKET_MODULE]: {
          ticket_id: input.ticket_id,
        },
      },
    ]);

    return new StepResponse(true);
  }
);
