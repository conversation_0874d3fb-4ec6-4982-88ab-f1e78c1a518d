import { container, logger } from "@medusajs/framework";
import { ContainerRegistration<PERSON><PERSON><PERSON>, Mo<PERSON>les } from "@medusajs/framework/utils";
import { createStep, StepResponse } from "@medusajs/framework/workflows-sdk";
import {
  CustomerDTO,
  IEventBusModuleService,
  InferTypeOf,
} from "@medusajs/types";
import { TICKET_MODULE } from "src/modules/ticket";
import OrderTicketService from "src/modules/ticket/service";
import { CreateTicketWorkflowInput } from "..";
import { TicketDTO } from "src/modules/ticket/types";
import { Order } from ".medusa/types/query-entry-points";

type CreateOrderTicketStepInput = {
  order_id: string;
};

interface Ticket {
  id: string;
  map: string;
  meeting_place: string;
  meeting_time: string;
  attraction_time: string;
  additional_meeting_details: string;
  intermediary_name: string;
  organizer_name: string;
  organizer_contact: string;
  ship_or_transport_description: string;
  what_to_bring: string;
  where_to_park: string;
  additional_info: string;
  link_to_download: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  tour_id_id: string;
}

interface Tour {
  id: string;
  ticket: Ticket;
}

interface Product {
  id: string;
  title: string;
  handle: string;
  subtitle: string | null;
  description: string;
  is_giftcard: boolean;
  status: string;
  thumbnail: string;
  weight: number | null;
  length: number | null;
  height: number | null;
  width: number | null;
  origin_country: string | null;
  hs_code: string | null;
  mid_code: string | null;
  material: string | null;
  discountable: boolean;
  external_id: string | null;
  metadata: any | null;
  type_id: string | null;
  type: any | null;
  collection_id: string | null;
  collection: any | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  tour: Tour;
}

interface Item {
  product_id: string;
  product: Product;
}

interface CartMetadata {
  phone: string;
  product_type: string;
  attraction_id: string;
  attraction_name: string;
  selected_date: string;
  date_from: string;
  date_to: string;
  start_time: string;
  selected_food_options: string;
  start_place: string;
}

interface Cart {
  metadata: CartMetadata;
  id: string;
}

interface ExtendedOrder extends Omit<Order, "items" | "cart" | "customer"> {
  id: string;
  items: Item[];
  cart: Cart;
  customer: CustomerDTO;
  display_id: number;
  summary: Order["summary"] & {
    accounting_total: number;
  };
}

export const createTicketStep = createStep(
  "create-ticket-step",
  async (input: CreateTicketWorkflowInput) => {
    const orderTicketService: OrderTicketService =
      container.resolve(TICKET_MODULE);
    const eventBusService: IEventBusModuleService = container.resolve(
      Modules.EVENT_BUS
    );

    const query = container.resolve(ContainerRegistrationKeys.QUERY);

    // Retrieve the order with its cart and metadata
    const {
      data: [order],
    } = await query.graph({
      entity: "order",
      fields: [
        "*",
        "summary.*",
        "customer.id",
        "cart.email",
        "cart.metadata",
        "customer.*",
        "payment_collections.*",
        "items.product.*",
        "items.product.tour.ticket.*",
      ],
      filters: {
        id: input.order.id,
      },
    });

    const typedOrder = order as unknown as ExtendedOrder;

    const OrderPayload: TicketDTO = {
      where_to_park: typedOrder?.items?.[0]?.product?.tour.ticket.where_to_park,
      what_to_bring: typedOrder?.items?.[0]?.product?.tour.ticket.what_to_bring,
      ship_or_transport_description:
        typedOrder?.items?.[0]?.product?.tour.ticket
          .ship_or_transport_description,

      link_to_download:
        typedOrder?.items?.[0]?.product?.tour.ticket.link_to_download,

      amount_paid: typedOrder.summary?.paid_total ?? 0,
      total_to_pay: typedOrder.summary?.accounting_total ?? 0,
      reservation_number: typedOrder.display_id as unknown as number,

      customer_name:
        typedOrder.customer?.first_name + " " + typedOrder.customer?.last_name,
      customer_email: typedOrder.customer?.email || null,
      customer_phone: typedOrder.customer?.phone || null,
      customer_address: typedOrder.customer?.addresses?.[0]?.address_1 || null,
      customer_city: typedOrder.customer?.addresses?.[0]?.city || null,
      customer_zip: typedOrder.customer?.addresses?.[0]?.postal_code || null,
      customer_country:
        typedOrder.customer?.addresses?.[0]?.country_code || null,

      additional_meeting_details:
        typedOrder.items[0].product.tour.ticket.additional_meeting_details,
      additional_info: typedOrder.items[0].product.tour.ticket.additional_info,
      attraction_id: typedOrder.cart.metadata.attraction_id,
      attraction_name: typedOrder.cart.metadata.attraction_name,

      map: typedOrder.items[0].product.tour.ticket.map,
      meeting_place: typedOrder.items[0].product.tour.ticket.meeting_place,
      meeting_time: typedOrder.items[0].product.tour.ticket.meeting_time,
      attraction_time: typedOrder.items[0].product.tour.ticket.attraction_time,
      intermediary_name:
        typedOrder.items[0].product.tour.ticket.intermediary_name,
      organizer_name: typedOrder.items[0].product.tour.ticket.organizer_name,
      organizer_contact:
        typedOrder.items[0].product.tour.ticket.organizer_contact,

      product_type: typedOrder.items[0].product?.type,
      selected_date: typedOrder.cart.metadata?.selected_date,
      date_from: typedOrder.cart.metadata?.date_from,
      date_to: typedOrder.cart.metadata?.date_to,
      start_time: typedOrder.cart.metadata?.start_time,
      selected_food_options: typedOrder.cart.metadata?.selected_food_options,
      start_place: typedOrder.cart.metadata?.start_place,

      tour_id: typedOrder.items[0].product.tour.id,
      meeting_image: typedOrder.items[0].product.tour.ticket?.meeting_image,
    };

    const ticketResult = await orderTicketService.createTickets(OrderPayload);

    const result = {
      ticket: ticketResult,
    };

    logger.info(`ticket created - ${JSON.stringify(ticketResult)}`);

    await eventBusService.emit({
      name: "order.ticket.created",
      data: {
        customer_id: typedOrder.customer.id,
        order_id: typedOrder.id,
        ticket_id: ticketResult.id,
      },
    });

    return new StepResponse(result);
  }
);
