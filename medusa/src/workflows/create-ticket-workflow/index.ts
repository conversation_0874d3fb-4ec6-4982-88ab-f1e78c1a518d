import {
  createWorkflow,
  when,
  WorkflowResponse,
} from "@medusajs/framework/workflows-sdk";
import { createTicketStep } from "./steps/create-ticket-step";
import { OrderDTO } from "@medusajs/types";
import { linkOrderWithTicketStep } from "./steps/link-order-with-ticket-step";

export type OrderAdditionalData = {};

export type CreateTicketWorkflowInput = {
  order: OrderDTO;
  additional_data: OrderAdditionalData;
};

export const createTicketWorkflow = createWorkflow(
  "create-ticket-workflow",
  (input: CreateTicketWorkflowInput) => {
    const { ticket } = createTicketStep({
      order: input.order,
      additional_data: input.additional_data,
    });

    console.log(
      `link data - order_id: ${JSON.stringify(
        input.order
      )} ticket_id: ${JSON.stringify(ticket)}`
    );

    when({ ticket }, ({ ticket }) => ticket !== undefined).then(() =>
      linkOrderWithTicketStep({
        order_id: input.order.id,
        ticket_id: ticket.id,
      })
    );

    return new WorkflowResponse({ result: ticket });
  }
);
