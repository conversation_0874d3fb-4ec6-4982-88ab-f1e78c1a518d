import {
  createWorkflow,
  WorkflowResponse,
} from "@medusajs/framework/workflows-sdk";
import {
  updatePaymentSessionStep,
  UpdatePaymentSessionStepInput,
} from "./steps/update-payment-session-step";

export const updatePaymentSessionToMatchAmount = createWorkflow(
  "update-payment-session-to-match-amount",
  (input: UpdatePaymentSessionStepInput) => {
    const session = updatePaymentSessionStep({
      amount: input.amount,
      session_id: input.session_id,
    });

    return new WorkflowResponse({
      session,
    });
  }
);
