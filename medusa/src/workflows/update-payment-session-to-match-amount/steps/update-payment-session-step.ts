import { Modu<PERSON> } from "@medusajs/framework/utils";
import { createStep, StepResponse } from "@medusajs/framework/workflows-sdk";
import { IPaymentModuleService } from "@medusajs/types";

export type UpdatePaymentSessionStepInput = {
  session_id: string;
  amount: number;
};

export const updatePaymentSessionStep = createStep(
  "update-payment-session-step",
  async (input: UpdatePaymentSessionStepInput, { container }) => {
    const sessionService: IPaymentModuleService = container.resolve(
      Modules.PAYMENT
    );

    const paymentSession = await sessionService.retrievePaymentSession(
      input.session_id
    );

    const result = await sessionService.updatePaymentSession({
      ...paymentSession,
      amount: input.amount,
    });

    return new StepResponse(result);
  }
);
