// @prefix workflow-index
// @description Create a new workflow

import {
  createWorkflow,
  when,
  WorkflowResponse,
} from "@medusajs/framework/workflows-sdk";
import {
  findOrCreateCustomerStep,
  updateCustomersStep,
} from "@medusajs/medusa/core-flows";

type FindOrCreateCustomerWorkflowInput = {
  email: string;
  payment_id: string;
  phone_number: string;
  first_name: string;
  last_name: string;
};

export const findOrCreateCustomerWorkflow = createWorkflow(
  "find-or-create-customer-workflow",
  (input: FindOrCreateCustomerWorkflowInput) => {
    const response = findOrCreateCustomerStep({
      email: input.email,
    });

    when({ response }, ({ response }) => response.customer !== undefined).then(
      () => {
        const newCustomer = updateCustomersStep({
          selector: {
            id: response?.customer?.id,
          },
          update: {
            phone: input.phone_number,
            first_name: input.first_name,
            last_name: input.last_name,
          },
        });

        return new WorkflowResponse({
          customer: newCustomer,
        });
      }
    );

    return new WorkflowResponse({
      customer: response.customer,
    });
  }
);
