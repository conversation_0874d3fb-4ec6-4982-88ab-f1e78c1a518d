import { container } from "@medusajs/framework";
import { ContainerRegistrationKeys, Mo<PERSON><PERSON> } from "@medusajs/framework/utils";
import { createStep, StepResponse } from "@medusajs/framework/workflows-sdk";

type LinkUserWithPaymentCollectionStepInput = {
  payment_id: string;
  customer_id: string;
};

export const linkUserWithPaymentCollectionStep = createStep(
  "link-user-with-payment-collection-step",
  async (input: LinkUserWithPaymentCollectionStepInput) => {
    const link = container.resolve(ContainerRegistrationKeys.LINK);

    const result = await link.create({
      [Modules.PAYMENT]: {
        payment_collection_id: input.payment_id,
      },
      [Modules.CUSTOMER]: {
        customer_id: input.customer_id,
      },
    });

    return new StepResponse({
      result,
    });
  }
);
