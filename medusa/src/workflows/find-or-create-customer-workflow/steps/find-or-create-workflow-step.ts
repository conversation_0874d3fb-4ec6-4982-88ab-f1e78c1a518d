import { ContainerRegistrationKeys, Modu<PERSON> } from "@medusajs/framework/utils";
import { createStep, StepResponse } from "@medusajs/framework/workflows-sdk";
import { CreateCustomerDTO } from "@medusajs/types";

export const findOrCreateWorkflowStep = createStep(
  "find-or-create-workflow-step",
  async (input: CreateCustomerDTO, { container }) => {
    const query = container.resolve(ContainerRegistrationKeys.QUERY);

    const customer = await query.graph({
      entity: "customer",
      filters: {
        email: input.email,
      },
    });

    if (customer) {
      return new StepResponse({ customer: customer.data });
    }

    const customerService = container.resolve(Modules.CUSTOMER);

    const createdCustomer = await customerService.createCustomers({
      ...input,
    });

    return new StepResponse({ customer: createdCustomer });
  }
);
