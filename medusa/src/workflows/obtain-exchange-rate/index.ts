import {
  createWorkflow,
  transform,
  WorkflowResponse,
} from "@medusajs/framework/workflows-sdk";
import {
  fetchExchangeRateStep,
  TFetchExchangeRateInput,
} from "./steps/fetch-exchange-rate-step";
import {
  fetchTourPrepaidPercentStep,
  TFetchTourPrepaidPercentInput,
} from "./steps/fetch-tour-prepaid-percent";
import { BigNumberInput } from "@medusajs/types";

export const obtainExchangeRateWorkflow = createWorkflow(
  "obtain-exchange-rate",
  (
    input: TFetchExchangeRateInput &
      TFetchTourPrepaidPercentInput & { amount: BigNumberInput }
  ) => {
    const exchangeRate = fetchExchangeRateStep(input);
    const prepaidPercent = fetchTourPrepaidPercentStep(input);
    const amount = transform({ amount: input.amount }, (data) => data.amount);

    const transformedExchangeRate = transform(
      { exchangeRate },
      (data) => data.exchangeRate
    );
    const transformedPrepaidPercent = transform(
      { prepaidPercent },
      (data) => data.prepaidPercent
    );

    const calculatedAmount = transform(
      { amount, transformedExchangeRate, transformedPrepaidPercent },
      (data) => {
        const prepaidPercent = data.transformedPrepaidPercent ?? 100;
        const prepaidPercentToFloat = prepaidPercent / 100;
        return (
          Number(data.amount) *
          Number(data.transformedExchangeRate.rate) *
          Number(prepaidPercentToFloat)
        );
      }
    );

    const calculatedAmountEuro = transform(
      { amount, calculatedAmount, transformedPrepaidPercent },
      (data) => {
        const prepaidPercent = data.transformedPrepaidPercent ?? 100;
        const prepaidPercentToFloat = prepaidPercent / 100;
        return Number(data.amount) * Number(prepaidPercentToFloat);
      }
    );

    const roundedToTwoDecimals = transform(
      { calculatedAmount },
      (data) => Math.round(data.calculatedAmount * 100) / 100
    );

    const roundedEuroToTwoDecimals = transform(
      { calculatedAmountEuro },
      (data) => Math.round(data.calculatedAmountEuro * 100) / 100
    );

    return new WorkflowResponse({
      calculatedAmount: roundedToTwoDecimals,
      calculatedAmountEuro: roundedEuroToTwoDecimals,
      exchangeRate: transformedExchangeRate,
      prepaidPercentage: transformedPrepaidPercent,
    });
  }
);
