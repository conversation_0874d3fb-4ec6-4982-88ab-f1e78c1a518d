import { createStep, StepResponse } from "@medusajs/framework/workflows-sdk";
import { EXCHANGE_RATE_MODULE } from "src/modules/exchange-rate";
import ExchangeRateService from "src/modules/exchange-rate/service";

export type TFetchExchangeRateInput = {
  currency_code: string;
};

export const fetchExchangeRateStep = createStep(
  "fetch-exchange-rate",
  async (input: TFetchExchangeRateInput, { container }) => {
    const exchangeRateService: ExchangeRateService =
      container.resolve(EXCHANGE_RATE_MODULE);

    const [result] = await exchangeRateService.listExchangeRates({
      currency_code: input.currency_code.toLocaleLowerCase(),
    });

    return new StepResponse(result);
  }
);
