import { createStep, StepResponse } from "@medusajs/framework/workflows-sdk";
import { TOUR_MODULE } from "src/modules/tours";
import TourService from "src/modules/tours/service";

export type TFetchTourPrepaidPercentInput = {
  tour_id: string;
  date_for_advance_calculation: string;
};

export const fetchTourPrepaidPercentStep = createStep(
  "fetch-tour-prepaid-percent",
  async (input: TFetchTourPrepaidPercentInput, { container }) => {
    const tourService: TourService = container.resolve(TOUR_MODULE);

    const [tour] = await tourService.listTours(
      {
        id: input.tour_id,
      },
      {
        relations: ["pricing"],
      }
    );

    const prepaidPercentNormal = tour.pricing.prepaid_percentage;
    const prepaidPercentSeasonHigh =
      tour.pricing.prepaid_percentage_high_season;

    const dateForAdvanceCalculation = new Date(
      input.date_for_advance_calculation
    );

    const isHighSeason =
      dateForAdvanceCalculation.getMonth() >= 6 &&
      dateForAdvanceCalculation.getMonth() <= 9;

    console.log(prepaidPercentNormal, prepaidPercentSeasonHigh);
    // const prepaidPercent = isHighSeason
    //   ? prepaidPercentSeasonHigh
    //   : prepaidPercentNormal;

    const prepaidPercent = prepaidPercentNormal;

    return new StepResponse(prepaidPercent);
  }
);
