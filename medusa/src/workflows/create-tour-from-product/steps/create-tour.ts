import { createStep, StepResponse } from "@medusajs/framework/workflows-sdk";
import { TOUR_MODULE } from "src/modules/tours";
import TourService from "src/modules/tours/service";
import { TCreateTourFormBody } from "src/admin/components/tours/schemas";

type CreateTourStepInput = {
  additional_data: TCreateTourFormBody;
};

export const createTourStep = createStep(
  "create-tour",
  async (data: CreateTourStepInput, { container }) => {
    const tourService: TourService = container.resolve(TOUR_MODULE);

    // First, create the tour
    const tour = await tourService.createTours(data.additional_data);

    // Then, create related entities with the tour_id
    const [foodOptions, blockedDatesByMonth, availableStartPlaces] =
      await Promise.all([
        data.additional_data.food_options.map((foodOption) =>
          tourService.createTourFoodOptions({
            price: foodOption?.price,
            tour_id_id: tour.id,
            name: foodOption?.name,
            description: foodOption?.description,
          })
        ),

        data.additional_data.blocked_dates_by_month?.map((blockedDateByMonth) =>
          tourService.createTourBlockedDatesByMonths({
            tour_id_id: tour.id,
            month: blockedDateByMonth.month,
            days: blockedDateByMonth.days,
          })
        ),
        data.additional_data.available_start_places?.map(
          (availableStartPlace) =>
            tourService.createTourAvailableStartPlaces({
              tour_id_id: tour.id,
              place: availableStartPlace?.place,
              rich_text_content: availableStartPlace?.rich_text_content,
            })
        ),
      ]);

    const result = {
      tour,
      foodOptions,
      blockedDatesByMonth,
      availableStartPlaces,
    };

    return new StepResponse(result, result);
  },

  async (custom, { container }) => {
    const tourService: TourService = container.resolve(TOUR_MODULE);

    await tourService.deleteTours(custom?.tour?.id);
  }
);
