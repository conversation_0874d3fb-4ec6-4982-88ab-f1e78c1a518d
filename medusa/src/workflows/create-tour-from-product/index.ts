import {
  createWorkflow,
  transform,
  when,
  WorkflowResponse,
} from "@medusajs/framework/workflows-sdk";
import { ProductDTO } from "@medusajs/types";
import { createTourStep } from "./steps/create-tour";
import { createRemoteLinkStep } from "@medusajs/medusa/core-flows";
import { Modules } from "@medusajs/framework/utils";
import { TOUR_MODULE } from "src/modules/tours";
import { TCreateTourFormBody } from "src/admin/components/tours/schemas";

export type CreateTourFromProductWorkflowInput = {
  product: ProductDTO;
  additional_data: TCreateTourFormBody;
};

export const createTourFromProductWorkflow = createWorkflow(
  "create-tour-from-product",
  (input: CreateTourFromProductWorkflowInput) => {
    const value = transform({ input }, (data) => data.input.additional_data);

    const tour = createTourStep({
      additional_data: value,
    });

    when({ tour }, ({ tour }) => tour !== undefined).then(() =>
      createRemoteLinkStep([
        {
          [Modules.PRODUCT]: {
            product_id: input.product.id,
          },
          [TOUR_MODULE]: {
            tour_id: tour.tour.id,
          },
        },
      ])
    );

    return new WorkflowResponse({
      tour,
    });
  }
);
