import {
  createWorkflow,
  transform,
  WorkflowResponse,
} from "@medusajs/framework/workflows-sdk";
import {
  addOrderTransactionStep,
  refundPaymentStep,
} from "@medusajs/medusa/core-flows";

export type UpdatePaymentCollectionAfterOrderLineItemChangeWorkflowInput = {
  payment_id: string;
  calculated_refund_amount: number;
  order_id: string;
};

export const updatePaymentCollectionAfterOrderLineItemChangeWorkflow =
  createWorkflow(
    "update-payment-collection-after-order-line-item-change",
    (input: UpdatePaymentCollectionAfterOrderLineItemChangeWorkflowInput) => {
      const payment = refundPaymentStep({
        payment_id: input.payment_id,
        amount: input.calculated_refund_amount,
      });

      const minusCalculatedRefundAmount = transform(
        { calculated_refund_amount: input.calculated_refund_amount },
        (data) => {
          return {
            calculated_refund_amount: data.calculated_refund_amount * -1,
          };
        }
      );

      const paymentContent = transform(payment, (data) => {
        return {
          ...data,
          amount: minusCalculatedRefundAmount.calculated_refund_amount,
        };
      });

      const uniqueReferenceId = transform(paymentContent, (data) => {
        return {
          ...data,
          reference_id: data.id + new Date().getTime(),
        };
      });

      addOrderTransactionStep({
        order_id: input.order_id,
        currency_code: "EUR",
        reference: "refund",
        amount: minusCalculatedRefundAmount.calculated_refund_amount,
        reference_id: uniqueReferenceId.reference_id,
      });

      return new WorkflowResponse({
        result: payment,
      });
    }
  );
