import { createStep, StepResponse } from "@medusajs/workflows-sdk";
import { Modules } from "@medusajs/framework/utils";
import ReportModuleService from "src/modules/report/service";
import { REPORT_MODULE } from "src/modules/report";
import { OrderWithMetadata } from "src/admin/types";
import { container } from "@medusajs/framework";

export interface SalesReportInput {
  dateFrom: string;
  dateTo: string;
}

export const generateReportStep = createStep(
  "generate-report-step",
  async (input: SalesReportInput) => {
    const orderModuleService = container.resolve(Modules.ORDER);

    const orders = (await orderModuleService.listOrders(
      {
        created_at: {
          $gte: input.dateFrom,
          $lte: input.dateTo,
        },
      },
      {
        relations: ["summary", "metadata"],
      }
    )) as OrderWithMetadata[];

    const reportService = container.resolve(
      REPORT_MODULE
    ) as ReportModuleService;

    const report = await reportService.generateReport(orders);

    return new StepResponse(report);
  }
);
