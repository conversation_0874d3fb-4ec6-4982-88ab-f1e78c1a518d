import {
  createWorkflow,
  WorkflowResponse,
} from "@medusajs/framework/workflows-sdk";
import { useQueryGraphStep } from "@medusajs/medusa/core-flows";

export const generateTicketWorkflow = createWorkflow(
  "generate-ticket-workflow",
  (input: ObtainOrderDetailsInput) => {
    const target = "prod_01JKT0A9JM15GXGZD6NBFCZ8C4";

    const tourDetails = useQueryGraphStep({
      entity: "product",
      fields: ["tour.*", "tour.ticket.*"],
      filters: {
        id: target,
      },
    });

    return new WorkflowResponse({
      tourDetails,
    });
  }
);
