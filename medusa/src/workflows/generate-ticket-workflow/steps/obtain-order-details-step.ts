// import { ContainerRegistration<PERSON><PERSON><PERSON>, Modules } from "@medusajs/framework/utils";
// import { createStep, StepResponse } from "@medusajs/framework/workflows-sdk";
// import { useQueryGraphStep } from "@medusajs/medusa/core-flows";

// export type ObtainOrderDetailsInput = {
//   order_id: string;
// };

// export const obtainOrderDetailsStep = createStep(
//   "obtain-order-details-step",
//   async (input: ObtainOrderDetailsInput, context) => {
//     const target = "prod_01JKT0A9JM15GXGZD6NBFCZ8C4";

//     const {
//       data: [product],
//     } = useQueryGraphStep({
//       entity: "product",
//       fields: ["*", "tour.*", "tour.ticket.*"],
//       filters: {
//         id: target,
//       },
//     });

//     const result = {
//       tour: product.tour,
//       tour_ticket: product.tour?.ticket,
//     };

//     return new StepResponse(result);
//   }
// );
