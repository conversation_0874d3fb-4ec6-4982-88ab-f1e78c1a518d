import { createStep, StepResponse } from "@medusajs/framework/workflows-sdk";
import { TOUR_MODULE } from "src/modules/tours";
import TourService from "src/modules/tours/service";
import { LegacyTourFormBody } from "src/admin/components/tours/schemas";
import { logger } from "@medusajs/framework/logger";

type UpdateTourStepInput = {
  additional_data: LegacyTourFormBody & { tour_id: string };
};

export const updateTourStep = createStep(
  "update-tour",
  async (data: UpdateTourStepInput, { container }) => {
    const tourService: TourService = container.resolve(TOUR_MODULE);

    if (!data.additional_data) {
      return;
    }

    const {
      content_blocks,
      tour_seo,
      blocked_dates,
      pricing,
      food_options,
      ticket,
      start_times,
      blocked_dates_by_month,
      start_places_by_date,
      available_start_places,
      blocked_start_times,
      ...tourPayload
    } = data.additional_data;

    const prevTourData = await tourService.retrieveTour(
      data.additional_data.tour_id,
      {
        relations: [
          "content_blocks",
          "food_options",
          "blocked_dates",
          "start_times",
          "blocked_dates_by_month",
          "start_places_by_date",
          "available_start_places",
          "blocked_start_times",
        ],
      }
    );

    console.log({ prevTourData });

    // Update the main tour with recommended tour ids
    const tour = await tourService.updateTours({
      ...{ id: data.additional_data.tour_id },
      ...tourPayload,
    });

    // Now handle the content block separately
    let contentBlock;

    if (tour.content_blocks) {
      contentBlock = await tourService.updateTourContentBlocks({
        id: tour.content_blocks.id,
        ...content_blocks,
      });
    } else {
      contentBlock = await tourService.createTourContentBlocks({
        tour_id: tour.id,
        ...content_blocks,
      });
    }

    let tour_pricing;

    if (tour.pricing) {
      tour_pricing = await tourService.updateTourPricings({
        id: tour.pricing.id,
        ...pricing,
      });
    } else {
      tour_pricing = await tourService.updateTourPricings({
        tour_id: tour.id,
        ...pricing,
      });
    }

    let tour_start_times;

    if (tour.start_times) {
      tour_start_times = await tourService.updateTourStartTimes({
        id: tour.start_times.id,
        ...start_times,
      });
    } else {
      tour_start_times = await tourService.createTourStartTimes({
        tour_id: tour.id,
        ...start_times,
      });
    }

    let tour_ticket;

    if (tour.ticket) {
      tour_ticket = await tourService.updateTourTickets({
        id: tour.ticket.id,
        ...ticket,
      });
    } else {
      tour_ticket = await tourService.createTourTickets({
        tour_id: tour.id,
        ...ticket,
      });
    }

    let tourSeo;

    if (tour.tour_seo) {
      // @ts-ignore-next-line
      tourSeo = await tourService.updateTourSeos({
        id: tour.tour_seo.id,
        ...tour_seo,
      });
    } else {
      // @ts-ignore-next-line
      tourSeo = await tourService.createTourSeos({
        tour_id: tour.id,
        ...tour_seo,
      });
    }

    // Update food options
    const foodOptions = await updateRelatedEntities(
      tourService.createTourFoodOptions,
      tourService.updateTourFoodOptions,
      tourService.deleteTourFoodOptions,
      prevTourData.food_options,
      food_options,
      tour.id
    );

    // Update blocked dates
    const blockedDates = await updateRelatedEntities(
      tourService.createTourBlockedDates,
      tourService.updateTourBlockedDates,
      tourService.deleteTourBlockedDates,
      prevTourData.blocked_dates,
      blocked_dates,
      tour.id
    );

    const datesBlockedByMonth = await updateRelatedEntities(
      tourService.createTourBlockedDatesByMonths,
      tourService.updateTourBlockedDatesByMonths,
      tourService.deleteTourBlockedDatesByMonths,
      prevTourData.blocked_dates_by_month,
      blocked_dates_by_month,
      tour.id
    );

    const startPlacesByDate = await updateRelatedEntities(
      tourService.createTourStartPlacesByDates,
      tourService.updateTourStartPlacesByDates,
      tourService.deleteTourStartPlacesByDates,
      prevTourData.start_places_by_date || [],
      start_places_by_date || [],
      tour.id
    );

    const availableStartPlaces = await updateRelatedEntities(
      tourService.createTourAvailableStartPlaces,
      tourService.updateTourAvailableStartPlaces,
      tourService.deleteTourAvailableStartPlaces,
      prevTourData.available_start_places || [],
      available_start_places || [],
      tour.id
    );

    const blockedStartTimes = await updateRelatedEntities(
      tourService.createTourBlockedStartTimesByDates,
      tourService.updateTourBlockedStartTimesByDates,
      tourService.deleteTourBlockedStartTimesByDates,
      prevTourData.blocked_start_times || [],
      blocked_start_times || [],
      tour.id
    );

    const result = {
      tour,
      foodOptions,
      contentBlock,
      tour_pricing,
      tour_ticket,
      tour_start_times,
      blockedDates,
      tourSeo,
      datesBlockedByMonth,
      startPlacesByDate,
      availableStartPlaces,
      blockedStartTimes,
    };

    return new StepResponse(result, prevTourData);
  },

  async (prevTourData, { container }) => {
    const tourService: TourService = container.resolve(TOUR_MODULE);

    logger.info("Reverting to the previous state");
    // Revert to the previous state
    await tourService.updateTours({ id: prevTourData?.id }, prevTourData);
  }
);

const updateRelatedEntities = async <T extends { id?: string }>(
  createFunction: (data: Omit<T, "id"> & { tour_id: string }) => Promise<T>,
  updateFunction: (data: T & { tour_id: string }) => Promise<T>,
  deleteFunction: (id: string) => Promise<void>,
  existingEntities: T[],
  newEntities: T[],
  tourId: string
) => {
  const updatedEntities: T[] = [];
  const existingMap = new Map(existingEntities.map((e) => [e.id!, e]));

  for (const entity of newEntities) {
    if (entity.id && existingMap.has(entity.id)) {
      updatedEntities.push(
        await updateFunction({
          ...entity,
          tour_id: tourId,
        } as T & { tour_id: string })
      );
      existingMap.delete(entity.id);
    } else {
      const { id, ...entityWithoutId } = entity;
      updatedEntities.push(
        await createFunction({
          ...entityWithoutId,
          tour_id: tourId,
        } as Omit<T, "id"> & { tour_id: string })
      );
    }
  }

  for (const [id] of existingMap) {
    if (id) await deleteFunction(id);
  }

  return updatedEntities;
};
