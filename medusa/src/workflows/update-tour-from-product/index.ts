import {
  createWorkflow,
  transform,
  when,
  WorkflowResponse,
} from "@medusajs/framework/workflows-sdk";
import { ProductDTO } from "@medusajs/types";
import { createRemoteLinkStep, updateRemoteLinksStep } from "@medusajs/medusa/core-flows";
import { Modules } from "@medusajs/framework/utils";
import { TOUR_MODULE } from "src/modules/tours";
import { TUpdateTourPayload } from "src/admin/components/tours/schemas";
import { updateTourStep } from "./steps/update-tour";

export type UpdateTourFromProductWorkflowInput = {
  product: ProductDTO;
  additional_data: TUpdateTourPayload;
};

export const updateTourFromProductWorkflow = createWorkflow(
  "update-tour-from-product",
  (input: UpdateTourFromProductWorkflowInput) => {
    const value = transform({ input }, (data) => data.input.additional_data);

    
    const tour = updateTourStep({
      additional_data: value,
    });

    return new WorkflowResponse({
      tour,
    });
  }
);
