import {
  createWorkflow,
  WorkflowResponse,
} from "@medusajs/framework/workflows-sdk";
import { OrderDTO } from "@medusajs/types";
import { generateTicketPDFStep } from "./steps/generate-ticket-pdf-step";

export type OrderAdditionalData = {};

export type GenerateTicketPDFWorkflowInput = {
  order: Partial<OrderDTO>;
  additional_data: OrderAdditionalData;
  withPathSave?: boolean;
};

export const generateTicketPDFWorkflow = createWorkflow(
  "generate-ticket-pdf-workflow",
  (input: GenerateTicketPDFWorkflowInput) => {
    const { buffer } = generateTicketPDFStep({
      order_id: input.order.id as string,
      withPathSave: input.withPathSave,
    });

    return new WorkflowResponse({ result: buffer });
  }
);
