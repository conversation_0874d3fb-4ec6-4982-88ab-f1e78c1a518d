import { container } from "@medusajs/framework";
import { createStep, StepResponse } from "@medusajs/framework/workflows-sdk";
import { PDF_GENERATOR_MODULE } from "src/modules/pdf-generator";
import PdfGeneratorService from "src/modules/pdf-generator/service";
import {
  getTicketTemplate,
  TicketTemplateData,
} from "src/templates/ticket-templates/ticket";
import { TICKET_MODULE } from "src/modules/ticket";
import TicketService from "src/modules/ticket/service";
import { ContainerRegistrationKeys } from "@medusajs/framework/utils";
import { JsonProperty } from "@mikro-orm/core";
import { getTicketFileName } from "src/lib/utils";

type GenerateTicketPDFStepInput = {
  order_id: string;
  withPathSave?: boolean;
};

export const generateTicketPDFStep = createStep(
  "generate-ticket-pdf-step",
  async (input: GenerateTicketPDFStepInput) => {
    const pdfGenerator: PdfGeneratorService =
      container.resolve(PDF_GENERATOR_MODULE);

    const query = container.resolve(ContainerRegistrationKeys.QUERY);

    const { data: orders } = await query.graph({
      entity: "order",
      fields: ["customer.*", "ticket.*", "items.*", "*", "summary.*"],
      filters: {
        id: input.order_id,
      },
    });

    const order = orders[0];

    const templateData: TicketTemplateData = {
      ...order.ticket,
      customer_name:
        order.customer?.first_name + " " + order.customer?.last_name,
      customer_email: order.customer?.email ?? "",
      customer_phone: order.customer?.phone ?? "",
      items: order.items,
      total_to_pay: order.summary?.accounting_total ?? 0,
      amount_paid: order.summary?.transaction_total ?? 0,
      pending_difference: order.summary?.pending_difference ?? 0,
    };

    const buffer = await pdfGenerator.generatePDFfromHTML(
      getTicketTemplate(templateData),
      input.withPathSave
        ? `./static/tickets/${getTicketFileName(input.order_id, order.customer?.last_name ?? null)}`
        : undefined
    );

    return new StepResponse({ buffer });
  }
);
