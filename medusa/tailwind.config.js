/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [],
  theme: {
    extend: {
      colors: {
        "ui-bg": {
          base: "rgba(255, 255, 255, 1)",
          "base-hover": "rgba(244, 244, 245, 1)",
          "base-pressed": "rgba(228, 228, 231, 1)",
          component: "rgba(250, 250, 250, 1)",
          "component-hover": "rgba(244, 244, 245, 1)",
          "component-pressed": "rgba(228, 228, 231, 1)",
          disabled: "rgba(244, 244, 245, 1)",
          field: "rgba(250, 250, 250, 1)",
          "field-component": "rgba(255, 255, 255, 1)",
          "field-component-hover": "rgba(250, 250, 250, 1)",
          "field-hover": "rgba(244, 244, 245, 1)",
          highlight: "rgba(239, 246, 255, 1)",
          "highlight-hover": "rgba(219, 234, 254, 1)",
          interactive: "rgba(59, 130, 246, 1)",
          overlay: "rgba(24, 24, 27, 0.4)",
          subtle: "rgba(250, 250, 250, 1)",
          "subtle-hover": "rgba(244, 244, 245, 1)",
          "subtle-pressed": "rgba(228, 228, 231, 1)",
          "switch-off": "rgba(228, 228, 231, 1)",
          "switch-off-hover": "rgba(212, 212, 216, 1)",
        },
        "ui-fg": {
          base: "rgba(24, 24, 27, 1)",
          disabled: "rgba(161, 161, 170, 1)",
          error: "rgba(225, 29, 72, 1)",
          interactive: "rgba(59, 130, 246, 1)",
          "interactive-hover": "rgba(37, 99, 235, 1)",
          muted: "rgba(113, 113, 122, 1)",
          "on-color": "rgba(255, 255, 255, 1)",
          "on-inverted": "rgba(255, 255, 255, 1)",
          subtle: "rgba(82, 82, 91, 1)",
        },
        "ui-border": {
          base: "rgba(228, 228, 231, 1)",
          danger: "rgba(190, 18, 60, 1)",
          error: "rgba(225, 29, 72, 1)",
          interactive: "rgba(59, 130, 246, 1)",
          "menu-bot": "rgba(255, 255, 255, 1)",
          "menu-top": "rgba(228, 228, 231, 1)",
          strong: "rgba(212, 212, 216, 1)",
          transparent: "rgba(255, 255, 255, 0)",
        },
        "ui-button": {
          danger: "rgba(225, 29, 72, 1)",
          "danger-hover": "rgba(190, 18, 60, 1)",
          "danger-pressed": "rgba(159, 18, 57, 1)",
          inverted: "rgba(39, 39, 42, 1)",
          "inverted-hover": "rgba(63, 63, 70, 1)",
          "inverted-pressed": "rgba(82, 82, 91, 1)",
          neutral: "rgba(255, 255, 255, 1)",
          "neutral-hover": "rgba(244, 244, 245, 1)",
          "neutral-pressed": "rgba(228, 228, 231, 1)",
          transparent: "rgba(255, 255, 255, 0)",
          "transparent-hover": "rgba(244, 244, 245, 1)",
          "transparent-pressed": "rgba(228, 228, 231, 1)",
        },
        "ui-tag": {
          "blue-bg": "rgba(219, 234, 254, 1)",
          "blue-bg-hover": "rgba(191, 219, 254, 1)",
          "blue-border": "rgba(191, 219, 254, 1)",
          "blue-icon": "rgba(96, 165, 250, 1)",
          "blue-text": "rgba(30, 64, 175, 1)",
          "green-bg": "rgba(209, 250, 229, 1)",
          "green-bg-hover": "rgba(167, 243, 208, 1)",
          "green-border": "rgba(167, 243, 208, 1)",
          "green-icon": "rgba(16, 185, 129, 1)",
          "green-text": "rgba(6, 95, 70, 1)",
          "neutral-bg": "rgba(244, 244, 245, 1)",
          "neutral-bg-hover": "rgba(228, 228, 231, 1)",
          "neutral-border": "rgba(228, 228, 231, 1)",
          "neutral-icon": "rgba(161, 161, 170, 1)",
          "neutral-text": "rgba(82, 82, 91, 1)",
          "orange-bg": "rgba(255, 237, 213, 1)",
          "orange-bg-hover": "rgba(254, 215, 170, 1)",
          "orange-border": "rgba(254, 215, 170, 1)",
          "orange-icon": "rgba(249, 115, 22, 1)",
          "orange-text": "rgba(154, 52, 18, 1)",
          "purple-bg": "rgba(237, 233, 254, 1)",
          "purple-bg-hover": "rgba(221, 214, 254, 1)",
          "purple-border": "rgba(221, 214, 254, 1)",
          "purple-icon": "rgba(167, 139, 250, 1)",
          "purple-text": "rgba(91, 33, 182, 1)",
          "red-bg": "rgba(255, 228, 230, 1)",
          "red-bg-hover": "rgba(254, 205, 211, 1)",
          "red-border": "rgba(254, 205, 211, 1)",
          "red-icon": "rgba(244, 63, 94, 1)",
          "red-text": "rgba(159, 18, 57, 1)",
        },
      },
    },
  },
  plugins: [],
};
