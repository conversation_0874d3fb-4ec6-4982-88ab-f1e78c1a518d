{"docwriter.hotkey.mac": "⌥ + .", "typescript.tsserver.maxTsServerMemory": 8192, "typescript.tsserver.watchOptions": {"watchFile": "useFsEvents", "watchDirectory": "useFsEvents", "fallbackPolling": "dynamicPriorityPolling"}, "files.watcherExclude": {"**/.git/objects/**": true, "**/.git/subtree-cache/**": true, "**/node_modules/**": true, "**/.hg/store/**": true, "**/dist/**": true, "**/.medusa/**": true, "**/.cache/**": true, "**/coverage/**": true, "**/temp/**": true}, "search.exclude": {"**/node_modules": true, "**/bower_components": true, "**/*.code-search": true, "**/dist": true, "**/.medusa": true, "**/.cache": true}, "typescript.disableAutomaticTypeAcquisition": true, "typescript.tsserver.experimental.enableProjectDiagnostics": false}