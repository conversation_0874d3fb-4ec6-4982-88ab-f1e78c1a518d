ENV_TARGET=development # development || production

# example http://localhost:9000 || http://medusa.api.com
MEDUSA_API_ENDPOINT_URL=http://localhost:9000
MEDUSA_STOREFRONT_URL=http://localhost:8000
# REMEMBER TO INCLUDE BOTH MEDUSA_API_ENDPOINT_URL AND STOREFRONT_URL IN CORESPONDING CORS ENV VARIABLES
STORE_CORS=http://localhost:8000,https://docs.medusajs.com
ADMIN_CORS=http://localhost:5173,http://localhost:9000,https://docs.medusajs.com,http://localhost:8000,http://localhost:3000
AUTH_CORS=http://localhost:5173,http://localhost:9000,https://docs.medusajs.com,http://localhost:8000,http://localhost:3000
REDIS_URL=redis://localhost:6379
JWT_SECRET=supersecret
COOKIE_SECRET=supersecret
DATABASE_URL=postgresql://<USER>:<PASSWORD>@localhost:5432/wakacyjne

DB_NAME=wakacyjne

# tpay
CLIEND_ID=
TPAY_CLIENT_SECRET=
TPAY_NOTIFICATION_EMAIL=

#smtp
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM=<EMAIL>

