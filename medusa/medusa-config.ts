import { loadEnv, defineConfig } from "@medusajs/framework/utils";
import { config } from "./src/config";
loadEnv(process.env.NODE_ENV || "development", process.cwd());

module.exports = defineConfig({
  admin: {
    backendUrl: config.MEDUSA_API_ENDPOINT_URL,
    storefrontUrl: config.MEDUSA_STOREFRONT_URL,
  },
  projectConfig: {
    databaseUrl: process.env.DATABASE_URL,
    ...(process.env.ENV_TARGET === "development" && {
      redisUrl: process.env.REDIS_URL,
      sessionOptions: {
        resave: false,
        saveUninitialized: false,
      },
    }),
    http: {
      storeCors: process.env.STORE_CORS!,
      adminCors: process.env.ADMIN_CORS!,
      authCors: process.env.AUTH_CORS!,
      jwtSecret: process.env.JWT_SECRET || "supersecret",
      cookieSecret: process.env.COOKIE_SECRET || "supersecret",
    },
  },
  modules: [
    {
      resolve: "./modules/guides",
    },
    {
      resolve: "./modules/tours",
    },
    {
      resolve: "./modules/pdf-generator",
    },
    {
      resolve: "./modules/ticket",
    },
    {
      resolve: "./modules/categories",
    },
    {
      resolve: "./modules/exchange-rate",
    },
    {
      resolve: "./modules/report",
    },
    {
      resolve: "./modules/redirections",
    },
    {
      resolve: "./modules/order-summary",
    },
    {
      resolve: "./modules/refunds",
    },
    {
      resolve: "./modules/order-transaction",
    },
    {
      resolve: "@medusajs/medusa/fulfillment",
      options: {
        providers: [
          {
            resolve: "./src/modules/tickets-fulfillment",
            id: "digital_ticket",
          },
        ],
      },
    },
    {
      resolve: "@medusajs/medusa/notification",
      options: {
        providers: [
          {
            resolve: "@medusajs/medusa/notification-local",
            id: "local",
            options: {
              name: "Local Notification Provider",
              channels: ["feed"],
            },
          },
          {
            resolve: "./src/modules/smtp",
            id: "smtp",
            options: {
              channels: ["email"],
            },
          },
        ],
      },
    },
    {
      resolve: "@medusajs/medusa/payment",
      options: {
        providers: [
          {
            resolve: "./src/modules/tpay",
            id: "tpay",
            options: {
              clientId: process.env.TPAY_CLIENT_ID,
              clientSecret: process.env.TPAY_CLIENT_SECRET,
            },
          },
        ],
      },
    },
    {
      resolve: "@medusajs/medusa/file",
      options: {
        providers: [
          {
            resolve: "@medusajs/medusa/file-local",
            id: "local",
            options: {
              backend_url: process.env.MEDUSA_API_ENDPOINT_URL + "/static",
            },
          },
        ],
      },
    },
    {
      resolve: "@medusajs/order",
    },

    {
      resolve: "@medusajs/medusa/cache-redis",
      options: {
        redisUrl: process.env.REDIS_URL,
      },
    },
  ],
});
