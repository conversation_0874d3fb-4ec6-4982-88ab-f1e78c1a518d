{"name": "medusa-starter-default", "version": "0.0.1", "description": "A starter for Medusa projects.", "author": "Medusa (https://medusajs.com)", "license": "MIT", "keywords": ["sqlite", "postgres", "typescript", "ecommerce", "headless", "medusa"], "scripts": {"build": "medusa build", "seed": "medusa exec ./src/scripts/seed.ts", "start": "medusa start", "dev": "medusa develop", "dev-migrate": "medusa db:generate tourModule && medusa db:migrate", "production": "medusa build && cd .medusa/server && cp ../../.env .env.production && export NODE_ENV=production && npm run start", "test:integration:http": "TEST_TYPE=integration:http NODE_OPTIONS=--experimental-vm-modules jest --silent=false --runInBand --forceExit", "test:integration:modules": "TEST_TYPE=integration:modules NODE_OPTIONS=--experimental-vm-modules jest --silent --runInBand --forceExit", "test:unit": "TEST_TYPE=unit NODE_OPTIONS=--experimental-vm-modules jest --silent --runInBand --forceExit"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.9.0", "@medusajs/admin-sdk": "2.2.0", "@medusajs/cli": "2.2.0", "@medusajs/file-local": "^2.2.0", "@medusajs/framework": "2.2.0", "@medusajs/icons": "^2.2.0", "@medusajs/js-sdk": "^2.2.0", "@medusajs/medusa": "2.2.0", "@medusajs/types": "^2.2.0", "@medusajs/ui": "^4.0.3", "@mikro-orm/core": "5.9.7", "@mikro-orm/knex": "5.9.7", "@mikro-orm/migrations": "5.9.7", "@mikro-orm/postgresql": "5.9.7", "@tanstack/react-query": "^5.63.0", "@tiptap/extension-color": "^2.11.5", "@tiptap/extension-dropcursor": "^2.11.2", "@tiptap/extension-image": "3.0.0-next.4", "@tiptap/extension-link": "^2.11.2", "@tiptap/extension-text-style": "^2.11.5", "@tiptap/react": "^2.11.1", "@tiptap/starter-kit": "^2.11.1", "awilix": "^8.0.1", "cors": "^2.8.5", "install": "^0.13.0", "nodemailer": "^6.10.0", "pg": "^8.13.0", "puppeteer": "^24.4.0", "react-day-picker": "^9.5.1", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.2", "zod": "^3.23.8"}, "devDependencies": {"@medusajs/test-utils": "2.2.0", "@mikro-orm/cli": "5.9.7", "@swc/core": "1.5.7", "@swc/jest": "^0.2.36", "@types/jest": "^29.5.13", "@types/node": "^20.0.0", "@types/nodemailer": "^6.4.17", "@types/react": "^18.3.2", "@types/react-dom": "^18.2.25", "jest": "^29.7.0", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "ts-node": "^10.9.2", "typescript": "^5.6.2", "vite": "^5.2.11"}, "engines": {"node": ">=20"}}