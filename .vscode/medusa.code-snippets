{"workflow": {"scope": "javascript,typescript,javascriptreact,typescriptreact", "prefix": "workflow", "body": ["// @prefix workflow-index", "// @description Create a new workflow", "", "import {", "  createWorkflow,", "  WorkflowResponse,", "} from \"@medusajs/framework/workflows-sdk\";", "", "type $3 = {};", "", "export const $0 = createWorkflow(\"$1\", (input: $3) => {", "  return new WorkflowResponse({});", "});", ""]}, "step-workflow": {"scope": "javascript,typescript,javascriptreact,typescriptreact", "prefix": "step-workflow", "body": ["import { ContainerRegistrationKeys } from \"@medusajs/framework/utils\";", "import { createStep, StepResponse } from \"@medusajs/framework/workflows-sdk\";", "", "type $2 = {", "  email: string,", "};", "", "export const $0 = createStep(\"$1\", async (input: $2, { container }) => {", "  const query = container.resolve(ContainerRegistrationKeys.QUERY);", "", "  const customer = await query.graph({", "    entity: \"customer\",", "    filters: {", "      email: input.email,", "    },", "  });", "", "  if (customer) {", "    return new StepResponse({ customer });", "  }", "", "  return new StepResponse({});", "});", ""]}}